#!/usr/bin/env python3
"""
Test script to find which message types the WhatsApp service accepts
"""

import requests
import json

def test_message_types():
    """Test different message type values"""
    
    url = "http://**************:3001/instances/8487921219/send"
    headers = {
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIm1vYmlsZU51bWJlciI6IjkxOTk5OTk5OTk5OSIsInJvbGUiOiJhZG1pbiIsImNvcnBvcmF0ZUlkIjpudWxsLCJpYXQiOjE3NTAzMjczNzcsImV4cCI6MTc1MDQxMzc3N30.g6kPgoL3El8ajTB9F6PM6EhBCeI8PDQSvOC-Y54Des8",
        "Content-Type": "application/json"
    }
    
    phone_number = "<EMAIL>"
    
    # Test different message types
    message_types_to_test = [
        'text',
        'image', 
        'video',
        'audio',
        'document',
        'file',
        'media',
        'photo',
        'picture'
    ]
    
    print("🧪 Testing Message Types Acceptance:")
    print("=" * 50)
    
    # First test text to make sure API works
    text_data = {
        "to": phone_number,
        "type": "text",
        "message": "Testing message types"
    }
    
    try:
        response = requests.post(url, headers=headers, json=text_data, timeout=10)
        if response.status_code in [200, 201]:
            print("✅ Text message works - API is functional")
        else:
            print(f"❌ Text message failed: {response.status_code}")
            print("API may be down - stopping tests")
            return
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return
    
    # Create a minimal test image (1x1 pixel PNG)
    test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    print("\n📸 Testing Media Message Types:")
    print("-" * 30)
    
    working_types = []
    
    for msg_type in message_types_to_test:
        if msg_type == 'text':
            continue  # Already tested
            
        print(f"\n🧪 Testing type: '{msg_type}'")
        
        # Test with media data
        test_data = {
            "to": phone_number,
            "type": msg_type,
            "media": f"data:image/png;base64,{test_image_b64}",
            "filename": "test.png"
        }
        
        try:
            response = requests.post(url, headers=headers, json=test_data, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                try:
                    result = response.json()
                    if (result.get('success') or result.get('status') == 'success' or 
                        'messageId' in result or 'id' in result):
                        print("✅ SUCCESS: This message type works!")
                        working_types.append(msg_type)
                    else:
                        print(f"❌ API Error: {result.get('error', 'Unknown error')}")
                except:
                    print("✅ SUCCESS: Got 200 response (may have worked)")
                    working_types.append(f"{msg_type} (non-JSON)")
            elif response.status_code == 400:
                try:
                    result = response.json()
                    error_msg = result.get('error', response.text)
                    if 'unsupported' in error_msg.lower() or 'invalid' in error_msg.lower():
                        print(f"❌ UNSUPPORTED: {error_msg}")
                    else:
                        print(f"❌ BAD REQUEST: {error_msg}")
                except:
                    print(f"❌ BAD REQUEST: {response.text[:100]}")
            else:
                print(f"❌ HTTP ERROR: {response.status_code}")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    print("\n" + "=" * 50)
    print("📋 RESULTS:")
    
    if working_types:
        print("✅ Working message types:")
        for msg_type in working_types:
            print(f"  • {msg_type}")
        print(f"\n💡 Use one of these types in Odoo: {working_types[0]}")
    else:
        print("❌ No working message types found for media")
        print("💡 This WhatsApp service may not support media sending")
        print("💡 Or it may require a completely different API structure")
    
    return working_types

def test_alternative_structures():
    """Test alternative data structures for media"""
    
    url = "http://**************:3001/instances/8487921219/send"
    headers = {
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIm1vYmlsZU51bWJlciI6IjkxOTk5OTk5OTk5OSIsInJvbGUiOiJhZG1pbiIsImNvcnBvcmF0ZUlkIjpudWxsLCJpYXQiOjE3NTAzMjczNzcsImV4cCI6MTc1MDQxMzc3N30.g6kPgoL3El8ajTB9F6PM6EhBCeI8PDQSvOC-Y54Des8",
        "Content-Type": "application/json"
    }
    
    phone_number = "<EMAIL>"
    test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    print("\n🔧 Testing Alternative Data Structures:")
    print("=" * 50)
    
    # Test different data structures
    test_structures = [
        {
            "name": "Structure 1: No type field",
            "data": {
                "to": phone_number,
                "media": f"data:image/png;base64,{test_image_b64}",
                "filename": "test.png"
            }
        },
        {
            "name": "Structure 2: File field",
            "data": {
                "to": phone_number,
                "file": f"data:image/png;base64,{test_image_b64}",
                "filename": "test.png"
            }
        },
        {
            "name": "Structure 3: Base64 field",
            "data": {
                "to": phone_number,
                "base64": test_image_b64,
                "filename": "test.png",
                "mimetype": "image/png"
            }
        }
    ]
    
    working_structures = []
    
    for structure in test_structures:
        print(f"\n🧪 {structure['name']}")
        print(f"Fields: {list(structure['data'].keys())}")
        
        try:
            response = requests.post(url, headers=headers, json=structure['data'], timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                try:
                    result = response.json()
                    if (result.get('success') or 'messageId' in result):
                        print("✅ SUCCESS: This structure works!")
                        working_structures.append(structure['name'])
                    else:
                        print(f"❌ API Error: {result}")
                except:
                    print("✅ SUCCESS: Got 200 response")
                    working_structures.append(structure['name'])
            else:
                print(f"❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    if working_structures:
        print(f"\n✅ Working structures: {working_structures}")
    else:
        print("\n❌ No working structures found")

def main():
    """Run all tests"""
    print("🚀 WhatsApp Message Type Testing")
    print("=" * 60)
    
    # Test 1: Message types
    working_types = test_message_types()
    
    # Test 2: Alternative structures
    test_alternative_structures()
    
    print("\n" + "=" * 60)
    print("📝 RECOMMENDATIONS:")
    
    if working_types:
        print(f"1. Update Odoo to use message type: '{working_types[0]}'")
        print("2. Test the working type with actual files")
    else:
        print("1. This WhatsApp service may not support media sending")
        print("2. Consider using text messages with file download links")
        print("3. Check WhatsApp service documentation for media support")

if __name__ == "__main__":
    main()
