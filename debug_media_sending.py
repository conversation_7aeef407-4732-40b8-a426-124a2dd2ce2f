#!/usr/bin/env python3
"""
Debug script to test media sending functionality
"""

import requests
import json
import base64

def test_media_sending_debug():
    """Test media sending with detailed debugging"""

    url = "http://**************:3001/instances/8487921219/send"
    headers = {
        "Content-Type": "application/json"
    }
    
    phone_number = "<EMAIL>"
    
    # Create a simple test image (1x1 pixel PNG in base64)
    test_image_b64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    filename = "test_image.png"
    mimetype = "image/png"
    
    print("🧪 Testing Media Sending Formats")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Phone: {phone_number}")
    print(f"File: {filename}")
    print(f"MIME: {mimetype}")
    print(f"Size: {len(test_image_b64)} chars")
    print("=" * 50)
    
    # Test the exact formats that Odoo will try (UPDATED WITH CORRECT FORMAT)
    formats = [
        {
            "name": "Format 1: WhatsApp Service Expected Format (CORRECT)",
            "data": {
                'to': phone_number,
                'type': 'media',  # ✅ Correct type
                'media': {
                    'data': test_image_b64,  # ✅ Raw base64, no data: prefix
                    'filename': filename,
                    'mimetype': mimetype
                },
                'message': 'Test image from debug script'  # ✅ Use 'message' not 'caption'
            }
        },
        {
            "name": "Format 2: Direct Type Field Format",
            "data": {
                'to': phone_number,
                'type': 'image',
                'image': f"data:{mimetype};base64,{test_image_b64}",
                'filename': filename,
                'caption': 'Test image from debug script'
            }
        },
        {
            "name": "Format 3: File Field Format",
            "data": {
                'to': phone_number,
                'type': 'image',
                'file': f"data:{mimetype};base64,{test_image_b64}",
                'filename': filename,
                'caption': 'Test image from debug script'
            }
        },
        {
            "name": "Format 4: Generic Media Type",
            "data": {
                'to': phone_number,
                'type': 'media',
                'media': f"data:{mimetype};base64,{test_image_b64}",
                'filename': filename,
                'mimetype': mimetype,
                'caption': 'Test image from debug script'
            }
        },
        {
            "name": "Format 5: No Type Field Format",
            "data": {
                'to': phone_number,
                'media': f"data:{mimetype};base64,{test_image_b64}",
                'filename': filename,
                'mimetype': mimetype,
                'message': 'Test image from debug script'
            }
        },
        {
            "name": "Format 6: Raw Base64 Format",
            "data": {
                'to': phone_number,
                'type': 'image',
                'media': test_image_b64,
                'filename': filename,
                'mimetype': mimetype,
                'caption': 'Test image from debug script'
            }
        }
    ]
    
    working_formats = []
    
    for format_test in formats:
        print(f"\n🧪 {format_test['name']}")
        print(f"Data keys: {list(format_test['data'].keys())}")
        
        try:
            response = requests.post(url, headers=headers, json=format_test['data'], timeout=15)
            print(f"Status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                try:
                    result = response.json()
                    print(f"Response: {json.dumps(result, indent=2)}")
                    
                    # Check for success indicators
                    is_success = (
                        result.get('success') == True or
                        result.get('status') == 'success' or
                        'messageId' in result or
                        'id' in result or
                        (result.get('error') is None and response.status_code == 200)
                    )
                    
                    if is_success:
                        print("✅ SUCCESS: This format works!")
                        working_formats.append(format_test['name'])
                    else:
                        print(f"❌ API Error: {result.get('error', 'Unknown error')}")
                        
                except Exception as json_e:
                    print(f"Response (non-JSON): {response.text[:200]}...")
                    if response.status_code == 200:
                        print("✅ SUCCESS: Got 200 response (may have worked)")
                        working_formats.append(f"{format_test['name']} (non-JSON)")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                try:
                    error_response = response.json()
                    print(f"Error details: {json.dumps(error_response, indent=2)}")
                except:
                    print(f"Error text: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"❌ Request Error: {e}")
    
    print("\n" + "=" * 50)
    print("📋 RESULTS:")
    
    if working_formats:
        print("✅ Working formats found:")
        for fmt in working_formats:
            print(f"  • {fmt}")
        print(f"\n💡 Odoo should use: {working_formats[0]}")
    else:
        print("❌ No working formats found")
        print("💡 This WhatsApp service may not support media sending")
    
    return working_formats

def test_text_message():
    """Test that text messages work"""

    url = "http://**************:3001/instances/8487921219/send"
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "to": "<EMAIL>",
        "type": "text",
        "message": "Test text message from debug script"
    }
    
    print("\n📝 Testing Text Message:")
    print("-" * 30)
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✅ Text messages work")
            return True
        else:
            print("❌ Text messages failed")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 WhatsApp Media Sending Debug")
    print("=" * 60)
    
    # Test 1: Text message baseline
    text_works = test_text_message()
    
    if not text_works:
        print("\n❌ Text messages don't work - API may be down")
        return
    
    # Test 2: Media formats
    working_formats = test_media_sending_debug()
    
    print("\n" + "=" * 60)
    print("📝 CONCLUSIONS:")
    
    if working_formats:
        print("✅ Media sending should work with these formats")
        print("✅ The issue in Odoo may be:")
        print("  • Attachment data not being read correctly")
        print("  • Message type detection issue")
        print("  • Logic flow problem")
    else:
        print("❌ Media sending doesn't work with any format")
        print("❌ The WhatsApp service may not support media")
    
    print("\n📋 Next Steps:")
    print("1. Check Odoo logs for detailed error messages")
    print("2. Verify attachment data is being read correctly")
    print("3. Ensure message type is set to 'image' not 'text'")

if __name__ == "__main__":
    main()
