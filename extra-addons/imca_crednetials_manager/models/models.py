from odoo import models, fields, api


class CrednetialsManager(models.Model):
    _name = 'x_credentials'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Crednetials Manager'

    name = fields.Char(string='Name', store=True)
    x_partner_id = fields.Many2one('res.partner', string='Client Ref', store=True)
    x_password = fields.Char(string='Password', store=True)
    x_type = fields.Char(string='Type', store=True)
    x_url = fields.Char(string='URL', store=True)
    x_username = fields.Char(string='Username', store=True)