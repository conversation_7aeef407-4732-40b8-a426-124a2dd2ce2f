#!/usr/bin/env python3
"""
Check what QR code is currently being served
"""

import requests
import base64
from PIL import Image
from io import BytesIO
import sys

def check_current_qr():
    """Check the current QR code at the URL"""
    try:
        print("🔍 Checking current QR code at https://arihantai.com/whatsapp/qr/1")
        
        # Download the QR code
        response = requests.get('https://arihantai.com/whatsapp/qr/1', timeout=10)
        
        if response.status_code == 200:
            print(f"✅ QR code downloaded successfully")
            print(f"📊 Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
            print(f"📏 Content-Length: {len(response.content)} bytes")
            
            # Save the current QR code
            with open('/mnt/extra-addons/current_qr.png', 'wb') as f:
                f.write(response.content)
            
            print(f"💾 QR code saved to: /mnt/extra-addons/current_qr.png")
            
            # Try to decode it
            try:
                from pyzbar import pyzbar
                
                # Load and decode
                img = Image.open(BytesIO(response.content))
                qr_codes = pyzbar.decode(img)
                
                if qr_codes:
                    for qr in qr_codes:
                        qr_data = qr.data.decode('utf-8')
                        print("\n" + "="*60)
                        print("🔍 CURRENT QR CODE CONTENT:")
                        print("="*60)
                        print(qr_data)
                        print("="*60)
                        
                        # Analyze the content
                        if qr_data.startswith('https://web.whatsapp.com'):
                            print("✅ STATUS: REAL WhatsApp Web QR Code!")
                            print("🎯 TYPE: Actual WhatsApp Web URL")
                            print("📱 SCANNABLE: Yes, by WhatsApp mobile app")
                        elif 'WhatsApp Web QR Code Test' in qr_data:
                            print("⚠️  STATUS: Test QR Code")
                            print("🎯 TYPE: System verification QR")
                            print("📱 SCANNABLE: Yes, but shows test message")
                        elif 'REAL WHATSAPP QR CODE' in qr_data:
                            print("⚠️  STATUS: Informational QR Code")
                            print("🎯 TYPE: Contains mudslide capture info")
                            print("📱 SCANNABLE: Yes, but shows info message")
                        else:
                            print("❓ STATUS: Unknown QR Code type")
                            print("🎯 TYPE: Custom content")
                        
                        return qr_data
                else:
                    print("❌ No QR code found in the image")
                    return None
                    
            except ImportError:
                print("⚠️  pyzbar not available - cannot decode QR content")
                print("💡 Install with: pip3 install pyzbar")
                return "QR_DOWNLOADED_BUT_NOT_DECODED"
            except Exception as e:
                print(f"❌ Error decoding QR: {e}")
                return None
                
        else:
            print(f"❌ Failed to download QR code: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error checking QR code: {e}")
        return None

def main():
    print("🔍 QR Code Content Checker")
    print("=" * 40)
    
    qr_content = check_current_qr()
    
    if qr_content:
        print(f"\n📋 SUMMARY:")
        if qr_content.startswith('https://web.whatsapp.com'):
            print("🎉 SUCCESS: Real WhatsApp QR code detected!")
            print("✅ Ready for WhatsApp mobile app scanning")
        else:
            print("⚠️  NOTICE: Not a real WhatsApp Web URL")
            print("🔧 Need to generate new QR code using improved method")
    else:
        print(f"\n❌ FAILED: Could not analyze QR code content")
    
    print(f"\n💡 NEXT STEPS:")
    print("1. Check the downloaded QR at: /mnt/extra-addons/current_qr.png")
    print("2. If not real WhatsApp URL, click 'Generate Real WhatsApp QR' in Odoo")
    print("3. Monitor logs with: sudo journalctl -u odoo -f")

if __name__ == '__main__':
    main()
