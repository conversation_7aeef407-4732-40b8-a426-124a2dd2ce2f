#!/usr/bin/env python3
"""
Test the current QR implementation to see what's actually happening
"""

import subprocess
import time
import os
import sys

def test_mudslide_direct():
    """Test mudslide directly to see what it outputs"""
    print("🧪 TEST 1: Direct mudslide command")
    print("=" * 50)
    
    try:
        # Check if mudslide is available
        result = subprocess.run(['which', 'mudslide'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ mudslide not found")
            return False
        
        print(f"✅ mudslide found at: {result.stdout.strip()}")
        
        # Test mudslide version
        version_result = subprocess.run(['mudslide', '--version'], capture_output=True, text=True)
        if version_result.returncode == 0:
            print(f"✅ mudslide version: {version_result.stdout.strip()}")
        
        # Run mudslide login with timeout
        print("🔄 Running mudslide login (10 second timeout)...")
        
        process = subprocess.Popen(
            ['mudslide', 'login'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd='/tmp'
        )
        
        # Read output for 10 seconds
        start_time = time.time()
        output_lines = []
        
        while time.time() - start_time < 10:
            if process.poll() is not None:
                break
                
            try:
                line = process.stdout.readline()
                if line:
                    output_lines.append(line.strip())
                    print(f"📝 {line.strip()}")
                    
                    # Look for QR code indicators
                    if '█' in line or '▄' in line or '▀' in line:
                        print("🎯 QR CODE CHARACTERS DETECTED!")
                        
            except:
                break
                
            time.sleep(0.1)
        
        # Kill process
        if process.poll() is None:
            process.terminate()
            time.sleep(1)
            if process.poll() is None:
                process.kill()
        
        print(f"📊 Total output lines: {len(output_lines)}")
        return len(output_lines) > 0
        
    except Exception as e:
        print(f"❌ Error testing mudslide: {e}")
        return False

def test_node_dependencies():
    """Test if Node.js dependencies are available"""
    print("\n🧪 TEST 2: Node.js dependencies")
    print("=" * 50)
    
    try:
        # Check Node.js version
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js version: {result.stdout.strip()}")
        else:
            print("❌ Node.js not found")
            return False
        
        # Check if Baileys is available
        test_script = '''
try {
    const baileys = require('@whiskeysockets/baileys');
    console.log('✅ Baileys library found');
} catch(e) {
    console.log('❌ Baileys library missing:', e.message);
}
'''
        
        result = subprocess.run(['node', '-e', test_script], capture_output=True, text=True)
        print(result.stdout.strip())
        
        return '✅' in result.stdout
        
    except Exception as e:
        print(f"❌ Error testing Node.js: {e}")
        return False

def test_current_qr_file():
    """Test the current QR file"""
    print("\n🧪 TEST 3: Current QR file analysis")
    print("=" * 50)
    
    qr_file = '/mnt/extra-addons/1.png'
    
    if not os.path.exists(qr_file):
        print(f"❌ QR file not found: {qr_file}")
        return False
    
    print(f"✅ QR file found: {qr_file}")
    
    # Check file size
    file_size = os.path.getsize(qr_file)
    print(f"📏 File size: {file_size} bytes")
    
    # Try to decode with simple method
    try:
        from PIL import Image
        img = Image.open(qr_file)
        print(f"✅ Image loaded: {img.size} pixels, mode: {img.mode}")
        
        # Try to decode QR
        try:
            from pyzbar import pyzbar
            qr_codes = pyzbar.decode(img)
            
            if qr_codes:
                qr_data = qr_codes[0].data.decode('utf-8')
                print(f"✅ QR decoded successfully")
                print(f"📏 Content length: {len(qr_data)} characters")
                
                # Show first 100 characters
                preview = qr_data[:100] + "..." if len(qr_data) > 100 else qr_data
                print(f"📝 Content preview: {preview}")
                
                # Check if it's a WhatsApp URL
                if qr_data.startswith('https://web.whatsapp.com'):
                    print("🎉 REAL WHATSAPP WEB URL DETECTED!")
                    return True
                else:
                    print("⚠️  Not a WhatsApp Web URL - contains descriptive text")
                    return False
            else:
                print("❌ No QR code found in image")
                return False
                
        except ImportError:
            print("⚠️  pyzbar not available - cannot decode QR content")
            return None
            
    except Exception as e:
        print(f"❌ Error analyzing QR file: {e}")
        return False

def main():
    print("🔍 COMPREHENSIVE QR IMPLEMENTATION TEST")
    print("=" * 60)
    
    # Run all tests
    mudslide_ok = test_mudslide_direct()
    node_ok = test_node_dependencies()
    qr_ok = test_current_qr_file()
    
    print("\n📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"🔧 Mudslide working: {'✅' if mudslide_ok else '❌'}")
    print(f"📦 Node.js deps ready: {'✅' if node_ok else '❌'}")
    print(f"📱 Current QR is real WhatsApp: {'✅' if qr_ok else '❌' if qr_ok is not None else '❓'}")
    
    if qr_ok:
        print("\n🎉 SUCCESS: Current QR code contains real WhatsApp Web URL!")
        print("✅ Ready for WhatsApp mobile app scanning")
    elif qr_ok is False:
        print("\n⚠️  ISSUE: Current QR code contains descriptive text, not WhatsApp URL")
        print("🔧 Need to generate new QR code with improved method")
    else:
        print("\n❓ UNKNOWN: Could not analyze QR code content")
    
    print(f"\n💡 RECOMMENDATIONS:")
    if not mudslide_ok:
        print("- Install mudslide: npm install -g mudslide")
    if not node_ok:
        print("- Install Node.js dependencies: npm install -g @whiskeysockets/baileys @hapi/boom")
    if not qr_ok:
        print("- Generate new QR code using 'Generate Real WhatsApp QR' button in Odoo")

if __name__ == '__main__':
    main()
