#!/usr/bin/env python3
"""
Test the mudslide wrapper to verify it captures QR data properly
"""

import subprocess
import time
import os
import tempfile

def test_mudslide_wrapper():
    """Test the mudslide wrapper script"""
    print("🧪 TESTING MUDSLIDE WRAPPER")
    print("=" * 50)
    
    # Create temporary directory
    test_dir = tempfile.mkdtemp(prefix='mudslide_test_')
    print(f"📁 Test directory: {test_dir}")
    
    try:
        # Create the wrapper script
        wrapper_content = '''
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting mudslide wrapper...');

// Create mudslide process
const mudslide = spawn('mudslide', ['login'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: process.cwd()
});

let qrCaptured = false;
let qrLines = [];

// Capture stdout
mudslide.stdout.on('data', (data) => {
    const output = data.toString();
    const lines = output.split('\\n');
    
    for (const line of lines) {
        if (line.trim()) {
            console.log('MUDSLIDE:', line);
            
            // Look for QR code characters
            if (line.includes('█') || line.includes('▄') || line.includes('▀')) {
                qrLines.push(line);
                console.log('QR_LINE_CAPTURED:', qrLines.length);
            }
            
            // Look for QR code URL in mudslide output (if it outputs the URL)
            if (line.includes('web.whatsapp.com')) {
                const urlMatch = line.match(/https:\\/\\/web\\.whatsapp\\.com\\/[^\\s]+/);
                if (urlMatch) {
                    const qrUrl = urlMatch[0];
                    console.log('QR_URL:' + qrUrl);
                    
                    // Save to file
                    fs.writeFileSync('qr_data.txt', qrUrl);
                    console.log('QR_CAPTURE_COMPLETE');
                    qrCaptured = true;
                    mudslide.kill();
                    process.exit(0);
                }
            }
            
            // Check for login success
            if (line.includes('Logged in') || line.includes('success')) {
                console.log('LOGIN_SUCCESS');
                mudslide.kill();
                process.exit(0);
            }
        }
    }
});

// Capture stderr
mudslide.stderr.on('data', (data) => {
    console.log('MUDSLIDE_ERROR:', data.toString());
});

// Handle mudslide exit
mudslide.on('close', (code) => {
    console.log('MUDSLIDE_EXIT:', code);
    
    if (qrLines.length > 0 && !qrCaptured) {
        console.log('QR_TERMINAL_CAPTURED:', qrLines.length, 'lines');
        
        // Save terminal QR lines
        fs.writeFileSync('qr_terminal.txt', qrLines.join('\\n'));
        console.log('QR_CAPTURE_COMPLETE');
    }
    
    process.exit(code);
});

// Timeout after 15 seconds for testing
setTimeout(() => {
    console.log('TIMEOUT_REACHED');
    mudslide.kill();
    
    if (qrLines.length > 0) {
        console.log('QR_TERMINAL_CAPTURED:', qrLines.length, 'lines');
        fs.writeFileSync('qr_terminal.txt', qrLines.join('\\n'));
        console.log('QR_CAPTURE_COMPLETE');
    }
    
    process.exit(1);
}, 15000);
'''
        
        wrapper_path = os.path.join(test_dir, 'test_wrapper.js')
        with open(wrapper_path, 'w') as f:
            f.write(wrapper_content)
        
        print(f"✅ Created wrapper script: {wrapper_path}")
        
        # Run the wrapper
        print("🔄 Running mudslide wrapper (15 second timeout)...")
        
        process = subprocess.Popen(
            ['node', wrapper_path],
            cwd=test_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Monitor output
        start_time = time.time()
        qr_lines_captured = 0
        qr_url_found = False
        
        while time.time() - start_time < 20:  # 20 second timeout
            if process.poll() is not None:
                break
                
            try:
                line = process.stdout.readline()
                if line:
                    line_stripped = line.strip()
                    print(f"📝 {line_stripped}")
                    
                    if 'QR_LINE_CAPTURED:' in line:
                        qr_lines_captured = int(line.split(':')[-1])
                    
                    if 'QR_URL:' in line:
                        qr_url_found = True
                        print("🎉 QR URL CAPTURED!")
                    
                    if 'QR_CAPTURE_COMPLETE' in line:
                        print("✅ QR CAPTURE COMPLETED!")
                        break
                        
            except:
                break
                
            time.sleep(0.1)
        
        # Clean up process
        if process.poll() is None:
            process.terminate()
            time.sleep(1)
            if process.poll() is None:
                process.kill()
        
        # Check results
        print("\n📋 TEST RESULTS:")
        print("=" * 30)
        
        qr_data_file = os.path.join(test_dir, 'qr_data.txt')
        qr_terminal_file = os.path.join(test_dir, 'qr_terminal.txt')
        
        if os.path.exists(qr_data_file):
            with open(qr_data_file, 'r') as f:
                qr_url = f.read().strip()
            print(f"✅ QR URL captured: {qr_url[:50]}...")
            return True
        elif os.path.exists(qr_terminal_file):
            with open(qr_terminal_file, 'r') as f:
                terminal_lines = f.read().strip().split('\n')
            print(f"✅ Terminal QR captured: {len(terminal_lines)} lines")
            print(f"📝 First line: {terminal_lines[0][:50]}..." if terminal_lines else "No lines")
            return True
        else:
            print("❌ No QR data captured")
            return False
            
    except Exception as e:
        print(f"❌ Error testing wrapper: {e}")
        return False
    finally:
        # Clean up
        try:
            import shutil
            shutil.rmtree(test_dir)
            print(f"🧹 Cleaned up test directory")
        except:
            pass

def main():
    print("🔍 MUDSLIDE WRAPPER TEST")
    print("=" * 60)
    
    # Check if mudslide is available
    try:
        result = subprocess.run(['which', 'mudslide'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ mudslide not found. Please install: npm install -g mudslide")
            return
        
        print(f"✅ mudslide found at: {result.stdout.strip()}")
    except Exception as e:
        print(f"❌ Error checking mudslide: {e}")
        return
    
    # Test the wrapper
    success = test_mudslide_wrapper()
    
    print(f"\n🎯 FINAL RESULT:")
    if success:
        print("✅ Mudslide wrapper working - QR data captured successfully!")
        print("🚀 Ready to use in Odoo module")
    else:
        print("❌ Mudslide wrapper failed - check mudslide installation")
        print("💡 Try: npm install -g mudslide")

if __name__ == '__main__':
    main()
