<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Instance Tree View -->
    <record id="view_whatsapp_instance_tree" model="ir.ui.view">
        <field name="name">whatsapp.instance.tree</field>
        <field name="model">whatsapp.instance</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Instances" 
                  decoration-success="status == 'connected'"
                  decoration-warning="status == 'connecting'"
                  decoration-danger="status == 'error'"
                  decoration-muted="not is_active">
                <field name="name"/>
                <field name="session_id"/>
                <field name="phone_number"/>
                <field name="status" widget="badge"
                       decoration-success="status == 'connected'"
                       decoration-warning="status == 'connecting'"
                       decoration-danger="status == 'error'"
                       decoration-muted="status == 'disconnected'"/>
                <field name="is_active" widget="boolean_toggle"/>
                <field name="is_default" widget="boolean_toggle"/>
                <field name="total_conversations"/>
                <field name="total_messages"/>
                <field name="messages_today"/>
                <field name="last_sync"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Instance Form View -->
    <record id="view_whatsapp_instance_form" model="ir.ui.view">
        <field name="name">whatsapp.instance.form</field>
        <field name="model">whatsapp.instance</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Instance">
                <header>
                    <button name="action_test_connection" type="object"
                            string="Test Connection" class="btn-primary"
                            icon="fa-plug"/>
                    <button name="action_sync_messages" type="object"
                            string="Sync Messages" class="btn-secondary"
                            icon="fa-refresh"/>
                    <button name="action_get_qr_code" type="object"
                            string="Get QR Code" class="btn-info"
                            icon="fa-qrcode"/>
                    <button name="action_create_whatsapp_instance" type="object"
                            string="Create Instance" class="btn-warning"
                            icon="fa-plus"/>
                    <button name="action_configure_webhook" type="object"
                            string="Configure Webhook" class="btn-success"
                            icon="fa-link"/>
                    <button name="action_test_webhook" type="object"
                            string="Test Webhook" class="btn-secondary"
                            icon="fa-check"/>
                    <button name="action_send_test_message" type="object"
                            string="Send Test Message" class="btn-success"
                            icon="fa-paper-plane" invisible="status != 'connected'"/>
                    <button name="action_discover_api_endpoints" type="object"
                            string="Discover API" class="btn-info"
                            icon="fa-search"/>
                    <button name="action_get_api_token" type="object"
                            string="Get API Token" class="btn-warning"
                            icon="fa-key"/>
                    <field name="status" widget="statusbar"
                           statusbar_visible="disconnected,connecting,connected"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_whatsapp_conversation_tree)d" type="action" 
                                class="oe_stat_button" icon="fa-comments"
                                context="{'search_default_instance_id': active_id}">
                            <field name="total_conversations" widget="statinfo" 
                                   string="Conversations"/>
                        </button>
                        <button name="%(action_whatsapp_message_tree)d" type="action" 
                                class="oe_stat_button" icon="fa-envelope"
                                context="{'search_default_instance_id': active_id}">
                            <field name="total_messages" widget="statinfo" 
                                   string="Messages"/>
                        </button>
                        <button class="oe_stat_button" icon="fa-calendar-day">
                            <field name="messages_today" widget="statinfo" 
                                   string="Today"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Default" 
                            bg_color="bg-success" 
                            invisible="not is_default"/>
                    <widget name="web_ribbon" title="Inactive" 
                            bg_color="bg-danger" 
                            invisible="is_active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Instance Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="session_id"/>
                            <field name="phone_number"/>
                            <field name="is_active"/>
                            <field name="is_default"/>
                        </group>
                        <group name="connection_info">
                            <field name="api_url"/>
                            <field name="api_token" password="True"/>
                            <field name="created_date"/>
                            <field name="last_sync"/>
                        </group>
                    </group>
                    
                    <group name="sync_settings" string="Sync Settings">
                        <group>
                            <field name="auto_sync"/>
                            <field name="sync_interval" invisible="not auto_sync"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Conversations" name="conversations">
                            <field name="conversation_ids" mode="tree">
                                <tree string="Conversations">
                                    <field name="partner_id"/>
                                    <field name="phone_number"/>
                                    <field name="status"/>
                                    <field name="message_count"/>
                                    <field name="unread_count"/>
                                    <field name="last_message_date"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Statistics" name="statistics">
                            <group>
                                <group>
                                    <field name="total_conversations" readonly="1"/>
                                    <field name="total_messages" readonly="1"/>
                                    <field name="messages_today" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Instance Search View -->
    <record id="view_whatsapp_instance_search" model="ir.ui.view">
        <field name="name">whatsapp.instance.search</field>
        <field name="model">whatsapp.instance</field>
        <field name="arch" type="xml">
            <search string="Search Instances">
                <field name="name" string="Instance Name"/>
                <field name="session_id" string="Session ID"/>
                <field name="phone_number" string="Phone Number"/>
                
                <filter string="Active" name="active" 
                        domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" 
                        domain="[('is_active', '=', False)]"/>
                
                <separator/>
                <filter string="Connected" name="connected" 
                        domain="[('status', '=', 'connected')]"/>
                <filter string="Disconnected" name="disconnected" 
                        domain="[('status', '=', 'disconnected')]"/>
                <filter string="Error" name="error" 
                        domain="[('status', '=', 'error')]"/>
                
                <separator/>
                <filter string="Default Instance" name="default" 
                        domain="[('is_default', '=', True)]"/>
                <filter string="Auto Sync Enabled" name="auto_sync" 
                        domain="[('auto_sync', '=', True)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Active" name="group_active" 
                            context="{'group_by': 'is_active'}"/>
                </group>
            </search>
        </field>
    </record>



</odoo>
