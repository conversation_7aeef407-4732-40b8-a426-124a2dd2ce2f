<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Message Tree View -->
    <record id="view_whatsapp_message_tree" model="ir.ui.view">
        <field name="name">whatsapp.message.tree</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Messages" 
                  decoration-bf="direction == 'incoming' and not is_read"
                  decoration-success="direction == 'outgoing'"
                  decoration-info="direction == 'incoming'">
                <field name="date_sent"/>
                <field name="conversation_id"/>
                <field name="direction" widget="badge"
                       decoration-success="direction == 'outgoing'"
                       decoration-info="direction == 'incoming'"/>
                <field name="message_type"/>
                <field name="content"/>
                <field name="status" widget="badge"
                       decoration-success="status == 'delivered'"
                       decoration-warning="status == 'pending'"
                       decoration-danger="status == 'failed'"/>
                <field name="is_read" widget="boolean_toggle"/>
                <field name="has_media" widget="boolean_toggle"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Message Form View -->
    <record id="view_whatsapp_message_form" model="ir.ui.view">
        <field name="name">whatsapp.message.form</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Message">
                <header>
                    <field name="status" widget="statusbar"
                           statusbar_visible="pending,sent,delivered,read,failed"/>
                    <button name="action_resend_message" type="object" string="Resend"
                            class="btn-primary" invisible="not (direction == 'outgoing' and status == 'failed')"/>
                    <button name="action_debug_message" type="object" string="Debug Info"
                            class="btn-info" invisible="direction != 'outgoing'"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="message_type" readonly="1"/>
                            <span> Message</span>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="message_info">
                            <field name="conversation_id"/>
                            <field name="direction" widget="badge"/>
                            <field name="message_type"/>
                            <field name="message_id"/>
                            <field name="has_media" invisible="1"/>
                        </group>
                        <group name="timestamps">
                            <field name="date_sent"/>
                            <field name="date_delivered"/>
                            <field name="date_read"/>
                            <field name="is_read"/>
                        </group>
                    </group>
                    
                    <div class="oe_clear"/>
                    <separator string="Message Content"/>
                    <field name="content" widget="text" nolabel="1"
                           placeholder="Message content..."
                           style="min-height: 120px; width: 100%;"/>
                    <div class="oe_clear"/>
                    
                    <notebook>
                        <page string="Media" name="media" invisible="not has_media">
                            <field name="attachment_ids" mode="kanban">
                                <kanban>
                                    <field name="filename"/>
                                    <field name="mimetype"/>
                                    <field name="file_size"/>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div class="oe_kanban_card">
                                                <div class="o_kanban_record_top">
                                                    <strong><field name="filename"/></strong>
                                                </div>
                                                <div class="o_kanban_record_body">
                                                    <field name="mimetype"/>
                                                    <br/>
                                                    <field name="file_size" widget="integer"/>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                            </field>
                        </page>
                        <page string="Technical Info" name="technical">
                            <group>
                                <field name="message_id"/>
                                <field name="user_id"/>
                                <field name="error_message" invisible="not error_message"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Message Search View -->
    <record id="view_whatsapp_message_search" model="ir.ui.view">
        <field name="name">whatsapp.message.search</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <search string="Search Messages">
                <field name="conversation_id" string="Conversation"/>
                <field name="content" string="Content"/>
                <field name="message_id" string="WhatsApp ID"/>
                
                <filter string="Incoming" name="incoming" 
                        domain="[('direction', '=', 'incoming')]"/>
                <filter string="Outgoing" name="outgoing" 
                        domain="[('direction', '=', 'outgoing')]"/>
                
                <separator/>
                <filter string="Unread" name="unread" 
                        domain="[('is_read', '=', False), ('direction', '=', 'incoming')]"/>
                <filter string="With Media" name="with_media" 
                        domain="[('has_media', '=', True)]"/>
                
                <separator/>
                <filter string="Text Messages" name="text" 
                        domain="[('message_type', '=', 'text')]"/>
                <filter string="Image Messages" name="image" 
                        domain="[('message_type', '=', 'image')]"/>
                <filter string="Audio Messages" name="audio" 
                        domain="[('message_type', '=', 'audio')]"/>
                <filter string="Video Messages" name="video" 
                        domain="[('message_type', '=', 'video')]"/>
                <filter string="Document Messages" name="document" 
                        domain="[('message_type', '=', 'document')]"/>
                
                <separator/>
                <filter string="Today" name="today" 
                        domain="[('date_sent', '>=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="This Week" name="this_week" 
                        domain="[('date_sent', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Conversation" name="group_conversation" 
                            context="{'group_by': 'conversation_id'}"/>
                    <filter string="Direction" name="group_direction" 
                            context="{'group_by': 'direction'}"/>
                    <filter string="Message Type" name="group_type" 
                            context="{'group_by': 'message_type'}"/>
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Date" name="group_date" 
                            context="{'group_by': 'date_sent:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_whatsapp_message_tree" model="ir.actions.act_window">
        <field name="name">WhatsApp Messages</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_whatsapp_message_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No WhatsApp messages found!
            </p>
            <p>
                Messages will appear here once conversations are synced.
            </p>
        </field>
    </record>

    <record id="action_whatsapp_message_outgoing" model="ir.actions.act_window">
        <field name="name">Outgoing Messages</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('direction', '=', 'outgoing')]</field>
        <field name="context">{'search_default_outgoing': 1}</field>
        <field name="search_view_id" ref="view_whatsapp_message_search"/>
    </record>

    <record id="action_whatsapp_message_incoming" model="ir.actions.act_window">
        <field name="name">Incoming Messages</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('direction', '=', 'incoming')]</field>
        <field name="context">{'search_default_incoming': 1}</field>
        <field name="search_view_id" ref="view_whatsapp_message_search"/>
    </record>

</odoo>
