<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Conversation Actions -->
    <record id="action_whatsapp_conversation_tree" model="ir.actions.act_window">
        <field name="name">WhatsApp Conversations</field>
        <field name="res_model">whatsapp.conversation</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first WhatsApp conversation!
            </p>
            <p>
                Track and manage WhatsApp conversations with your customers.
            </p>
        </field>
    </record>

    <!-- WhatsApp Message Actions -->
    <record id="action_whatsapp_message_tree" model="ir.actions.act_window">
        <field name="name">WhatsApp Messages</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No WhatsApp messages yet!
            </p>
            <p>
                All your WhatsApp messages will appear here.
            </p>
        </field>
    </record>

    <!-- WhatsApp Instance Actions -->
    <record id="action_whatsapp_instance_tree" model="ir.actions.act_window">
        <field name="name">WhatsApp Instances</field>
        <field name="res_model">whatsapp.instance</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Configure your first WhatsApp instance!
            </p>
            <p>
                Set up WhatsApp instances to connect with your customers.
            </p>
        </field>
    </record>

    <!-- WhatsApp Template Actions -->
    <record id="action_whatsapp_template_tree" model="ir.actions.act_window">
        <field name="name">Message Templates</field>
        <field name="res_model">whatsapp.template</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first message template!
            </p>
            <p>
                Create reusable message templates for quick responses.
            </p>
        </field>
    </record>

    <!-- WhatsApp Payment Actions -->
    <record id="action_whatsapp_payment_tree" model="ir.actions.act_window">
        <field name="name">WhatsApp Payments</field>
        <field name="res_model">whatsapp.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No payment records yet!
            </p>
            <p>
                Track WhatsApp payment transactions here.
            </p>
        </field>
    </record>
</odoo>
