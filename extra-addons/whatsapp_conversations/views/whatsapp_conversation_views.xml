<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Conversation Tree View -->
    <record id="view_whatsapp_conversation_tree" model="ir.ui.view">
        <field name="name">whatsapp.conversation.tree</field>
        <field name="model">whatsapp.conversation</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Conversations" 
                  decoration-bf="unread_count > 0"
                  decoration-muted="status == 'archived'"
                  decoration-danger="status == 'blocked'">
                <field name="partner_id"/>
                <field name="phone_number"/>
                <field name="instance_id"/>
                <field name="status" widget="badge" 
                       decoration-success="status == 'active'"
                       decoration-muted="status == 'archived'"
                       decoration-danger="status == 'blocked'"
                       decoration-warning="status == 'spam'"/>
                <field name="message_count"/>
                <field name="unread_count" widget="badge" decoration-danger="unread_count > 0"/>
                <field name="last_message_date"/>
                <field name="last_message_preview"/>
                <field name="user_id"/>
                <field name="priority" widget="priority"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Conversation Form View -->
    <record id="view_whatsapp_conversation_form" model="ir.ui.view">
        <field name="name">whatsapp.conversation.form</field>
        <field name="model">whatsapp.conversation</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Conversation">
                <header>
                    <button name="%(action_whatsapp_send_message)d" type="action"
                            string="Send Message" class="btn-primary"
                            icon="fa-paper-plane"
                            context="{'default_conversation_id': active_id, 'default_instance_id': instance_id, 'default_phone_number': phone_number}"/>
                    <button name="action_mark_as_read" type="object" 
                            string="Mark as Read" 
                            invisible="unread_count == 0"
                            icon="fa-check"/>
                    <button name="action_archive_conversation" type="object" 
                            string="Archive" 
                            invisible="status == 'archived'"
                            icon="fa-archive"/>
                    <field name="status" widget="statusbar" 
                           statusbar_visible="active,archived"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_payments" type="object" 
                                class="oe_stat_button" icon="fa-money">
                            <field name="payment_ids" widget="statinfo" 
                                   string="Payments"/>
                        </button>
                    </div>
                    
                    <widget name="web_ribbon" title="Archived" 
                            bg_color="bg-danger" 
                            invisible="status != 'archived'"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="display_name" readonly="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="contact_info">
                            <field name="partner_id" 
                                   options="{'no_create': True, 'no_edit': True}"/>
                            <field name="phone_number"/>
                            <field name="instance_id" 
                                   options="{'no_create': True}"/>
                        </group>
                        <group name="conversation_info">
                            <field name="user_id"/>
                            <field name="priority" widget="priority"/>
                            <field name="is_public"/>
                            <field name="tag_ids" widget="many2many_tags" 
                                   options="{'color_field': 'color'}"/>
                        </group>
                    </group>
                    
                    <group name="statistics">
                        <group>
                            <field name="message_count"/>
                            <field name="unread_count"/>
                        </group>
                        <group>
                            <field name="first_message_date"/>
                            <field name="last_message_date"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Messages" name="messages">
                            <field name="message_ids" 
                                   context="{'default_conversation_id': active_id}"
                                   mode="tree">
                                <tree string="Messages" 
                                      decoration-bf="direction == 'incoming' and not is_read"
                                      decoration-success="direction == 'outgoing'"
                                      decoration-info="direction == 'incoming'">
                                    <field name="date_sent"/>
                                    <field name="direction" widget="badge"
                                           decoration-success="direction == 'outgoing'"
                                           decoration-info="direction == 'incoming'"/>
                                    <field name="message_type"/>
                                    <field name="content"/>
                                    <field name="status" widget="badge"
                                           decoration-success="status == 'delivered'"
                                           decoration-warning="status == 'pending'"
                                           decoration-danger="status == 'failed'"/>
                                    <field name="is_read" widget="boolean_toggle"/>
                                    <field name="has_media" widget="boolean_toggle"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Payments" name="payments">
                            <field name="payment_ids" 
                                   context="{'default_conversation_id': active_id, 'default_partner_id': partner_id}">
                                <tree string="Payments">
                                    <field name="payment_date"/>
                                    <field name="amount" widget="monetary"/>
                                    <field name="payment_method"/>
                                    <field name="payment_status" widget="badge"
                                           decoration-success="payment_status == 'confirmed'"
                                           decoration-warning="payment_status == 'pending'"
                                           decoration-danger="payment_status == 'failed'"/>
                                    <field name="reference"/>
                                    <field name="description"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Notes" name="notes">
                            <field name="notes" placeholder="Internal notes about this conversation..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- WhatsApp Conversation Kanban View -->
    <record id="view_whatsapp_conversation_kanban" model="ir.ui.view">
        <field name="name">whatsapp.conversation.kanban</field>
        <field name="model">whatsapp.conversation</field>
        <field name="arch" type="xml">
            <kanban string="WhatsApp Conversations" 
                    default_group_by="status"
                    class="o_kanban_small_column">
                <field name="partner_id"/>
                <field name="phone_number"/>
                <field name="unread_count"/>
                <field name="last_message_date"/>
                <field name="last_message_preview"/>
                <field name="status"/>
                <field name="priority"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_record_top">
                                <div class="o_kanban_record_headings">
                                    <strong class="o_kanban_record_title">
                                        <field name="partner_id"/>
                                    </strong>
                                    <small class="o_kanban_record_subtitle text-muted">
                                        <field name="phone_number"/>
                                    </small>
                                </div>
                                <div class="o_kanban_record_top_right">
                                    <field name="priority" widget="priority"/>
                                    <t t-if="record.unread_count.raw_value > 0">
                                        <span class="badge badge-danger">
                                            <field name="unread_count"/>
                                        </span>
                                    </t>
                                </div>
                            </div>
                            <div class="o_kanban_record_body">
                                <field name="last_message_preview"/>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="last_message_date" widget="relative"/>
                                </div>
                                <div class="oe_kanban_bottom_right">
                                    <field name="user_id" widget="many2one_avatar_user"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- WhatsApp Conversation Search View -->
    <record id="view_whatsapp_conversation_search" model="ir.ui.view">
        <field name="name">whatsapp.conversation.search</field>
        <field name="model">whatsapp.conversation</field>
        <field name="arch" type="xml">
            <search string="Search Conversations">
                <field name="partner_id" string="Contact"/>
                <field name="phone_number" string="Phone Number"/>
                <field name="instance_id" string="Instance"/>
                <field name="user_id" string="Assigned User"/>
                
                <filter string="Active" name="active" 
                        domain="[('status', '=', 'active')]"/>
                <filter string="Archived" name="archived" 
                        domain="[('status', '=', 'archived')]"/>
                <filter string="Blocked" name="blocked" 
                        domain="[('status', '=', 'blocked')]"/>
                
                <separator/>
                <filter string="Unread Messages" name="unread" 
                        domain="[('unread_count', '>', 0)]"/>
                <filter string="My Conversations" name="my_conversations" 
                        domain="[('user_id', '=', uid)]"/>
                <filter string="Public Conversations" name="public" 
                        domain="[('is_public', '=', True)]"/>
                
                <separator/>
                <filter string="Today" name="today" 
                        domain="[('last_message_date', '>=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="This Week" name="this_week" 
                        domain="[('last_message_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" 
                        domain="[('last_message_date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'status'}"/>
                    <filter string="Assigned User" name="group_user" 
                            context="{'group_by': 'user_id'}"/>
                    <filter string="Instance" name="group_instance" 
                            context="{'group_by': 'instance_id'}"/>
                    <filter string="Priority" name="group_priority" 
                            context="{'group_by': 'priority'}"/>
                </group>
            </search>
        </field>
    </record>



</odoo>
