<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Payment Tree View -->
    <record id="view_whatsapp_payment_tree" model="ir.ui.view">
        <field name="name">whatsapp.payment.tree</field>
        <field name="model">whatsapp.payment</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Payments" 
                  decoration-success="payment_status == 'confirmed'"
                  decoration-warning="payment_status == 'pending'"
                  decoration-danger="payment_status == 'failed'">
                <field name="payment_date"/>
                <field name="conversation_id"/>
                <field name="partner_id"/>
                <field name="amount" widget="monetary"/>
                <field name="currency_id" invisible="1"/>
                <field name="payment_method"/>
                <field name="payment_status" widget="badge"
                       decoration-success="payment_status == 'confirmed'"
                       decoration-warning="payment_status == 'pending'"
                       decoration-danger="payment_status == 'failed'"/>
                <field name="reference"/>
                <field name="description"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Payment Form View -->
    <record id="view_whatsapp_payment_form" model="ir.ui.view">
        <field name="name">whatsapp.payment.form</field>
        <field name="model">whatsapp.payment</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Payment">
                <header>
                    <field name="payment_status" widget="statusbar" 
                           statusbar_visible="pending,confirmed,failed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="amount" widget="monetary"/>
                            <field name="currency_id" invisible="1"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="payment_info">
                            <field name="conversation_id"/>
                            <field name="partner_id"/>
                            <field name="payment_date"/>
                            <field name="payment_method"/>
                        </group>
                        <group name="details">
                            <field name="reference"/>
                            <field name="transaction_id"/>
                            <field name="external_reference"/>
                            <field name="is_verified"/>
                        </group>
                    </group>
                    
                    <group name="description" string="Description">
                        <field name="description" nolabel="1"/>
                    </group>
                    
                    <notebook>
                        <page string="Payment Details" name="details">
                            <group>
                                <group>
                                    <field name="bank_name"/>
                                    <field name="ifsc_code"/>
                                    <field name="upi_id"/>
                                    <field name="upi_reference"/>
                                </group>
                                <group>
                                    <field name="card_last_four"/>
                                    <field name="card_type"/>
                                    <field name="due_date"/>
                                    <field name="confirmed_date"/>
                                </group>
                            </group>
                        </page>
                        <page string="Notes" name="notes">
                            <field name="notes" placeholder="Internal notes about this payment..."/>
                            <field name="verification_notes" placeholder="Verification notes..."/>
                        </page>
                        <page string="Attachments" name="attachments">
                            <field name="attachment_ids"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Payment Search View -->
    <record id="view_whatsapp_payment_search" model="ir.ui.view">
        <field name="name">whatsapp.payment.search</field>
        <field name="model">whatsapp.payment</field>
        <field name="arch" type="xml">
            <search string="Search Payments">
                <field name="conversation_id" string="Conversation"/>
                <field name="partner_id" string="Contact"/>
                <field name="reference" string="Reference"/>
                <field name="transaction_id" string="Transaction ID"/>
                
                <filter string="Pending" name="pending" 
                        domain="[('payment_status', '=', 'pending')]"/>
                <filter string="Confirmed" name="confirmed" 
                        domain="[('payment_status', '=', 'confirmed')]"/>
                <filter string="Failed" name="failed" 
                        domain="[('payment_status', '=', 'failed')]"/>
                
                <separator/>
                <filter string="Verified" name="verified" 
                        domain="[('is_verified', '=', True)]"/>
                <filter string="Unverified" name="unverified" 
                        domain="[('is_verified', '=', False)]"/>
                
                <separator/>
                <filter string="Today" name="today" 
                        domain="[('payment_date', '>=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter string="This Week" name="this_week" 
                        domain="[('payment_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" 
                        domain="[('payment_date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_status" 
                            context="{'group_by': 'payment_status'}"/>
                    <filter string="Payment Method" name="group_method" 
                            context="{'group_by': 'payment_method'}"/>
                    <filter string="Contact" name="group_partner" 
                            context="{'group_by': 'partner_id'}"/>
                    <filter string="Date" name="group_date" 
                            context="{'group_by': 'payment_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_whatsapp_payment_tree" model="ir.actions.act_window">
        <field name="name">WhatsApp Payments</field>
        <field name="res_model">whatsapp.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_whatsapp_payment_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No WhatsApp payments found!
            </p>
            <p>
                Payment records will appear here when payments are processed through WhatsApp.
            </p>
        </field>
    </record>

    <record id="action_whatsapp_payment_pending" model="ir.actions.act_window">
        <field name="name">Pending Payments</field>
        <field name="res_model">whatsapp.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('payment_status', '=', 'pending')]</field>
        <field name="context">{'search_default_pending': 1}</field>
        <field name="search_view_id" ref="view_whatsapp_payment_search"/>
    </record>

    <record id="action_whatsapp_payment_confirmed" model="ir.actions.act_window">
        <field name="name">Confirmed Payments</field>
        <field name="res_model">whatsapp.payment</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('payment_status', '=', 'confirmed')]</field>
        <field name="context">{'search_default_confirmed': 1}</field>
        <field name="search_view_id" ref="view_whatsapp_payment_search"/>
    </record>

</odoo>
