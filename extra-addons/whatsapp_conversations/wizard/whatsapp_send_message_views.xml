<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Send Message Wizard Form View -->
    <record id="view_whatsapp_send_message_form" model="ir.ui.view">
        <field name="name">whatsapp.send.message.form</field>
        <field name="model">whatsapp.send.message</field>
        <field name="arch" type="xml">
            <form string="Send WhatsApp Message">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="message_type" widget="radio" options="{'horizontal': true}"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="recipient_info">
                            <field name="conversation_id" options="{'no_create': True}"/>
                            <field name="instance_id" options="{'no_create': True}" required="1"/>
                            <field name="phone_number" required="1"/>
                        </group>
                    </group>
                    
                    <!-- Text Message Section -->
                    <group string="Text Message" invisible="message_type != 'text'">
                        <field name="message_content" widget="text" placeholder="Enter your message here..." nolabel="1"/>
                    </group>
                    
                    <!-- Media Message Section -->
                    <group string="Media Message" invisible="message_type != 'media'">
                        <group>
                            <field name="media_file" filename="media_filename"/>
                            <field name="media_filename" invisible="1"/>
                        </group>
                        <group>
                            <field name="media_caption" widget="text" placeholder="Optional caption for media..."/>
                        </group>
                    </group>
                    
                    <!-- Template Message Section -->
                    <group string="Template Message" invisible="message_type != 'template'">
                        <group>
                            <field name="template_id" options="{'no_create': True}" required="message_type == 'template'"/>
                        </group>
                        <group>
                            <field name="template_variables" widget="text" 
                                   placeholder='{"variable1": "value1", "variable2": "value2"}'
                                   help="Enter template variables in JSON format"/>
                        </group>
                        
                        <!-- Template Preview -->
                        <div invisible="not template_id" class="alert alert-info" role="alert">
                            <strong>Template Content:</strong><br/>
                            <field name="template_id" readonly="1" options="{'no_open': True}"/>
                        </div>
                    </group>
                </sheet>
                
                <footer>
                    <button name="action_send_message" type="object" string="Send Message" class="btn-primary"/>
                    <button name="action_preview_message" type="object" string="Preview" class="btn-secondary" 
                            invisible="message_type == 'media'"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- WhatsApp Send Message Action -->
    <record id="action_whatsapp_send_message" model="ir.actions.act_window">
        <field name="name">Send WhatsApp Message</field>
        <field name="res_model">whatsapp.send.message</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_conversation_id': active_id,
            'default_instance_id': context.get('default_instance_id'),
            'default_phone_number': context.get('default_phone_number')
        }</field>
    </record>
</odoo>
