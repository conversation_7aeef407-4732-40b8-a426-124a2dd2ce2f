# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import requests
import json
import logging

_logger = logging.getLogger(__name__)

class WhatsAppSendMessage(models.TransientModel):
    _name = 'whatsapp.send.message'
    _description = 'WhatsApp Send Message Wizard'

    conversation_id = fields.Many2one('whatsapp.conversation', string='Conversation', required=True)
    instance_id = fields.Many2one('whatsapp.instance', string='WhatsApp Instance', required=True)
    phone_number = fields.Char(string='Phone Number', required=True)
    message_type = fields.Selection([
        ('text', 'Text'),
        ('media', 'Media'),
        ('template', 'Template')
    ], string='Message Type', default='text', required=True)
    
    # Text message fields
    message_content = fields.Text(string='Message Content')
    
    # Media message fields
    media_file = fields.Binary(string='Media File')
    media_filename = fields.Char(string='Filename')
    media_caption = fields.Text(string='Caption')
    
    # Template message fields
    template_id = fields.Many2one('whatsapp.template', string='Template')
    template_variables = fields.Text(string='Template Variables (JSON)')
    
    @api.onchange('conversation_id')
    def _onchange_conversation_id(self):
        if self.conversation_id:
            self.instance_id = self.conversation_id.instance_id
            self.phone_number = self.conversation_id.phone_number

    @api.onchange('template_id')
    def _onchange_template_id(self):
        if self.template_id:
            # Pre-fill template variables if any
            if self.template_id.variable_ids:
                variables = {}
                for var in self.template_id.variable_ids:
                    variables[var.name] = var.default_value or ""
                self.template_variables = json.dumps(variables, indent=2)

    def action_send_message(self):
        """Send WhatsApp message through the service"""
        self.ensure_one()
        
        if not self.instance_id:
            raise UserError(_('Please select a WhatsApp instance.'))
        
        if not self.phone_number:
            raise UserError(_('Phone number is required.'))
        
        # Validate phone number format
        phone = self.phone_number.strip()
        if not phone.startswith('+'):
            phone = '+' + phone
        
        try:
            # Prepare message data based on type
            message_data = self._prepare_message_data(phone)
            
            # Send message through WhatsApp service
            response = self._send_to_whatsapp_service(message_data)
            
            # Create message record in Odoo
            self._create_message_record(message_data, response)
            
            # Update conversation
            if self.conversation_id:
                self.conversation_id.write({
                    'last_message_date': fields.Datetime.now(),
                    'message_count': self.conversation_id.message_count + 1
                })
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Message sent successfully!'),
                    'type': 'success',
                    'sticky': False,
                }
            }
            
        except Exception as e:
            _logger.error(f"Error sending WhatsApp message: {str(e)}")
            raise UserError(_('Failed to send message: %s') % str(e))

    def _prepare_message_data(self, phone):
        """Prepare message data based on message type"""
        data = {
            'phone': phone,
            'instance_id': self.instance_id.session_id,
            'type': self.message_type
        }
        
        if self.message_type == 'text':
            if not self.message_content:
                raise UserError(_('Message content is required for text messages.'))
            data['message'] = self.message_content
            
        elif self.message_type == 'media':
            if not self.media_file:
                raise UserError(_('Media file is required for media messages.'))
            # For now, we'll handle media upload separately
            # This would need to be implemented based on your service API
            data['caption'] = self.media_caption or ''
            data['filename'] = self.media_filename or 'file'
            
        elif self.message_type == 'template':
            if not self.template_id:
                raise UserError(_('Template is required for template messages.'))
            
            # Parse template variables
            variables = {}
            if self.template_variables:
                try:
                    variables = json.loads(self.template_variables)
                except json.JSONDecodeError:
                    raise UserError(_('Invalid JSON format in template variables.'))
            
            # Replace variables in template content
            message_content = self.template_id.content
            for var_name, var_value in variables.items():
                message_content = message_content.replace(f'{{{var_name}}}', str(var_value))
            
            data['message'] = message_content
            data['template_id'] = self.template_id.id
        
        return data

    def _send_to_whatsapp_service(self, message_data):
        """Send message to WhatsApp service API"""
        service_url = "http://localhost:3001"
        endpoint = f"{service_url}/instances/{message_data['instance_id']}/send"
        
        try:
            response = requests.post(
                endpoint,
                json=message_data,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                error_msg = f"Service returned status {response.status_code}"
                if response.text:
                    error_msg += f": {response.text}"
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to connect to WhatsApp service: {str(e)}")

    def _create_message_record(self, message_data, response):
        """Create message record in Odoo"""
        message_vals = {
            'conversation_id': self.conversation_id.id if self.conversation_id else False,
            'instance_id': self.instance_id.id,
            'direction': 'outgoing',
            'message_type': self.message_type,
            'content': message_data.get('message', ''),
            'phone_number': message_data['phone'],
            'date_sent': fields.Datetime.now(),
            'status': 'sent' if response.get('success') else 'failed',
            'message_id': response.get('message_id', ''),
            'user_id': self.env.user.id,
        }
        
        if response.get('error'):
            message_vals['error_message'] = response['error']
        
        # Create the message record
        message = self.env['whatsapp.message'].create(message_vals)
        
        # Update template usage if template was used
        if self.message_type == 'template' and self.template_id:
            self.template_id.write({
                'usage_count': self.template_id.usage_count + 1,
                'last_used': fields.Datetime.now()
            })
        
        return message

    def action_preview_message(self):
        """Preview the message before sending"""
        self.ensure_one()
        
        if self.message_type == 'text':
            preview_content = self.message_content or ''
        elif self.message_type == 'template' and self.template_id:
            # Parse template variables and show preview
            variables = {}
            if self.template_variables:
                try:
                    variables = json.loads(self.template_variables)
                except json.JSONDecodeError:
                    variables = {}
            
            preview_content = self.template_id.content
            for var_name, var_value in variables.items():
                preview_content = preview_content.replace(f'{{{var_name}}}', str(var_value))
        else:
            preview_content = "Media message with caption: " + (self.media_caption or "No caption")
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Message Preview'),
                'message': preview_content,
                'type': 'info',
                'sticky': True,
            }
        }
