/* WhatsApp Conversations Module Styles */

/* WhatsApp Brand Colors */
:root {
    --whatsapp-green: #25D366;
    --whatsapp-green-dark: #128C7E;
    --whatsapp-green-light: #DCF8C6;
    --whatsapp-blue: #34B7F1;
    --whatsapp-gray: #ECE5DD;
    --whatsapp-dark-gray: #3B4A54;
}

/* WhatsApp Button Styling */
.btn-whatsapp {
    background-color: var(--whatsapp-green);
    border-color: var(--whatsapp-green);
    color: white;
}

.btn-whatsapp:hover {
    background-color: var(--whatsapp-green-dark);
    border-color: var(--whatsapp-green-dark);
    color: white;
}

/* WhatsApp Icon Styling */
.fa-whatsapp {
    color: var(--whatsapp-green);
}

/* Conversation List Styling */
.whatsapp-conversation-card {
    border-left: 4px solid var(--whatsapp-green);
    transition: all 0.3s ease;
}

.whatsapp-conversation-card:hover {
    box-shadow: 0 4px 8px rgba(37, 211, 102, 0.2);
    transform: translateY(-2px);
}

.whatsapp-conversation-unread {
    background-color: var(--whatsapp-green-light);
    font-weight: bold;
}

/* Message Bubble Styling */
.whatsapp-message-incoming {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 18px 18px 18px 4px;
    padding: 8px 12px;
    margin: 4px 0;
    max-width: 70%;
    float: left;
    clear: both;
}

.whatsapp-message-outgoing {
    background-color: var(--whatsapp-green-light);
    border-radius: 18px 18px 4px 18px;
    padding: 8px 12px;
    margin: 4px 0;
    max-width: 70%;
    float: right;
    clear: both;
}

.whatsapp-message-timestamp {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
}

.whatsapp-message-status {
    font-size: 12px;
    color: var(--whatsapp-green);
    margin-left: 4px;
}

/* Payment Record Styling */
.whatsapp-payment-confirmed {
    border-left: 4px solid #28a745;
}

.whatsapp-payment-pending {
    border-left: 4px solid #ffc107;
}

.whatsapp-payment-failed {
    border-left: 4px solid #dc3545;
}

/* Statistics Cards */
.whatsapp-stat-card {
    background: linear-gradient(135deg, var(--whatsapp-green), var(--whatsapp-green-dark));
    color: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.whatsapp-stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.whatsapp-stat-label {
    font-size: 0.9em;
    opacity: 0.9;
}

/* Template Preview */
.whatsapp-template-preview {
    background-color: var(--whatsapp-gray);
    border-radius: 10px;
    padding: 15px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.4;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .whatsapp-message-incoming,
    .whatsapp-message-outgoing {
        max-width: 85%;
    }
    
    .whatsapp-stat-card {
        margin-bottom: 15px;
    }
    
    .whatsapp-conversation-card {
        margin-bottom: 10px;
    }
}

/* Kanban View Enhancements */
.o_kanban_view .whatsapp-conversation-card {
    border-radius: 8px;
    overflow: hidden;
}

.o_kanban_record .whatsapp-unread-badge {
    background-color: var(--whatsapp-green);
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: bold;
}

/* Form View Enhancements */
.o_form_view .whatsapp-section {
    border: 1px solid var(--whatsapp-green);
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.o_form_view .whatsapp-section h3 {
    color: var(--whatsapp-green-dark);
    margin-bottom: 15px;
}

/* List View Enhancements */
.o_list_view .whatsapp-priority-high {
    background-color: #fff5f5;
}

.o_list_view .whatsapp-priority-urgent {
    background-color: #ffe6e6;
    font-weight: bold;
}

/* Button Enhancements */
.whatsapp-action-button {
    background: linear-gradient(45deg, var(--whatsapp-green), var(--whatsapp-green-dark));
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.whatsapp-action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(37, 211, 102, 0.3);
    color: white;
}

/* Status Badges */
.badge-whatsapp-active {
    background-color: var(--whatsapp-green);
    color: white;
}

.badge-whatsapp-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge-whatsapp-failed {
    background-color: #dc3545;
    color: white;
}

.badge-whatsapp-archived {
    background-color: #6c757d;
    color: white;
}

/* Animation Classes */
.whatsapp-fade-in {
    animation: whatsappFadeIn 0.5s ease-in;
}

@keyframes whatsappFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.whatsapp-pulse {
    animation: whatsappPulse 2s infinite;
}

@keyframes whatsappPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .whatsapp-message-incoming {
        background-color: #2a2a2a;
        border-color: #444;
        color: #fff;
    }
    
    .whatsapp-template-preview {
        background-color: #2a2a2a;
        color: #fff;
    }
}

/* Print Styles */
@media print {
    .whatsapp-message-incoming,
    .whatsapp-message-outgoing {
        background-color: white !important;
        border: 1px solid #ccc !important;
        color: black !important;
        float: none !important;
        max-width: 100% !important;
        margin: 5px 0 !important;
    }
    
    .whatsapp-action-button {
        display: none !important;
    }
}
