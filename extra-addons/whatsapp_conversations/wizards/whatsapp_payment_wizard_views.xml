<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Payment Wizard Form View -->
    <record id="view_whatsapp_payment_wizard_form" model="ir.ui.view">
        <field name="name">whatsapp.payment.wizard.form</field>
        <field name="model">whatsapp.payment.wizard</field>
        <field name="arch" type="xml">
            <form string="Record WhatsApp Payment">
                <sheet>
                    <div class="oe_title">
                        <h1>Record WhatsApp Payment</h1>
                    </div>
                    
                    <group>
                        <group name="payment_info">
                            <field name="conversation_id" required="1"/>
                            <field name="partner_id" required="1"/>
                            <field name="amount" required="1"/>
                            <field name="currency_id"/>
                        </group>
                        <group name="details">
                            <field name="payment_date"/>
                            <field name="payment_method"/>
                            <field name="reference"/>
                            <field name="transaction_id"/>
                        </group>
                    </group>
                    
                    <group name="description" string="Description">
                        <field name="description" nolabel="1" 
                               placeholder="Enter payment description..."/>
                    </group>
                    
                    <group name="verification" string="Verification">
                        <field name="is_verified"/>
                        <field name="payment_gateway"/>
                        <field name="verification_data" widget="text" 
                               placeholder="Enter verification details..."/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_create_payment" type="object" 
                            string="Create Payment" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_whatsapp_payment_wizard" model="ir.actions.act_window">
        <field name="name">Record WhatsApp Payment</field>
        <field name="res_model">whatsapp.payment.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_whatsapp_payment_wizard_form"/>
    </record>

</odoo>
