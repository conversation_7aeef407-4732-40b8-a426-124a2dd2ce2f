# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class WhatsAppBulkMessage(models.TransientModel):
    _name = 'whatsapp.bulk.message'
    _description = 'WhatsApp Bulk Message Wizard'

    # Instance and Template
    instance_id = fields.Many2one(
        'whatsapp.instance',
        string='WhatsApp Instance',
        required=True,
        domain=[('is_active', '=', True)]
    )
    template_id = fields.Many2one(
        'whatsapp.template',
        string='Message Template',
        domain=[('is_active', '=', True)]
    )
    
    # Message Content
    message_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('document', 'Document')
    ], string='Message Type', default='text', required=True)
    
    message_content = fields.Text(
        string='Message Content',
        required=True
    )
    
    # Media
    attachment_id = fields.Many2one(
        'ir.attachment',
        string='Media File'
    )
    media_caption = fields.Text(
        string='Media Caption'
    )
    
    # Recipients
    recipient_type = fields.Selection([
        ('partners', 'Contacts'),
        ('conversations', 'Conversations'),
        ('phone_numbers', 'Phone Numbers')
    ], string='Recipient Type', default='partners', required=True)
    
    partner_ids = fields.Many2many(
        'res.partner',
        string='Contacts'
    )
    conversation_ids = fields.Many2many(
        'whatsapp.conversation',
        string='Conversations'
    )
    phone_numbers = fields.Text(
        string='Phone Numbers',
        help='Enter phone numbers separated by commas or new lines'
    )
    
    # Scheduling
    send_immediately = fields.Boolean(
        string='Send Immediately',
        default=True
    )
    scheduled_date = fields.Datetime(
        string='Scheduled Date'
    )
    
    # Preview
    preview_content = fields.Text(
        string='Preview Content',
        compute='_compute_preview_content'
    )
    recipient_count = fields.Integer(
        string='Recipient Count',
        compute='_compute_recipient_count'
    )
    
    @api.depends('message_content', 'template_id')
    def _compute_preview_content(self):
        for record in self:
            if record.template_id:
                # Use template content as base
                content = record.template_id.content
                # Replace with actual message content if provided
                if record.message_content:
                    content = record.message_content
            else:
                content = record.message_content or ''
            
            record.preview_content = content
    
    @api.depends('recipient_type', 'partner_ids', 'conversation_ids', 'phone_numbers')
    def _compute_recipient_count(self):
        for record in self:
            count = 0
            if record.recipient_type == 'partners':
                count = len(record.partner_ids)
            elif record.recipient_type == 'conversations':
                count = len(record.conversation_ids)
            elif record.recipient_type == 'phone_numbers' and record.phone_numbers:
                # Count phone numbers (split by comma or newline)
                phones = record._parse_phone_numbers()
                count = len(phones)
            
            record.recipient_count = count
    
    @api.onchange('template_id')
    def _onchange_template_id(self):
        if self.template_id:
            self.message_content = self.template_id.content
    
    def _parse_phone_numbers(self):
        """Parse phone numbers from text field"""
        if not self.phone_numbers:
            return []
        
        # Split by comma or newline and clean up
        phones = []
        for line in self.phone_numbers.split('\n'):
            for phone in line.split(','):
                phone = phone.strip()
                if phone:
                    phones.append(phone)
        
        return phones
    
    def _get_recipients(self):
        """Get list of recipients based on recipient type"""
        recipients = []
        
        if self.recipient_type == 'partners':
            for partner in self.partner_ids:
                phone = partner.mobile or partner.phone
                if phone:
                    recipients.append({
                        'partner_id': partner.id,
                        'phone': phone,
                        'name': partner.name
                    })
        
        elif self.recipient_type == 'conversations':
            for conversation in self.conversation_ids:
                recipients.append({
                    'partner_id': conversation.partner_id.id,
                    'phone': conversation.phone_number,
                    'name': conversation.partner_id.name,
                    'conversation_id': conversation.id
                })
        
        elif self.recipient_type == 'phone_numbers':
            phones = self._parse_phone_numbers()
            for phone in phones:
                # Try to find existing partner
                partner = self.env['res.partner'].search([
                    '|', ('mobile', '=', phone), ('phone', '=', phone)
                ], limit=1)
                
                recipients.append({
                    'partner_id': partner.id if partner else False,
                    'phone': phone,
                    'name': partner.name if partner else phone
                })
        
        return recipients
    
    def action_preview_message(self):
        """Preview the message before sending"""
        self.ensure_one()
        recipients = self._get_recipients()
        
        if not recipients:
            raise UserError(_('No valid recipients found'))
        
        # Update preview fields
        self._compute_preview_content()
        self._compute_recipient_count()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Preview'),
                'message': _('Message will be sent to %d recipients') % len(recipients),
                'type': 'info',
            }
        }
    
    def action_send_bulk_message(self):
        """Send bulk messages"""
        self.ensure_one()
        
        if not self.message_content:
            raise UserError(_('Message content is required'))
        
        recipients = self._get_recipients()
        if not recipients:
            raise UserError(_('No valid recipients found'))
        
        if self.send_immediately:
            return self._send_messages_now(recipients)
        else:
            return self._schedule_messages(recipients)
    
    def _send_messages_now(self, recipients):
        """Send messages immediately"""
        success_count = 0
        error_count = 0
        errors = []
        
        for recipient in recipients:
            try:
                # Get or create conversation
                conversation = self._get_or_create_conversation(recipient)
                
                # Create message
                message_vals = {
                    'conversation_id': conversation.id,
                    'direction': 'outgoing',
                    'message_type': self.message_type,
                    'content': self.message_content,
                    'user_id': self.env.user.id,
                }
                
                message = self.env['whatsapp.message'].create(message_vals)
                
                # Handle media attachment
                if self.attachment_id and self.message_type != 'text':
                    self._create_message_attachment(message)
                
                # Send to WhatsApp
                message._send_to_whatsapp()
                success_count += 1
                
            except Exception as e:
                error_count += 1
                errors.append(f"{recipient['name']}: {str(e)}")
                _logger.error(f"Failed to send message to {recipient['phone']}: {e}")
        
        # Show result
        if error_count == 0:
            message = _('All %d messages sent successfully!') % success_count
            notification_type = 'success'
        else:
            message = _('%d messages sent, %d failed') % (success_count, error_count)
            if errors:
                message += '\n\nErrors:\n' + '\n'.join(errors[:5])
            notification_type = 'warning'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Bulk Message Result'),
                'message': message,
                'type': notification_type,
            }
        }
    
    def _schedule_messages(self, recipients):
        """Schedule messages for later sending"""
        # This would integrate with a job queue system
        # For now, just show a message
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Messages Scheduled'),
                'message': _('%d messages scheduled for %s') % (
                    len(recipients), 
                    self.scheduled_date.strftime('%Y-%m-%d %H:%M')
                ),
                'type': 'success',
            }
        }
    
    def _get_or_create_conversation(self, recipient):
        """Get or create conversation for recipient"""
        conversation = self.env['whatsapp.conversation'].search([
            ('phone_number', '=', recipient['phone']),
            ('instance_id', '=', self.instance_id.id)
        ], limit=1)
        
        if not conversation:
            conversation = self.env['whatsapp.conversation'].create_or_get_conversation(
                recipient['phone'],
                self.instance_id.id,
                recipient.get('partner_id')
            )
        
        return conversation
    
    def _create_message_attachment(self, message):
        """Create attachment for message"""
        if self.attachment_id:
            attachment_vals = {
                'message_id': message.id,
                'filename': self.attachment_id.name,
                'file_data': self.attachment_id.datas,
                'mimetype': self.attachment_id.mimetype,
                'file_size': len(self.attachment_id.raw) if self.attachment_id.raw else 0,
            }
            self.env['whatsapp.attachment'].create(attachment_vals)
