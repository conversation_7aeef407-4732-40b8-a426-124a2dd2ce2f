<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Send Message Wizard Form -->
    <record id="view_whatsapp_send_message_form" model="ir.ui.view">
        <field name="name">whatsapp.send.message.form</field>
        <field name="model">whatsapp.send.message</field>
        <field name="arch" type="xml">
            <form string="Send WhatsApp Message">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <i class="fa fa-whatsapp text-success"/> Send WhatsApp Message
                        </h1>
                    </div>
                    
                    <group>
                        <group name="recipient_info" string="Recipient">
                            <field name="partner_id" 
                                   options="{'no_create': True}"
                                   placeholder="Select contact..."/>
                            <field name="phone_number" 
                                   widget="phone"
                                   placeholder="+91XXXXXXXXXX"/>
                            <field name="conversation_id" 
                                   invisible="1"/>
                            <field name="instance_id" 
                                   options="{'no_create': True}"
                                   required="1"/>
                        </group>
                        <group name="message_info" string="Message">
                            <field name="message_type"/>
                            <field name="template_id" 
                                   options="{'no_create': True}"
                                   placeholder="Select template (optional)..."/>
                            <field name="quoted_message_id" 
                                   invisible="1"/>
                        </group>
                    </group>
                    
                    <div class="oe_clear"/>
                    <separator string="Message Content"/>
                    <field name="message_content"
                           nolabel="1"
                           placeholder="Type your message here..."
                           widget="text"
                           style="min-height: 150px; width: 100%;"/>
                    <div class="oe_clear"/>
                    
                    <group name="attachments" string="Attachments" 
                           invisible="message_type == 'text'">
                        <field name="attachment_ids" 
                               widget="many2many_binary" 
                               nolabel="1"/>
                    </group>
                    
                    <group name="options" string="Options">
                        <group>
                            <field name="schedule_send" widget="boolean_toggle"/>
                            <field name="scheduled_date" 
                                   invisible="not schedule_send"
                                   required="schedule_send"/>
                        </group>
                        <group>
                            <field name="create_payment_record" widget="boolean_toggle"/>
                            <field name="payment_amount" 
                                   invisible="not create_payment_record"
                                   widget="monetary"/>
                            <field name="currency_id" 
                                   invisible="not create_payment_record"/>
                        </group>
                    </group>
                </sheet>
                
                <footer>
                    <button name="action_preview_message" type="object" 
                            string="Preview" class="btn-secondary"
                            icon="fa-eye"/>
                    <button name="action_send_message" type="object" 
                            string="Send Message" class="btn-primary"
                            icon="fa-paper-plane"/>
                    <button string="Cancel" class="btn-secondary" 
                            special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- WhatsApp Send Message Action -->
    <record id="action_whatsapp_send_message" model="ir.actions.act_window">
        <field name="name">Send WhatsApp Message</field>
        <field name="res_model">whatsapp.send.message</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_whatsapp_send_message_form"/>
    </record>

</odoo>
