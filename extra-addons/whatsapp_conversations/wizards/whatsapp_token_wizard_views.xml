<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WhatsApp Token Wizard Form View -->
        <record id="view_whatsapp_token_wizard_form" model="ir.ui.view">
            <field name="name">whatsapp.token.wizard.form</field>
            <field name="model">whatsapp.token.wizard</field>
            <field name="arch" type="xml">
                <form string="Configure API Token">
                    <div class="alert alert-info" role="alert">
                        <strong>API Token Configuration</strong><br/>
                        We've detected an API token from your WhatsApp service configuration.
                        Please review and confirm the token below.
                    </div>
                    
                    <group>
                        <field name="instance_id" readonly="1"/>
                        <field name="config_file" readonly="1" invisible="not config_file"/>
                    </group>
                    
                    <group string="Detected Token">
                        <field name="detected_token" readonly="1" invisible="not detected_token"/>
                        <div class="alert alert-warning" role="alert" invisible="detected_token">
                            <strong>No token detected automatically.</strong><br/>
                            Please enter your API token manually below.
                        </div>
                    </group>
                    
                    <group string="API Token Configuration">
                        <field name="api_token" password="True" placeholder="Enter your API token here"/>
                        <field name="confirm_token"/>
                    </group>
                    
                    <div class="alert alert-info" role="alert">
                        <strong>Security Note:</strong><br/>
                        Your API token will be stored securely and encrypted in the database.
                        Only authorized users can view or modify this token.
                    </div>
                    
                    <footer>
                        <button name="action_test_token" type="object" string="Test Token" class="btn-secondary"/>
                        <button name="action_set_token" type="object" string="Set Token" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- WhatsApp Token Wizard Action -->
        <record id="action_whatsapp_token_wizard" model="ir.actions.act_window">
            <field name="name">Configure API Token</field>
            <field name="res_model">whatsapp.token.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
