<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- WhatsApp Token Wizard Form View -->
        <record id="view_whatsapp_token_wizard_form" model="ir.ui.view">
            <field name="name">whatsapp.token.wizard.form</field>
            <field name="model">whatsapp.token.wizard</field>
            <field name="arch" type="xml">
                <form string="Get API Token">
                    <div class="alert alert-info" role="alert">
                        <strong>JWT Token Required</strong><br/>
                        To configure webhooks and sync messages, you need a JWT authentication token from your WhatsApp Dashboard.
                    </div>

                    <group>
                        <field name="instance_id" readonly="1"/>
                        <field name="dashboard_url" readonly="1"/>
                    </group>

                    <div class="alert alert-warning" role="alert">
                        <strong>Step 1: Open WhatsApp Dashboard</strong><br/>
                        Click the button below to open your WhatsApp Dashboard in a new tab.
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button name="action_open_dashboard" type="object" string="Open WhatsApp Dashboard" class="btn-primary btn-lg" icon="fa-external-link"/>
                    </div>

                    <div class="alert alert-info" role="alert">
                        <strong>Step 2: Login and Get JWT Token</strong><br/>
                        1. In the dashboard, login with credentials: <code>919999999999</code> / <code>admin123</code><br/>
                        2. After login, look for JWT token in:<br/>
                        &#160;&#160;• Profile/Settings section<br/>
                        &#160;&#160;• API Token or Developer section<br/>
                        &#160;&#160;• User profile dropdown<br/>
                        3. Copy your JWT authentication token<br/>
                        4. Paste the token in the field below<br/>
                        <br/>
                        <strong>Note:</strong> If you don't see the token, you may need to generate one or check the API documentation.
                    </div>

                    <group string="JWT Token Configuration">
                        <field name="api_token" password="True" placeholder="Paste your JWT token here"/>
                        <field name="confirm_token"/>
                    </group>

                    <div class="alert alert-success" role="alert">
                        <strong>Security Note:</strong><br/>
                        Your API token will be stored securely in Odoo and used only for WhatsApp service communication.
                    </div>

                    <footer>
                        <button name="action_test_token" type="object" string="Test Token" class="btn-secondary"/>
                        <button name="action_set_token" type="object" string="Set Token" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- WhatsApp Token Wizard Action -->
        <record id="action_whatsapp_token_wizard" model="ir.actions.act_window">
            <field name="name">Configure API Token</field>
            <field name="res_model">whatsapp.token.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>
