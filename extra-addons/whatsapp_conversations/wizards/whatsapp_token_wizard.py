# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class WhatsAppTokenWizard(models.TransientModel):
    _name = 'whatsapp.token.wizard'
    _description = 'WhatsApp API Token Configuration Wizard'

    instance_id = fields.Many2one('whatsapp.instance', string='WhatsApp Instance', required=True)
    detected_token = fields.Char('Detected API Token', readonly=True)
    config_file = fields.Char('Configuration File', readonly=True)
    api_token = fields.Char('API Token', required=True)
    confirm_token = fields.Bo<PERSON>an('I confirm this token is correct', default=False)

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        if 'detected_token' in self.env.context:
            res['api_token'] = self.env.context['detected_token']
        return res

    def action_set_token(self):
        """Set the API token for the instance"""
        self.ensure_one()
        
        if not self.confirm_token:
            raise UserError(_('Please confirm that the API token is correct before proceeding.'))
        
        if not self.api_token:
            raise UserError(_('API Token is required.'))
        
        # Update the instance with the API token
        self.instance_id.write({
            'api_token': self.api_token
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('API Token has been set successfully. You can now configure webhooks and sync messages.'),
                'type': 'success',
            }
        }

    def action_test_token(self):
        """Test the API token by making a request to the service"""
        self.ensure_one()
        
        if not self.api_token:
            raise UserError(_('Please enter an API token to test.'))
        
        try:
            import requests
            
            # Test the token with a simple API call
            url = f"{self.instance_id.api_url}/instances"
            headers = {
                'Authorization': f'Bearer {self.api_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Token Valid'),
                        'message': _('API Token is valid and working correctly.'),
                        'type': 'success',
                    }
                }
            elif response.status_code == 401:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Invalid Token'),
                        'message': _('API Token is invalid. Please check the token and try again.'),
                        'type': 'danger',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Token Test'),
                        'message': _('Token test returned status %s. This may be normal depending on your service configuration.') % response.status_code,
                        'type': 'info',
                    }
                }
                
        except Exception as e:
            raise UserError(_('Error testing API token: %s') % str(e))
