# -*- coding: utf-8 -*-
{
    'name': 'WhatsApp Conversations',
    'version': '********.0',
    'category': 'Communication',
    'summary': 'WhatsApp Conversations Management with Payment Tracking',
    'description': """
WhatsApp Conversations Management
=================================

This module provides comprehensive WhatsApp conversation management with:

Features:
---------
* Store and manage all WhatsApp conversations
* Real-time message synchronization with WhatsApp service
* Contact-wise conversation history
* Payment tracking for each contact
* Direct messaging from Odoo to WhatsApp
* Comprehensive reporting and analytics
* Message status tracking (sent, delivered, read)
* Media file management
* Conversation search and filtering

Technical Features:
------------------
* Integration with WhatsApp Business API
* Secure payment record management
* Advanced reporting with charts and graphs
* Real-time notifications
* Message templates management
* Bulk messaging capabilities
* Contact synchronization
* Message scheduling

Security:
---------
* Encrypted payment data storage
* Role-based access control
* Audit trail for all operations
* Data privacy compliance
    """,
    'author': 'Arihant AI Solutions',
    'website': 'https://arihantai.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'contacts',
        'mail',
        'web',
        'portal',
        'account',
        'sale',
    ],
    'data': [
        # Security
        'security/whatsapp_security.xml',
        # 'security/ir.model.access.csv',  # Temporarily disabled

        # Data
        'data/default_access.xml',
        # 'data/whatsapp_data.xml',  # Temporarily disabled
        # 'data/message_templates.xml',  # Temporarily disabled
        
        # Views - Temporarily disabled for initial installation
        # 'views/whatsapp_conversation_views.xml',
        # 'views/whatsapp_message_views.xml',
        # 'views/whatsapp_payment_views.xml',
        # 'views/res_partner_views.xml',
        # 'views/whatsapp_template_views.xml',
        # 'views/whatsapp_instance_views.xml',

        # Reports - Temporarily disabled
        # 'reports/whatsapp_conversation_reports.xml',
        # 'reports/whatsapp_payment_reports.xml',
        # 'reports/whatsapp_analytics_reports.xml',

        # Wizards - Temporarily disabled
        # 'wizards/whatsapp_send_message_views.xml',
        # 'wizards/whatsapp_bulk_message_views.xml',
        # 'wizards/whatsapp_payment_wizard_views.xml',

        # Menu
        'views/whatsapp_menu.xml',
    ],
    # 'assets': {
    #     'web.assets_backend': [
    #         'whatsapp_conversations/static/src/css/whatsapp_conversations.css',
    #         'whatsapp_conversations/static/src/js/whatsapp_conversations.js',
    #         'whatsapp_conversations/static/src/js/whatsapp_widget.js',
    #     ],
    #     'web.assets_frontend': [
    #         'whatsapp_conversations/static/src/css/whatsapp_portal.css',
    #     ],
    # },
    'demo': [
        'demo/whatsapp_demo.xml',
    ],
    'images': [
        'static/description/banner.png',
        'static/description/icon.png',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    'external_dependencies': {
        'python': ['requests', 'cryptography'],
    },
}
