<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Payment Report Action -->
    <record id="action_whatsapp_payment_report" model="ir.actions.act_window">
        <field name="name">Payment Analytics</field>
        <field name="res_model">whatsapp.payment.report</field>
        <field name="view_mode">graph,pivot,tree</field>
        <field name="search_view_id" ref="view_whatsapp_payment_report_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No payment data found!
            </p>
            <p>
                Payment analytics will appear here once you have WhatsApp payments.
            </p>
        </field>
    </record>

    <!-- WhatsApp Payment Report Tree View -->
    <record id="view_whatsapp_payment_report_tree" model="ir.ui.view">
        <field name="name">whatsapp.payment.report.tree</field>
        <field name="model">whatsapp.payment.report</field>
        <field name="arch" type="xml">
            <tree string="Payment Analytics" create="false" edit="false">
                <field name="date"/>
                <field name="instance_id"/>
                <field name="partner_id"/>
                <field name="payment_method"/>
                <field name="payment_count"/>
                <field name="total_amount" widget="monetary"/>
                <field name="currency_id" invisible="1"/>
                <field name="confirmed_count"/>
                <field name="pending_count"/>
                <field name="failed_count"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Payment Report Graph View -->
    <record id="view_whatsapp_payment_report_graph" model="ir.ui.view">
        <field name="name">whatsapp.payment.report.graph</field>
        <field name="model">whatsapp.payment.report</field>
        <field name="arch" type="xml">
            <graph string="Payment Analytics" type="bar">
                <field name="date" type="row"/>
                <field name="total_amount" type="measure"/>
                <field name="payment_count" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- WhatsApp Payment Report Pivot View -->
    <record id="view_whatsapp_payment_report_pivot" model="ir.ui.view">
        <field name="name">whatsapp.payment.report.pivot</field>
        <field name="model">whatsapp.payment.report</field>
        <field name="arch" type="xml">
            <pivot string="Payment Analytics">
                <field name="date" type="row"/>
                <field name="payment_method" type="col"/>
                <field name="total_amount" type="measure"/>
                <field name="payment_count" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- WhatsApp Payment Report Search View -->
    <record id="view_whatsapp_payment_report_search" model="ir.ui.view">
        <field name="name">whatsapp.payment.report.search</field>
        <field name="model">whatsapp.payment.report</field>
        <field name="arch" type="xml">
            <search string="Search Payment Analytics">
                <field name="instance_id"/>
                <field name="partner_id"/>
                <field name="payment_method"/>
                
                <filter string="Today" name="today" 
                        domain="[('date', '=', context_today())]"/>
                <filter string="This Week" name="this_week" 
                        domain="[('date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" 
                        domain="[('date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Date" name="group_date" 
                            context="{'group_by': 'date'}"/>
                    <filter string="Instance" name="group_instance" 
                            context="{'group_by': 'instance_id'}"/>
                    <filter string="Payment Method" name="group_method" 
                            context="{'group_by': 'payment_method'}"/>
                    <filter string="Contact" name="group_partner" 
                            context="{'group_by': 'partner_id'}"/>
                </group>
            </search>
        </field>
    </record>

</odoo>
