<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Conversation Report Action -->
    <record id="action_whatsapp_conversation_report" model="ir.actions.act_window">
        <field name="name">Conversation Analytics</field>
        <field name="res_model">whatsapp.conversation.report</field>
        <field name="view_mode">graph,pivot,tree</field>
        <field name="search_view_id" ref="view_whatsapp_conversation_report_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No conversation data found!
            </p>
            <p>
                Conversation analytics will appear here once you have WhatsApp conversations.
            </p>
        </field>
    </record>

    <!-- WhatsApp Conversation Report Tree View -->
    <record id="view_whatsapp_conversation_report_tree" model="ir.ui.view">
        <field name="name">whatsapp.conversation.report.tree</field>
        <field name="model">whatsapp.conversation.report</field>
        <field name="arch" type="xml">
            <tree string="Conversation Analytics" create="false" edit="false">
                <field name="date"/>
                <field name="instance_id"/>
                <field name="partner_id"/>
                <field name="conversation_count"/>
                <field name="message_count"/>
                <field name="incoming_count"/>
                <field name="outgoing_count"/>
                <field name="unread_count"/>
                <field name="response_time_avg"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Conversation Report Graph View -->
    <record id="view_whatsapp_conversation_report_graph" model="ir.ui.view">
        <field name="name">whatsapp.conversation.report.graph</field>
        <field name="model">whatsapp.conversation.report</field>
        <field name="arch" type="xml">
            <graph string="Conversation Analytics" type="line">
                <field name="date" type="row"/>
                <field name="message_count" type="measure"/>
                <field name="conversation_count" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- WhatsApp Conversation Report Pivot View -->
    <record id="view_whatsapp_conversation_report_pivot" model="ir.ui.view">
        <field name="name">whatsapp.conversation.report.pivot</field>
        <field name="model">whatsapp.conversation.report</field>
        <field name="arch" type="xml">
            <pivot string="Conversation Analytics">
                <field name="date" type="row"/>
                <field name="instance_id" type="col"/>
                <field name="message_count" type="measure"/>
                <field name="conversation_count" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- WhatsApp Conversation Report Search View -->
    <record id="view_whatsapp_conversation_report_search" model="ir.ui.view">
        <field name="name">whatsapp.conversation.report.search</field>
        <field name="model">whatsapp.conversation.report</field>
        <field name="arch" type="xml">
            <search string="Search Conversation Analytics">
                <field name="instance_id"/>
                <field name="partner_id"/>
                
                <filter string="Today" name="today" 
                        domain="[('date', '=', context_today())]"/>
                <filter string="This Week" name="this_week" 
                        domain="[('date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" 
                        domain="[('date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Date" name="group_date" 
                            context="{'group_by': 'date'}"/>
                    <filter string="Instance" name="group_instance" 
                            context="{'group_by': 'instance_id'}"/>
                    <filter string="Contact" name="group_partner" 
                            context="{'group_by': 'partner_id'}"/>
                </group>
            </search>
        </field>
    </record>

</odoo>
