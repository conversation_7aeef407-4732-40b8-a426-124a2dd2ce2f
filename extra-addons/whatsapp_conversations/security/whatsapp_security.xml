<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Conversations Security Groups -->
    
    <!-- Base WhatsApp User Group -->
    <record id="group_whatsapp_user" model="res.groups">
        <field name="name">WhatsApp User</field>
        <field name="category_id" ref="base.module_category_communication"/>
        <field name="comment">Basic WhatsApp conversation access</field>
    </record>
    
    <!-- WhatsApp Manager Group -->
    <record id="group_whatsapp_manager" model="res.groups">
        <field name="name">WhatsApp Manager</field>
        <field name="category_id" ref="base.module_category_communication"/>
        <field name="implied_ids" eval="[(4, ref('group_whatsapp_user'))]"/>
        <field name="comment">Full WhatsApp conversation management</field>
    </record>
    
    <!-- WhatsApp Payment Manager Group -->
    <record id="group_whatsapp_payment_manager" model="res.groups">
        <field name="name">WhatsApp Payment Manager</field>
        <field name="category_id" ref="base.module_category_accounting"/>
        <field name="implied_ids" eval="[(4, ref('group_whatsapp_manager'))]"/>
        <field name="comment">WhatsApp payment records management</field>
    </record>
    
    <!-- WhatsApp System Administrator Group -->
    <record id="group_whatsapp_admin" model="res.groups">
        <field name="name">WhatsApp System Administrator</field>
        <field name="category_id" ref="base.module_category_administration"/>
        <field name="implied_ids" eval="[(4, ref('group_whatsapp_payment_manager'))]"/>
        <field name="comment">Full WhatsApp system administration</field>
    </record>

    <!-- Record Rules for WhatsApp Conversations -->
    
    <!-- Users can only see their own conversations or public ones -->
    <record id="whatsapp_conversation_user_rule" model="ir.rule">
        <field name="name">WhatsApp Conversation User Access</field>
        <field name="model_id" ref="model_whatsapp_conversation"/>
        <field name="domain_force">[
            '|',
            ('user_id', '=', user.id),
            ('is_public', '=', True)
        ]</field>
        <field name="groups" eval="[(4, ref('group_whatsapp_user'))]"/>
    </record>
    
    <!-- Managers can see all conversations -->
    <record id="whatsapp_conversation_manager_rule" model="ir.rule">
        <field name="name">WhatsApp Conversation Manager Access</field>
        <field name="model_id" ref="model_whatsapp_conversation"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_whatsapp_manager'))]"/>
    </record>
    
    <!-- Payment Records Security -->
    <record id="whatsapp_payment_manager_rule" model="ir.rule">
        <field name="name">WhatsApp Payment Manager Access</field>
        <field name="model_id" ref="model_whatsapp_payment"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_whatsapp_payment_manager'))]"/>
    </record>
    
    <!-- Users can only see payments they created -->
    <record id="whatsapp_payment_user_rule" model="ir.rule">
        <field name="name">WhatsApp Payment User Access</field>
        <field name="model_id" ref="model_whatsapp_payment"/>
        <field name="domain_force">[('create_uid', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('group_whatsapp_user'))]"/>
    </record>

    <!-- Message Security Rules -->
    <record id="whatsapp_message_user_rule" model="ir.rule">
        <field name="name">WhatsApp Message User Access</field>
        <field name="model_id" ref="model_whatsapp_message"/>
        <field name="domain_force">[
            '|',
            ('conversation_id.user_id', '=', user.id),
            ('conversation_id.is_public', '=', True)
        ]</field>
        <field name="groups" eval="[(4, ref('group_whatsapp_user'))]"/>
    </record>
    
    <record id="whatsapp_message_manager_rule" model="ir.rule">
        <field name="name">WhatsApp Message Manager Access</field>
        <field name="model_id" ref="model_whatsapp_message"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval="[(4, ref('group_whatsapp_manager'))]"/>
    </record>

</odoo>
