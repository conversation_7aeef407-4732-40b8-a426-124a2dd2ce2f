# -*- coding: utf-8 -*-

import json
import logging
from odoo import http, _
from odoo.http import request

_logger = logging.getLogger(__name__)


class WhatsAppWebhook(http.Controller):
    
    @http.route(['/whatsapp/webhook', '/whatsapp/webhook/<string:session_id>'], type='json', auth='none', methods=['POST'], csrf=False)
    def whatsapp_webhook(self, session_id=None, **kwargs):
        """
        Webhook endpoint for real-time WhatsApp message receiving
        
        Expected payload format:
        {
            "type": "message",
            "instance_id": "8487921219",
            "message": {
                "id": "message_id",
                "from": "<EMAIL>",
                "to": "<EMAIL>",
                "body": "Hello from WhatsApp",
                "type": "text",
                "timestamp": 1640995200,
                "fromMe": false
            }
        }
        """
        try:
            data = request.jsonrequest
            _logger.info(f"WhatsApp webhook received: {data}")
            
            if not data:
                return {'status': 'error', 'message': 'No data received'}
            
            # Validate required fields
            if not data.get('type') or not data.get('instance_id'):
                return {'status': 'error', 'message': 'Missing required fields'}
            
            # Process different webhook types
            if data.get('type') == 'message':
                return self._process_message_webhook(data)
            elif data.get('type') == 'status':
                return self._process_status_webhook(data)
            elif data.get('type') == 'qr':
                return self._process_qr_webhook(data)
            else:
                _logger.warning(f"Unknown webhook type: {data.get('type')}")
                return {'status': 'ignored', 'message': 'Unknown webhook type'}
                
        except Exception as e:
            _logger.error(f"Error processing WhatsApp webhook: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _process_message_webhook(self, data):
        """Process incoming message webhook"""
        try:
            instance_id = data.get('instance_id')
            message_data = data.get('message', {})
            
            # Find the WhatsApp instance
            instance = request.env['whatsapp.instance'].sudo().search([
                ('session_id', '=', instance_id)
            ], limit=1)
            
            if not instance:
                _logger.warning(f"WhatsApp instance not found: {instance_id}")
                return {'status': 'error', 'message': 'Instance not found'}
            
            # Skip outgoing messages (sent from this system)
            if message_data.get('fromMe', False):
                return {'status': 'ignored', 'message': 'Outgoing message ignored'}
            
            # Extract phone number
            from_number = message_data.get('from', '').replace('@c.us', '')
            if not from_number:
                return {'status': 'error', 'message': 'Invalid phone number'}
            
            # Get or create conversation
            conversation = request.env['whatsapp.conversation'].sudo().create_or_get_conversation(
                phone_number=from_number,
                instance_id=instance.id
            )
            
            # Check if message already exists
            existing_msg = request.env['whatsapp.message'].sudo().search([
                ('message_id', '=', message_data.get('id')),
                ('conversation_id', '=', conversation.id)
            ], limit=1)
            
            if existing_msg:
                return {'status': 'ignored', 'message': 'Message already exists'}
            
            # Create new message
            message_vals = {
                'conversation_id': conversation.id,
                'direction': 'incoming',
                'content': message_data.get('body', ''),
                'message_type': self._map_message_type(message_data.get('type', 'text')),
                'message_id': message_data.get('id'),
                'date_sent': self._convert_timestamp(message_data.get('timestamp')),
                'status': 'delivered',
            }
            
            # Handle different message types
            if message_data.get('type') == 'location':
                message_vals.update({
                    'latitude': message_data.get('latitude'),
                    'longitude': message_data.get('longitude'),
                    'location_name': message_data.get('location_name'),
                    'location_address': message_data.get('location_address'),
                })
            elif message_data.get('type') == 'contact':
                message_vals.update({
                    'contact_name': message_data.get('contact_name'),
                    'contact_phone': message_data.get('contact_phone'),
                    'contact_vcard': message_data.get('contact_vcard'),
                })
            
            message = request.env['whatsapp.message'].sudo().create(message_vals)
            
            # Handle media attachments
            if message_data.get('media'):
                self._create_attachment(message, message_data['media'])
            
            _logger.info(f"WhatsApp message created: {message.id}")
            return {'status': 'success', 'message_id': message.id}
            
        except Exception as e:
            _logger.error(f"Error processing message webhook: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _process_status_webhook(self, data):
        """Process status update webhook"""
        try:
            instance_id = data.get('instance_id')
            status_data = data.get('status', {})
            
            # Find the WhatsApp instance
            instance = request.env['whatsapp.instance'].sudo().search([
                ('session_id', '=', instance_id)
            ], limit=1)
            
            if not instance:
                return {'status': 'error', 'message': 'Instance not found'}
            
            # Update instance status
            instance_status = status_data.get('status', 'unknown')
            if instance_status == 'connected':
                instance.status = 'connected'
            elif instance_status in ['qr_ready', 'connecting']:
                instance.status = 'connecting'
            else:
                instance.status = 'disconnected'
            
            _logger.info(f"WhatsApp instance {instance_id} status updated to: {instance_status}")
            return {'status': 'success', 'message': 'Status updated'}
            
        except Exception as e:
            _logger.error(f"Error processing status webhook: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _process_qr_webhook(self, data):
        """Process QR code webhook"""
        try:
            instance_id = data.get('instance_id')
            qr_data = data.get('qr', {})
            
            # Find the WhatsApp instance
            instance = request.env['whatsapp.instance'].sudo().search([
                ('session_id', '=', instance_id)
            ], limit=1)
            
            if not instance:
                return {'status': 'error', 'message': 'Instance not found'}
            
            # Update instance status to connecting when QR is ready
            if qr_data.get('qr'):
                instance.status = 'connecting'
            
            _logger.info(f"WhatsApp instance {instance_id} QR code updated")
            return {'status': 'success', 'message': 'QR updated'}
            
        except Exception as e:
            _logger.error(f"Error processing QR webhook: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _map_message_type(self, whatsapp_type):
        """Map WhatsApp message type to Odoo message type"""
        type_mapping = {
            'text': 'text',
            'image': 'image',
            'video': 'video',
            'audio': 'audio',
            'voice': 'voice',
            'document': 'document',
            'location': 'location',
            'contact': 'contact',
            'sticker': 'sticker',
        }
        return type_mapping.get(whatsapp_type, 'text')
    
    def _convert_timestamp(self, timestamp):
        """Convert timestamp to Odoo datetime"""
        if not timestamp:
            return request.env['whatsapp.message']._fields['date_sent'].default()
        
        try:
            from datetime import datetime
            return datetime.fromtimestamp(int(timestamp))
        except (ValueError, TypeError):
            return request.env['whatsapp.message']._fields['date_sent'].default()
    
    def _create_attachment(self, message, media_data):
        """Create attachment for media message"""
        try:
            attachment_vals = {
                'message_id': message.id,
                'filename': media_data.get('filename', 'media_file'),
                'file_data': media_data.get('data'),
                'mimetype': media_data.get('mimetype'),
                'file_size': len(media_data.get('data', '')),
            }
            
            request.env['whatsapp.attachment'].sudo().create(attachment_vals)
            
        except Exception as e:
            _logger.error(f"Error creating attachment: {e}")
    
    @http.route('/whatsapp/webhook/test', type='http', auth='none', methods=['GET'], csrf=False)
    def webhook_test(self, **kwargs):
        """Test endpoint to verify webhook is working"""
        return "WhatsApp Webhook is working!"

    @http.route(['/whatsapp/webhook/debug', '/whatsapp/webhook/debug/<string:session_id>'],
               type='http', auth='none', methods=['POST', 'GET'], csrf=False)
    def webhook_debug(self, session_id=None, **kwargs):
        """Debug endpoint to see what data is being sent"""
        try:
            if request.httprequest.method == 'GET':
                return "WhatsApp Webhook Debug Endpoint - Ready to receive POST data"

            # Log all incoming data for debugging
            content_type = request.httprequest.content_type
            raw_data = request.httprequest.get_data()

            _logger.info(f"=== WEBHOOK DEBUG ===")
            _logger.info(f"Session ID: {session_id}")
            _logger.info(f"Content-Type: {content_type}")
            _logger.info(f"Raw Data: {raw_data}")
            _logger.info(f"Form Data: {request.httprequest.form}")
            _logger.info(f"Args: {request.httprequest.args}")

            if content_type and 'json' in content_type:
                try:
                    json_data = request.jsonrequest
                    _logger.info(f"JSON Data: {json_data}")
                except:
                    _logger.info("Failed to parse JSON")

            return "Webhook data logged successfully"

        except Exception as e:
            _logger.error(f"Webhook debug error: {e}")
            return f"Error: {e}"
