# -*- coding: utf-8 -*-

import json
import logging
from odoo import http, _
from odoo.http import request

_logger = logging.getLogger(__name__)


class WhatsAppWebhook(http.Controller):
    
    @http.route(['/whatsapp/webhook', '/whatsapp/webhook/<string:session_id>'], type='http', auth='none', methods=['POST'], csrf=False)
    def whatsapp_webhook(self, session_id=None, **kwargs):
        """
        Webhook endpoint for real-time WhatsApp message receiving

        Expected payload format:
        {
            "sessionId": "8487921219",
            "messageId": "message_id",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "Hello from WhatsApp",
            "type": "chat",
            "timestamp": "2025-06-19T10:00:00.000Z",
            "direction": "incoming"
        }
        """
        try:
            # Parse JSON data from request
            try:
                data = json.loads(request.httprequest.get_data().decode('utf-8'))
            except:
                data = kwargs

            _logger.info(f"WhatsApp webhook received: {data}")

            if not data:
                return json.dumps({'status': 'error', 'message': 'No data received'})

            # Use session_id from URL or data
            instance_id = session_id or data.get('sessionId') or data.get('instance_id')

            if not instance_id:
                return json.dumps({'status': 'error', 'message': 'Missing session ID'})

            # Process different webhook types based on the actual service format
            if data.get('type') == 'message':
                # Handle both data formats from the WhatsApp service
                if data.get('data'):
                    # Format 1: {'type': 'message', 'data': {...}}
                    message_data = data['data']
                    message_data.update({
                        'messageId': message_data.get('messageId'),
                        'from': message_data.get('from'),
                        'to': message_data.get('to'),
                        'body': message_data.get('body'),
                        'type': message_data.get('type'),
                        'timestamp': message_data.get('timestamp'),
                        'direction': message_data.get('direction', 'incoming')
                    })
                elif data.get('message'):
                    # Format 2: {'type': 'message', 'message': {...}}
                    message_data = data['message']
                    message_data.update({
                        'messageId': message_data.get('id'),
                        'from': message_data.get('from'),
                        'to': message_data.get('to'),
                        'body': message_data.get('body'),
                        'type': message_data.get('type'),
                        'timestamp': message_data.get('timestamp'),
                        'direction': 'outgoing' if message_data.get('fromMe') else 'incoming'
                    })
                else:
                    # Direct message format
                    message_data = data

                return json.dumps(self._process_message_webhook(message_data, instance_id))
            elif data.get('messageId') or data.get('from'):
                # Direct message format (fallback)
                return json.dumps(self._process_message_webhook(data, instance_id))
            elif data.get('type') == 'status':
                return json.dumps(self._process_status_webhook(data, instance_id))
            elif data.get('type') == 'qr':
                return json.dumps(self._process_qr_webhook(data, instance_id))
            else:
                _logger.warning(f"Unknown webhook data: {data}")
                return json.dumps({'status': 'success', 'message': 'Webhook received'})

        except Exception as e:
            _logger.error(f"Error processing WhatsApp webhook: {e}")
            return json.dumps({'status': 'error', 'message': str(e)})
    
    def _process_message_webhook(self, data, instance_id):
        """Process incoming message webhook"""
        try:
            # Find the WhatsApp instance
            instance = request.env['whatsapp.instance'].sudo().search([
                ('session_id', '=', instance_id)
            ], limit=1)

            if not instance:
                _logger.warning(f"WhatsApp instance not found: {instance_id}")
                return {'status': 'error', 'message': 'Instance not found'}

            # Process both incoming and outgoing messages (including bot messages)
            # Only skip if explicitly marked as system-generated
            is_outgoing = data.get('direction') == 'outgoing' or data.get('fromMe', False)

            # Don't skip outgoing messages - we want to see bot messages in Odoo
            # if is_outgoing:
            #     return {'status': 'ignored', 'message': 'Outgoing message ignored'}

            # Skip status messages (WhatsApp status updates)
            from_field = data.get('from', '')
            if 'status@broadcast' in from_field:
                return {'status': 'ignored', 'message': 'Status message ignored'}

            # Determine direction and contact phone number
            to_field = data.get('to', '')
            is_outgoing = data.get('direction') == 'outgoing' or data.get('fromMe', False)

            if is_outgoing:
                # For outgoing messages, the contact is in the 'to' field
                contact_number = to_field.replace('@c.us', '')
                direction = 'outgoing'
            else:
                # For incoming messages, the contact is in the 'from' field
                contact_number = from_field.replace('@c.us', '')
                direction = 'incoming'

            if not contact_number:
                return {'status': 'error', 'message': 'Invalid phone number'}

            # Get or create conversation
            conversation = request.env['whatsapp.conversation'].sudo().create_or_get_conversation(
                phone_number=contact_number,
                instance_id=instance.id
            )

            # Check if message already exists
            message_id = data.get('messageId') or data.get('id')
            if message_id:
                existing_msg = request.env['whatsapp.message'].sudo().search([
                    ('message_id', '=', message_id),
                    ('conversation_id', '=', conversation.id)
                ], limit=1)

                if existing_msg:
                    return {'status': 'ignored', 'message': 'Message already exists'}

            # Create new message
            message_vals = {
                'conversation_id': conversation.id,
                'direction': direction,  # Use calculated direction (incoming/outgoing)
                'content': data.get('body', ''),
                'message_type': self._map_message_type(data.get('type', 'text')),
                'message_id': message_id,
                'date_sent': self._convert_timestamp_iso(data.get('timestamp')),
                'status': 'sent' if is_outgoing else 'delivered',
            }

            # Handle different message types
            if data.get('type') == 'location':
                message_vals.update({
                    'latitude': data.get('latitude'),
                    'longitude': data.get('longitude'),
                    'location_name': data.get('location_name'),
                    'location_address': data.get('location_address'),
                })
            elif data.get('type') == 'contact':
                message_vals.update({
                    'contact_name': data.get('contact_name'),
                    'contact_phone': data.get('contact_phone'),
                    'contact_vcard': data.get('contact_vcard'),
                })

            message = request.env['whatsapp.message'].sudo().create(message_vals)

            # Handle media attachments
            if data.get('media') or data.get('hasMedia'):
                self._create_attachment(message, data)

            _logger.info(f"WhatsApp message created: {message.id}")
            return {'status': 'success', 'message_id': message.id}

        except Exception as e:
            _logger.error(f"Error processing message webhook: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _process_status_webhook(self, data, instance_id):
        """Process status update webhook"""
        try:
            # Find the WhatsApp instance
            instance = request.env['whatsapp.instance'].sudo().search([
                ('session_id', '=', instance_id)
            ], limit=1)

            if not instance:
                return {'status': 'error', 'message': 'Instance not found'}

            # Update instance status
            instance_status = data.get('status', 'unknown')
            if instance_status == 'connected':
                instance.status = 'connected'
            elif instance_status in ['qr_ready', 'connecting']:
                instance.status = 'connecting'
            else:
                instance.status = 'disconnected'

            _logger.info(f"WhatsApp instance {instance_id} status updated to: {instance_status}")
            return {'status': 'success', 'message': 'Status updated'}

        except Exception as e:
            _logger.error(f"Error processing status webhook: {e}")
            return {'status': 'error', 'message': str(e)}

    def _process_qr_webhook(self, data, instance_id):
        """Process QR code webhook"""
        try:
            # Find the WhatsApp instance
            instance = request.env['whatsapp.instance'].sudo().search([
                ('session_id', '=', instance_id)
            ], limit=1)

            if not instance:
                return {'status': 'error', 'message': 'Instance not found'}

            # Update instance status to connecting when QR is ready
            if data.get('qr'):
                instance.status = 'connecting'

            _logger.info(f"WhatsApp instance {instance_id} QR code updated")
            return {'status': 'success', 'message': 'QR updated'}

        except Exception as e:
            _logger.error(f"Error processing QR webhook: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _map_message_type(self, whatsapp_type):
        """Map WhatsApp message type to Odoo message type"""
        type_mapping = {
            'text': 'text',
            'image': 'image',
            'video': 'video',
            'audio': 'audio',
            'voice': 'voice',
            'document': 'document',
            'location': 'location',
            'contact': 'contact',
            'sticker': 'sticker',
        }
        return type_mapping.get(whatsapp_type, 'text')
    
    def _convert_timestamp(self, timestamp):
        """Convert timestamp to Odoo datetime"""
        if not timestamp:
            return request.env['whatsapp.message']._fields['date_sent'].default()

        try:
            from datetime import datetime
            return datetime.fromtimestamp(int(timestamp))
        except (ValueError, TypeError):
            return request.env['whatsapp.message']._fields['date_sent'].default()

    def _convert_timestamp_iso(self, timestamp):
        """Convert ISO timestamp to Odoo datetime"""
        if not timestamp:
            return request.env['whatsapp.message']._fields['date_sent'].default()

        try:
            from datetime import datetime
            # Handle ISO format like "2025-06-19T10:00:00.000Z"
            if isinstance(timestamp, str):
                # Remove milliseconds and Z if present
                timestamp = timestamp.replace('Z', '').split('.')[0]
                return datetime.fromisoformat(timestamp)
            else:
                # Fallback to unix timestamp
                return datetime.fromtimestamp(int(timestamp))
        except (ValueError, TypeError):
            return request.env['whatsapp.message']._fields['date_sent'].default()
    
    def _create_attachment(self, message, media_data):
        """Create attachment for media message"""
        try:
            attachment_vals = {
                'message_id': message.id,
                'filename': media_data.get('filename', 'media_file'),
                'file_data': media_data.get('data'),
                'mimetype': media_data.get('mimetype'),
                'file_size': len(media_data.get('data', '')),
            }
            
            request.env['whatsapp.attachment'].sudo().create(attachment_vals)
            
        except Exception as e:
            _logger.error(f"Error creating attachment: {e}")
    
    @http.route('/whatsapp/webhook/test', type='http', auth='none', methods=['GET'], csrf=False)
    def webhook_test(self, **kwargs):
        """Test endpoint to verify webhook is working"""
        return "WhatsApp Webhook is working!"

    @http.route(['/whatsapp/webhook/debug', '/whatsapp/webhook/debug/<string:session_id>'],
               type='http', auth='none', methods=['POST', 'GET'], csrf=False)
    def webhook_debug(self, session_id=None, **kwargs):
        """Debug endpoint to see what data is being sent"""
        try:
            if request.httprequest.method == 'GET':
                return "WhatsApp Webhook Debug Endpoint - Ready to receive POST data"

            # Log all incoming data for debugging
            content_type = request.httprequest.content_type
            raw_data = request.httprequest.get_data()

            _logger.info(f"=== WEBHOOK DEBUG ===")
            _logger.info(f"Session ID: {session_id}")
            _logger.info(f"Content-Type: {content_type}")
            _logger.info(f"Raw Data: {raw_data}")
            _logger.info(f"Form Data: {request.httprequest.form}")
            _logger.info(f"Args: {request.httprequest.args}")

            if content_type and 'json' in content_type:
                try:
                    json_data = request.jsonrequest
                    _logger.info(f"JSON Data: {json_data}")
                except:
                    _logger.info("Failed to parse JSON")

            return "Webhook data logged successfully"

        except Exception as e:
            _logger.error(f"Webhook debug error: {e}")
            return f"Error: {e}"
