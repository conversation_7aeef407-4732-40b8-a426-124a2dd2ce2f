# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import base64
import mimetypes
import logging

_logger = logging.getLogger(__name__)


class WhatsAppAttachment(models.Model):
    _name = 'whatsapp.attachment'
    _description = 'WhatsApp Message Attachment'
    _order = 'create_date desc'
    _rec_name = 'filename'

    # Basic Information
    filename = fields.Char(
        string='Filename',
        required=True,
        help='Name of the attached file'
    )
    file_data = fields.Binary(
        string='File Data',
        required=True,
        help='Binary data of the file'
    )
    file_size = fields.Integer(
        string='File Size (bytes)',
        help='Size of the file in bytes'
    )
    mimetype = fields.Char(
        string='MIME Type',
        help='MIME type of the file'
    )
    
    # File Type Classification
    file_type = fields.Selection([
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('document', 'Document'),
        ('other', 'Other')
    ], string='File Type', compute='_compute_file_type', store=True)
    
    # Relations
    message_id = fields.Many2one(
        'whatsapp.message',
        string='Message',
        required=True,
        ondelete='cascade'
    )
    conversation_id = fields.Many2one(
        'whatsapp.conversation',
        string='Conversation',
        related='message_id.conversation_id',
        store=True
    )
    
    # WhatsApp Specific
    whatsapp_media_id = fields.Char(
        string='WhatsApp Media ID',
        help='Media ID from WhatsApp'
    )
    caption = fields.Text(
        string='Caption',
        help='Caption for the media file'
    )
    
    # Download Information
    is_downloaded = fields.Boolean(
        string='Downloaded',
        default=False,
        help='Whether the file has been downloaded from WhatsApp'
    )
    download_url = fields.Char(
        string='Download URL',
        help='URL to download the file from WhatsApp'
    )
    download_date = fields.Datetime(
        string='Download Date',
        help='When the file was downloaded'
    )
    
    # Image/Video Specific
    width = fields.Integer(string='Width (pixels)')
    height = fields.Integer(string='Height (pixels)')
    duration = fields.Float(string='Duration (seconds)')
    
    # Document Specific
    page_count = fields.Integer(string='Page Count')
    
    # Audio Specific
    is_voice_note = fields.Boolean(
        string='Voice Note',
        default=False,
        help='Whether this is a voice note'
    )
    
    # Thumbnail
    thumbnail = fields.Binary(
        string='Thumbnail',
        help='Thumbnail image for videos and documents'
    )
    
    @api.depends('mimetype', 'filename')
    def _compute_file_type(self):
        for record in self:
            if not record.mimetype and record.filename:
                # Try to guess mimetype from filename
                mimetype, _ = mimetypes.guess_type(record.filename)
                record.mimetype = mimetype
            
            if record.mimetype:
                if record.mimetype.startswith('image/'):
                    record.file_type = 'image'
                elif record.mimetype.startswith('video/'):
                    record.file_type = 'video'
                elif record.mimetype.startswith('audio/'):
                    record.file_type = 'audio'
                elif record.mimetype in ['application/pdf', 'application/msword', 
                                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                       'application/vnd.ms-excel',
                                       'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                       'text/plain', 'text/csv']:
                    record.file_type = 'document'
                else:
                    record.file_type = 'other'
            else:
                record.file_type = 'other'
    
    @api.model
    def create(self, vals):
        """Override create to set file size and mimetype"""
        if vals.get('file_data') and not vals.get('file_size'):
            try:
                file_data = base64.b64decode(vals['file_data'])
                vals['file_size'] = len(file_data)
            except Exception:
                pass
        
        if vals.get('filename') and not vals.get('mimetype'):
            mimetype, _ = mimetypes.guess_type(vals['filename'])
            if mimetype:
                vals['mimetype'] = mimetype
        
        return super().create(vals)
    
    def action_download_file(self):
        """Download the attachment file"""
        self.ensure_one()
        
        if not self.file_data:
            raise ValidationError(_('No file data available'))
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/whatsapp.attachment/{self.id}/file_data/{self.filename}?download=true',
            'target': 'self',
        }
    
    def action_preview_file(self):
        """Preview the attachment file"""
        self.ensure_one()
        
        if self.file_type == 'image':
            return {
                'type': 'ir.actions.act_window',
                'name': _('Image Preview'),
                'res_model': 'whatsapp.attachment.preview',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_attachment_id': self.id,
                }
            }
        elif self.file_type in ['document', 'other']:
            # For documents, just download
            return self.action_download_file()
        else:
            # For video/audio, open in new tab
            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/whatsapp.attachment/{self.id}/file_data/{self.filename}',
                'target': 'new',
            }
    
    def get_file_icon(self):
        """Get appropriate icon for file type"""
        self.ensure_one()
        
        icon_mapping = {
            'image': 'fa-file-image-o',
            'video': 'fa-file-video-o',
            'audio': 'fa-file-audio-o',
            'document': 'fa-file-text-o',
            'other': 'fa-file-o',
        }
        
        return icon_mapping.get(self.file_type, 'fa-file-o')
    
    def get_human_readable_size(self):
        """Get human readable file size"""
        self.ensure_one()
        
        if not self.file_size:
            return '0 B'
        
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    @api.constrains('file_size')
    def _check_file_size(self):
        """Check file size limits"""
        max_size = 50 * 1024 * 1024  # 50 MB limit
        for record in self:
            if record.file_size and record.file_size > max_size:
                raise ValidationError(_('File size cannot exceed 50 MB'))
    
    def _generate_thumbnail(self):
        """Generate thumbnail for images and videos"""
        self.ensure_one()
        
        if self.file_type not in ['image', 'video']:
            return
        
        try:
            if self.file_type == 'image':
                # Generate image thumbnail
                from PIL import Image
                import io
                
                file_data = base64.b64decode(self.file_data)
                image = Image.open(io.BytesIO(file_data))
                
                # Create thumbnail
                image.thumbnail((200, 200), Image.Resampling.LANCZOS)
                
                # Save thumbnail
                thumbnail_io = io.BytesIO()
                image.save(thumbnail_io, format='JPEG', quality=85)
                thumbnail_data = base64.b64encode(thumbnail_io.getvalue())
                
                self.thumbnail = thumbnail_data
                self.width = image.width
                self.height = image.height
                
        except Exception as e:
            _logger.warning(f"Failed to generate thumbnail for {self.filename}: {e}")
    
    @api.model
    def cleanup_old_attachments(self, days=30):
        """Cleanup old attachments (cron job)"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days)
        old_attachments = self.search([
            ('create_date', '<', cutoff_date),
            ('message_id.conversation_id.status', '=', 'archived')
        ])
        
        _logger.info(f"Cleaning up {len(old_attachments)} old attachments")
        old_attachments.unlink()
        
        return True
