# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class WhatsAppConversation(models.Model):
    _name = 'whatsapp.conversation'
    _description = 'WhatsApp Conversation'
    _order = 'last_message_date desc'
    _rec_name = 'display_name'

    # Basic Information
    name = fields.Char(
        string='Conversation Name',
        compute='_compute_name',
        store=True
    )
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    # Contact Information
    partner_id = fields.Many2one(
        'res.partner',
        string='Contact',
        required=True,
        ondelete='cascade',
        help='Contact associated with this conversation'
    )
    phone_number = fields.Char(
        string='Phone Number',
        required=True,
        help='WhatsApp phone number'
    )
    
    # Instance Information
    instance_id = fields.Many2one(
        'whatsapp.instance',
        string='WhatsApp Instance',
        required=True,
        ondelete='cascade'
    )
    
    # Conversation Status
    status = fields.Selection([
        ('active', 'Active'),
        ('archived', 'Archived'),
        ('blocked', 'Blocked'),
        ('spam', 'Spam')
    ], string='Status', default='active', required=True)
    
    is_public = fields.Boolean(
        string='Public Conversation',
        default=False,
        help='Allow all users to see this conversation'
    )
    
    # Message Statistics
    message_count = fields.Integer(
        string='Message Count',
        compute='_compute_message_statistics',
        store=True
    )
    unread_count = fields.Integer(
        string='Unread Messages',
        compute='_compute_message_statistics',
        store=True
    )
    
    # Timestamps
    first_message_date = fields.Datetime(
        string='First Message',
        compute='_compute_message_statistics',
        store=True
    )
    last_message_date = fields.Datetime(
        string='Last Message',
        compute='_compute_message_statistics',
        store=True
    )
    last_message_preview = fields.Text(
        string='Last Message Preview',
        compute='_compute_message_statistics',
        store=True
    )
    
    # Relations
    message_ids = fields.One2many(
        'whatsapp.message',
        'conversation_id',
        string='Messages'
    )
    payment_ids = fields.One2many(
        'whatsapp.payment',
        'conversation_id',
        string='Payments'
    )
    
    # User Assignment
    user_id = fields.Many2one(
        'res.users',
        string='Assigned User',
        default=lambda self: self.env.user,
        help='User responsible for this conversation'
    )
    
    # Tags and Categories
    tag_ids = fields.Many2many(
        'whatsapp.conversation.tag',
        string='Tags'
    )
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Urgent')
    ], string='Priority', default='1')
    
    # Notes
    notes = fields.Text(
        string='Internal Notes',
        help='Internal notes about this conversation'
    )
    
    @api.depends('partner_id', 'phone_number')
    def _compute_name(self):
        for record in self:
            if record.partner_id:
                record.name = record.partner_id.name
            else:
                record.name = record.phone_number or 'Unknown'
    
    @api.depends('name', 'phone_number', 'instance_id')
    def _compute_display_name(self):
        for record in self:
            name = record.name or 'Unknown'
            phone = record.phone_number or ''
            instance = record.instance_id.name if record.instance_id else ''
            record.display_name = f"{name} ({phone}) - {instance}"
    
    @api.depends('message_ids', 'message_ids.is_read', 'message_ids.date_sent', 'message_ids.content')
    def _compute_message_statistics(self):
        for record in self:
            messages = record.message_ids
            record.message_count = len(messages)
            
            # Unread messages (incoming messages that are not read)
            unread_messages = messages.filtered(lambda m: not m.is_read and m.direction == 'incoming')
            record.unread_count = len(unread_messages)
            
            # First and last message dates
            if messages:
                sorted_messages = messages.sorted('date_sent')
                record.first_message_date = sorted_messages[0].date_sent
                record.last_message_date = sorted_messages[-1].date_sent
                
                # Last message preview
                last_message = sorted_messages[-1]
                if last_message.message_type == 'text':
                    preview = last_message.content[:100]
                    if len(last_message.content) > 100:
                        preview += '...'
                    record.last_message_preview = preview
                else:
                    record.last_message_preview = f"[{last_message.message_type.title()}]"
            else:
                record.first_message_date = False
                record.last_message_date = False
                record.last_message_preview = ''
    
    def action_mark_as_read(self):
        """Mark all messages in conversation as read"""
        self.ensure_one()
        unread_messages = self.message_ids.filtered(lambda m: not m.is_read and m.direction == 'incoming')
        unread_messages.write({'is_read': True})
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('All messages marked as read'),
                'type': 'success',
            }
        }
    
    def action_archive_conversation(self):
        """Archive this conversation"""
        self.ensure_one()
        self.status = 'archived'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Conversation archived'),
                'type': 'success',
            }
        }
    
    def action_send_message(self):
        """Open send message wizard"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Send Message'),
            'res_model': 'whatsapp.send.message',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_conversation_id': self.id,
                'default_partner_id': self.partner_id.id,
                'default_phone_number': self.phone_number,
            }
        }
    
    def action_view_payments(self):
        """View payments for this conversation"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Payments'),
            'res_model': 'whatsapp.payment',
            'view_mode': 'tree,form',
            'domain': [('conversation_id', '=', self.id)],
            'context': {
                'default_conversation_id': self.id,
                'default_partner_id': self.partner_id.id,
            }
        }
    
    @api.model
    def create_or_get_conversation(self, phone_number, instance_id, partner_id=None):
        """Create or get existing conversation for a phone number"""
        # Try to find existing conversation
        conversation = self.search([
            ('phone_number', '=', phone_number),
            ('instance_id', '=', instance_id)
        ], limit=1)
        
        if conversation:
            return conversation
        
        # Create new conversation
        if not partner_id:
            # Try to find partner by phone
            partner = self.env['res.partner'].search([
                '|',
                ('phone', '=', phone_number),
                ('mobile', '=', phone_number)
            ], limit=1)
            
            if not partner:
                # Create new partner
                partner = self.env['res.partner'].create({
                    'name': phone_number,
                    'phone': phone_number,
                    'is_company': False,
                })
            partner_id = partner.id
        
        conversation = self.create({
            'partner_id': partner_id,
            'phone_number': phone_number,
            'instance_id': instance_id,
        })
        
        return conversation


class WhatsAppConversationTag(models.Model):
    _name = 'whatsapp.conversation.tag'
    _description = 'WhatsApp Conversation Tag'
    _order = 'name'

    name = fields.Char(string='Tag Name', required=True)
    color = fields.Integer(string='Color', default=0)
    description = fields.Text(string='Description')
