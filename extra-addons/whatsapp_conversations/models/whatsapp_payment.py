# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from cryptography.fernet import Fernet
import base64
import logging

_logger = logging.getLogger(__name__)


class WhatsAppPayment(models.Model):
    _name = 'whatsapp.payment'
    _description = 'WhatsApp Payment Record'
    _order = 'payment_date desc'
    _rec_name = 'display_name'

    # Basic Information
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    # Payment Details
    amount = fields.Monetary(
        string='Amount',
        required=True,
        currency_field='currency_id'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id,
        required=True
    )
    
    payment_method = fields.Selection([
        ('cash', 'Cash'),
        ('bank_transfer', 'Bank Transfer'),
        ('upi', 'UPI'),
        ('card', 'Credit/Debit Card'),
        ('wallet', 'Digital Wallet'),
        ('cheque', 'Cheque'),
        ('other', 'Other')
    ], string='Payment Method', required=True)
    
    payment_status = fields.Selection([
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('cancelled', 'Cancelled')
    ], string='Payment Status', default='pending', required=True)
    
    # Dates
    payment_date = fields.Datetime(
        string='Payment Date',
        default=fields.Datetime.now,
        required=True
    )
    due_date = fields.Date(
        string='Due Date'
    )
    confirmed_date = fields.Datetime(
        string='Confirmed Date',
        readonly=True
    )
    
    # Relations
    conversation_id = fields.Many2one(
        'whatsapp.conversation',
        string='Conversation',
        required=True,
        ondelete='cascade'
    )
    partner_id = fields.Many2one(
        'res.partner',
        string='Contact',
        related='conversation_id.partner_id',
        store=True
    )
    
    # Payment References
    reference = fields.Char(
        string='Payment Reference',
        help='Internal reference for this payment'
    )
    external_reference = fields.Char(
        string='External Reference',
        help='Bank/UPI/Gateway reference number'
    )
    transaction_id = fields.Char(
        string='Transaction ID',
        help='Transaction ID from payment gateway'
    )
    
    # Encrypted Payment Details
    encrypted_details = fields.Text(
        string='Encrypted Payment Details',
        help='Encrypted sensitive payment information'
    )
    
    # Bank Details (encrypted)
    bank_name = fields.Char(string='Bank Name')
    account_number_encrypted = fields.Text(string='Account Number (Encrypted)')
    ifsc_code = fields.Char(string='IFSC Code')
    
    # UPI Details
    upi_id = fields.Char(string='UPI ID')
    upi_reference = fields.Char(string='UPI Reference')
    
    # Card Details (encrypted)
    card_last_four = fields.Char(string='Card Last 4 Digits')
    card_type = fields.Selection([
        ('credit', 'Credit Card'),
        ('debit', 'Debit Card')
    ], string='Card Type')
    
    # Description and Notes
    description = fields.Text(
        string='Description',
        help='Description of what this payment is for'
    )
    notes = fields.Text(
        string='Internal Notes',
        help='Internal notes about this payment'
    )
    
    # Attachments
    attachment_ids = fields.Many2many(
        'ir.attachment',
        string='Attachments',
        help='Payment receipts, screenshots, etc.'
    )
    
    # User Information
    user_id = fields.Many2one(
        'res.users',
        string='Recorded By',
        default=lambda self: self.env.user,
        required=True
    )
    confirmed_by = fields.Many2one(
        'res.users',
        string='Confirmed By',
        readonly=True
    )
    
    # Security and Audit
    is_verified = fields.Boolean(
        string='Verified',
        default=False,
        help='Whether this payment has been verified'
    )
    verification_notes = fields.Text(
        string='Verification Notes'
    )
    
    @api.depends('amount', 'currency_id', 'partner_id', 'payment_date')
    def _compute_display_name(self):
        for record in self:
            amount_str = f"{record.currency_id.symbol}{record.amount:,.2f}" if record.currency_id else str(record.amount)
            partner_name = record.partner_id.name if record.partner_id else 'Unknown'
            date_str = record.payment_date.strftime('%Y-%m-%d') if record.payment_date else ''
            record.display_name = f"{amount_str} - {partner_name} ({date_str})"
    
    @api.model
    def _get_encryption_key(self):
        """Get or create encryption key for sensitive data"""
        # In production, this should be stored securely (e.g., environment variable)
        # For demo purposes, we'll use a simple approach
        key_param = self.env['ir.config_parameter'].sudo().get_param('whatsapp.encryption_key')
        if not key_param:
            key = Fernet.generate_key()
            self.env['ir.config_parameter'].sudo().set_param('whatsapp.encryption_key', key.decode())
            return key
        return key_param.encode()
    
    def _encrypt_data(self, data):
        """Encrypt sensitive data"""
        if not data:
            return False
        try:
            key = self._get_encryption_key()
            f = Fernet(key)
            encrypted_data = f.encrypt(data.encode())
            return base64.b64encode(encrypted_data).decode()
        except Exception as e:
            _logger.error(f"Encryption error: {e}")
            return False
    
    def _decrypt_data(self, encrypted_data):
        """Decrypt sensitive data"""
        if not encrypted_data:
            return False
        try:
            key = self._get_encryption_key()
            f = Fernet(key)
            decoded_data = base64.b64decode(encrypted_data.encode())
            decrypted_data = f.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            _logger.error(f"Decryption error: {e}")
            return False
    
    def set_account_number(self, account_number):
        """Set encrypted account number"""
        self.account_number_encrypted = self._encrypt_data(account_number)
    
    def get_account_number(self):
        """Get decrypted account number"""
        return self._decrypt_data(self.account_number_encrypted)
    
    def action_confirm_payment(self):
        """Confirm the payment"""
        self.ensure_one()
        if self.payment_status != 'pending':
            raise UserError(_('Only pending payments can be confirmed'))
        
        self.write({
            'payment_status': 'confirmed',
            'confirmed_date': fields.Datetime.now(),
            'confirmed_by': self.env.user.id,
        })
        
        # Send notification message to WhatsApp
        self._send_payment_confirmation()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Payment confirmed successfully'),
                'type': 'success',
            }
        }
    
    def action_mark_failed(self):
        """Mark payment as failed"""
        self.ensure_one()
        self.payment_status = 'failed'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Payment Status Updated'),
                'message': _('Payment marked as failed'),
                'type': 'warning',
            }
        }
    
    def action_verify_payment(self):
        """Verify the payment"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Verify Payment'),
            'res_model': 'whatsapp.payment.verify',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_payment_id': self.id,
            }
        }
    
    def _send_payment_confirmation(self):
        """Send payment confirmation message to WhatsApp"""
        try:
            message_content = _(
                "✅ Payment Confirmed\n\n"
                "Amount: %s %s\n"
                "Reference: %s\n"
                "Date: %s\n\n"
                "Thank you for your payment!"
            ) % (
                self.currency_id.symbol,
                self.amount,
                self.reference or 'N/A',
                self.payment_date.strftime('%Y-%m-%d %H:%M')
            )
            
            # Create outgoing message
            message_vals = {
                'conversation_id': self.conversation_id.id,
                'direction': 'outgoing',
                'content': message_content,
                'message_type': 'text',
                'user_id': self.env.user.id,
            }
            
            message = self.env['whatsapp.message'].create(message_vals)
            message._send_to_whatsapp()
            
        except Exception as e:
            _logger.error(f"Failed to send payment confirmation: {e}")
    
    @api.constrains('amount')
    def _check_amount(self):
        for record in self:
            if record.amount <= 0:
                raise ValidationError(_('Payment amount must be positive'))
    
    @api.model
    def get_payment_summary(self, partner_id=None, date_from=None, date_to=None):
        """Get payment summary for reporting"""
        domain = []
        if partner_id:
            domain.append(('partner_id', '=', partner_id))
        if date_from:
            domain.append(('payment_date', '>=', date_from))
        if date_to:
            domain.append(('payment_date', '<=', date_to))
        
        payments = self.search(domain)
        
        summary = {
            'total_amount': sum(payments.mapped('amount')),
            'confirmed_amount': sum(payments.filtered(lambda p: p.payment_status == 'confirmed').mapped('amount')),
            'pending_amount': sum(payments.filtered(lambda p: p.payment_status == 'pending').mapped('amount')),
            'failed_amount': sum(payments.filtered(lambda p: p.payment_status == 'failed').mapped('amount')),
            'total_count': len(payments),
            'confirmed_count': len(payments.filtered(lambda p: p.payment_status == 'confirmed')),
            'pending_count': len(payments.filtered(lambda p: p.payment_status == 'pending')),
            'failed_count': len(payments.filtered(lambda p: p.payment_status == 'failed')),
        }
        
        return summary
