# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import requests
import json
import logging
import base64

_logger = logging.getLogger(__name__)


class WhatsAppMessage(models.Model):
    _name = 'whatsapp.message'
    _description = 'WhatsApp Message'
    _order = 'date_sent desc'
    _rec_name = 'display_name'

    # Basic Information
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    # Message Content
    content = fields.Text(
        string='Message Content',
        help='Text content of the message'
    )
    message_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('document', 'Document'),
        ('location', 'Location'),
        ('contact', 'Contact'),
        ('sticker', 'Sticker'),
        ('voice', 'Voice Note'),
        ('system', 'System Message')
    ], string='Message Type', default='text', required=True)
    
    # Direction and Status
    direction = fields.Selection([
        ('incoming', 'Incoming'),
        ('outgoing', 'Outgoing')
    ], string='Direction', required=True)
    
    status = fields.Selection([
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
        ('failed', 'Failed')
    ], string='Status', default='pending')
    
    is_read = fields.Boolean(
        string='Is Read',
        default=False,
        help='Whether this message has been read'
    )
    
    # Timestamps
    date_sent = fields.Datetime(
        string='Date Sent',
        default=fields.Datetime.now,
        required=True
    )
    date_delivered = fields.Datetime(
        string='Date Delivered'
    )
    date_read = fields.Datetime(
        string='Date Read'
    )
    
    # Relations
    conversation_id = fields.Many2one(
        'whatsapp.conversation',
        string='Conversation',
        required=True,
        ondelete='cascade'
    )
    partner_id = fields.Many2one(
        'res.partner',
        string='Contact',
        related='conversation_id.partner_id',
        store=True
    )
    instance_id = fields.Many2one(
        'whatsapp.instance',
        string='Instance',
        related='conversation_id.instance_id',
        store=True
    )
    
    # Message Identifiers
    message_id = fields.Char(
        string='WhatsApp Message ID',
        help='Unique message ID from WhatsApp'
    )
    quoted_message_id = fields.Many2one(
        'whatsapp.message',
        string='Quoted Message',
        help='Message being replied to'
    )
    
    # Media and Attachments
    attachment_ids = fields.One2many(
        'whatsapp.attachment',
        'message_id',
        string='Attachments'
    )
    has_media = fields.Boolean(
        string='Has Media',
        compute='_compute_has_media',
        store=True
    )
    
    # Location Data (for location messages)
    latitude = fields.Float(string='Latitude')
    longitude = fields.Float(string='Longitude')
    location_name = fields.Char(string='Location Name')
    location_address = fields.Text(string='Location Address')
    
    # Contact Data (for contact messages)
    contact_name = fields.Char(string='Contact Name')
    contact_phone = fields.Char(string='Contact Phone')
    contact_vcard = fields.Text(string='Contact vCard')
    
    # User Information
    user_id = fields.Many2one(
        'res.users',
        string='Sent By User',
        help='Odoo user who sent this message (for outgoing messages)'
    )
    
    # Error Information
    error_message = fields.Text(
        string='Error Message',
        help='Error details if message failed to send'
    )
    
    @api.depends('content', 'message_type', 'direction', 'date_sent')
    def _compute_display_name(self):
        for record in self:
            direction_icon = '→' if record.direction == 'outgoing' else '←'
            content_preview = ''
            
            if record.message_type == 'text' and record.content:
                content_preview = record.content[:50]
                if len(record.content) > 50:
                    content_preview += '...'
            else:
                content_preview = f'[{record.message_type.title()}]'
            
            date_str = record.date_sent.strftime('%H:%M') if record.date_sent else ''
            record.display_name = f"{direction_icon} {content_preview} ({date_str})"
    
    @api.depends('attachment_ids', 'message_type')
    def _compute_has_media(self):
        for record in self:
            record.has_media = bool(record.attachment_ids) or record.message_type != 'text'

    def action_mark_as_read(self):
        """Mark this message as read"""
        self.ensure_one()
        if not self.is_read and self.direction == 'incoming':
            self.write({
                'is_read': True,
                'date_read': fields.Datetime.now()
            })

    def action_reply_message(self):
        """Reply to this message"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Reply Message'),
            'res_model': 'whatsapp.send.message',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_conversation_id': self.conversation_id.id,
                'default_partner_id': self.partner_id.id,
                'default_phone_number': self.conversation_id.phone_number,
                'default_quoted_message_id': self.id,
            }
        }

    def action_resend_message(self):
        """Resend failed message"""
        self.ensure_one()
        if self.direction == 'outgoing' and self.status == 'failed':
            try:
                self._send_to_whatsapp()
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Message resent successfully'),
                        'type': 'success',
                    }
                }
            except Exception as e:
                raise UserError(_('Failed to resend message: %s') % str(e))

    def _send_to_whatsapp(self):
        """Send message to WhatsApp service"""
        self.ensure_one()

        if self.direction != 'outgoing':
            raise UserError(_('Can only send outgoing messages'))

        instance = self.instance_id
        if not instance:
            raise UserError(_('No WhatsApp instance configured'))

        # Check if message has attachments (media) - define early
        has_media = bool(self.attachment_ids)

        # Debug logging
        _logger.info(f"=== SEND MESSAGE DEBUG ===")
        _logger.info(f"Message Type: {self.message_type}")
        _logger.info(f"Has Media: {has_media}")
        _logger.info(f"Attachment Count: {len(self.attachment_ids)}")
        _logger.info(f"Content: {self.content}")
        if self.attachment_ids:
            for i, att in enumerate(self.attachment_ids):
                att_name = getattr(att, 'name', None) or getattr(att, 'filename', 'unknown')
                att_size = len(att.datas) if hasattr(att, 'datas') and att.datas else (len(att.file_data) if hasattr(att, 'file_data') and att.file_data else 0)
                _logger.info(f"Attachment {i}: {att_name}, size: {att_size}")
        _logger.info(f"==========================")

        # Use only the standard send endpoint - this service doesn't have specialized media endpoints
        possible_endpoints = [
            f"{instance.api_url}/instances/{instance.session_id}/send",
        ]

        headers = {'Content-Type': 'application/json'}
        if instance.api_token:
            headers['Authorization'] = f'Bearer {instance.api_token}'

        # Prepare message data with proper phone number formatting
        phone_number = self.conversation_id.phone_number

        # Ensure phone number is in correct WhatsApp format
        if not phone_number.endswith('@c.us'):
            # Remove any existing formatting
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # Add country code if missing (assuming India +91 as default)
            if len(clean_phone) == 10:
                clean_phone = '91' + clean_phone
            elif len(clean_phone) == 11 and clean_phone.startswith('0'):
                clean_phone = '91' + clean_phone[1:]

            phone_number = clean_phone + '@c.us'

        # Try different data formats and endpoints
        success = False
        last_error = None

        _logger.info(f"Attempting to send message to {phone_number} via instance {instance.session_id}")

        # Handle media messages BEFORE the endpoint loop
        if has_media and self.message_type != 'text':
            _logger.info(f"Processing media message: type={self.message_type}, attachments={len(self.attachment_ids)}")

            # Process media file
            attachment = self.attachment_ids[0]  # Use first attachment

            # Debug attachment type and attributes
            _logger.info(f"Attachment model: {attachment._name}")
            _logger.info(f"Attachment attributes: {[attr for attr in dir(attachment) if not attr.startswith('_')]}")

            # Handle both WhatsApp attachments and standard Odoo attachments
            if hasattr(attachment, 'file_data'):
                # WhatsApp attachment model
                _logger.info("Using WhatsApp attachment model")
                file_data = attachment.file_data
                filename = attachment.filename if hasattr(attachment, 'filename') else 'media_file'
                mimetype = attachment.mimetype if hasattr(attachment, 'mimetype') else 'application/octet-stream'
            else:
                # Standard Odoo attachment
                _logger.info("Using standard Odoo attachment model")
                file_data = attachment.datas
                filename = attachment.name if hasattr(attachment, 'name') else 'attachment'
                mimetype = attachment.mimetype if hasattr(attachment, 'mimetype') else 'application/octet-stream'

            _logger.info(f"File details: name={filename}, mimetype={mimetype}, data_size={len(file_data) if file_data else 0}")

            # Ensure file_data is base64 string and validate
            import base64
            if isinstance(file_data, bytes):
                file_data_b64 = base64.b64encode(file_data).decode('utf-8')
            elif file_data:
                # Assume it's already base64 encoded
                file_data_b64 = file_data
            else:
                _logger.error(f"No file data available for attachment: {filename}")
                raise UserError(_('No file data available for attachment: %s') % filename)

            # Validate that we have actual file data
            if not file_data_b64 or len(file_data_b64) < 10:
                _logger.error(f"Invalid or empty file data for: {filename}")
                raise UserError(_('Invalid or empty file data for: %s') % filename)

            _logger.info(f"Processing media file: {filename}, size: {len(file_data_b64)} chars, type: {mimetype}")

            # Use smart format detection - try multiple formats automatically
            # Map Odoo message types to WhatsApp API types
            api_message_type = self._get_api_message_type(self.message_type)

            # Try multiple formats in order of likelihood to work
            media_formats = self._get_media_formats(phone_number, api_message_type, mimetype, file_data_b64, filename)

            _logger.info(f"Trying {len(media_formats)} different formats for {filename} (type: {api_message_type})")

            # Try each format until one works
            for format_name, data in media_formats:
                _logger.info(f"Attempting {format_name} for {filename}")

                try:
                    url = possible_endpoints[0]  # Use the main endpoint
                    response = requests.post(url, headers=headers, json=data, timeout=30)
                    _logger.info(f"{format_name} response: {response.status_code}")

                    if response.status_code in [200, 201]:
                        try:
                            result = response.json()
                        except:
                            result = {'success': True, 'messageId': 'sent'}

                        # Check for success
                        is_success = (
                            result.get('success') == True or
                            result.get('status') == 'success' or
                            'messageId' in result or
                            'id' in result or
                            (result.get('error') is None and response.status_code == 200)
                        )

                        if is_success:
                            _logger.info(f"✅ {format_name} worked! Media sent successfully")
                            self.write({
                                'status': 'sent',
                                'message_id': result.get('messageId') or result.get('id') or 'sent',
                                'error_message': f'Sent successfully using {format_name}',
                            })
                            return  # Exit completely - media sent successfully
                        else:
                            _logger.warning(f"{format_name} failed: {result.get('error', 'Unknown error')}")
                    else:
                        _logger.warning(f"{format_name} failed: HTTP {response.status_code}")

                except Exception as e:
                    _logger.warning(f"{format_name} error: {e}")
                    continue

            # If we reach here, all media formats failed
            _logger.error(f"All {len(media_formats)} media formats failed for {filename}")
            error_msg = f"Unable to send {self.message_type} file '{filename}'. "
            error_msg += "The WhatsApp service tried 6 different formats but none worked. This could be due to:\n"
            error_msg += "• File size too large (try a smaller file)\n"
            error_msg += "• File type not supported by WhatsApp\n"
            error_msg += "• WhatsApp service configuration issue\n"
            error_msg += "• Network connectivity problem"

            self.write({
                'status': 'failed',
                'error_message': error_msg,
            })
            raise UserError(_(error_msg))

        # Handle text messages
        for url in possible_endpoints:
            try:
                _logger.info(f"Trying endpoint: {url}")

                # Text message handling
                if 'sendText' in url:
                    data = {
                        'number': phone_number.replace('@c.us', ''),
                        'text': self.content
                    }
                elif 'send' in url:
                    data = {
                        'to': phone_number,
                        'type': 'text',
                        'message': self.content
                    }
                else:
                    data = {
                        'chatId': phone_number,
                        'message': self.content,
                        'session': instance.session_id
                    }

                # Log data (but hide sensitive file data)
                log_data = data.copy()
                if 'media' in log_data and isinstance(log_data['media'], str) and 'base64' in log_data['media']:
                    log_data['media'] = f"[BASE64 DATA - {len(log_data['media'])} chars]"
                _logger.info(f"Sending data to {url}: {log_data}")

                response = requests.post(url, headers=headers, json=data, timeout=30)
                _logger.info(f"Response status: {response.status_code}, Response: {response.text[:500]}...")

                if response.status_code in [200, 201]:
                    try:
                        result = response.json()
                    except:
                        # If response is not JSON, treat as success if status is 200
                        result = {'success': True, 'messageId': 'sent'}

                    # Check for success in different response formats
                    is_success = (
                        result.get('success') == True or
                        result.get('status') == 'success' or
                        result.get('sent') == True or
                        'messageId' in result or
                        'id' in result or
                        (result.get('error') is None and response.status_code == 200)
                    )

                    # Additional check for media messages - some APIs return different success indicators
                    if has_media and not is_success:
                        is_success = (
                            'media' in str(result).lower() or
                            'file' in str(result).lower() or
                            'uploaded' in str(result).lower()
                        )

                    if is_success:

                        self.write({
                            'status': 'sent',
                            'message_id': result.get('messageId') or result.get('id') or 'sent',
                            'error_message': False,
                        })
                        _logger.info(f"Message sent successfully via {url}")
                        success = True
                        break
                    else:
                        last_error = result.get('error') or result.get('message') or 'Unknown error'
                        _logger.warning(f"API returned error: {last_error}")
                else:
                    last_error = f'HTTP {response.status_code}: {response.text}'
                    _logger.warning(f"HTTP error: {last_error}")

                    # Media format trying is now handled in the main media processing above
                    # This section is no longer needed as all formats are tried automatically

            except requests.exceptions.RequestException as e:
                last_error = str(e)
                continue

        if not success:
            # For media messages, provide detailed error instead of fallback
            if has_media and self.message_type != 'text':
                # Get filename safely
                filename = 'unknown file'
                if self.attachment_ids:
                    attachment = self.attachment_ids[0]
                    filename = getattr(attachment, 'filename', None) or getattr(attachment, 'name', 'unknown file')

                _logger.error(f"Media message sending failed for {filename}. Last error: {last_error}")

                # Provide clear, user-friendly error message
                error_msg = f"Unable to send {self.message_type} file '{filename}'. "

                if 'All media formats failed' in str(last_error):
                    error_msg += "The WhatsApp service tried 6 different formats but none worked. This could be due to:\n"
                    error_msg += "• File size too large (try a smaller file)\n"
                    error_msg += "• File type not supported by WhatsApp\n"
                    error_msg += "• WhatsApp service configuration issue\n"
                    error_msg += "• Network connectivity problem"
                elif 'HTTP 413' in str(last_error):
                    error_msg += "File is too large. WhatsApp has file size limits:\n"
                    error_msg += "• Images: 16MB max\n"
                    error_msg += "• Videos: 16MB max\n"
                    error_msg += "• Documents: 100MB max\n"
                    error_msg += "Please use a smaller file."
                elif 'HTTP 415' in str(last_error) or 'HTTP 422' in str(last_error):
                    error_msg += "File format not supported. WhatsApp supports:\n"
                    error_msg += "• Images: JPEG, PNG, GIF\n"
                    error_msg += "• Videos: MP4, 3GP, MOV\n"
                    error_msg += "• Audio: MP3, OGG, WAV, AMR\n"
                    error_msg += "• Documents: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX"
                elif 'unsupported' in str(last_error).lower():
                    error_msg += "The WhatsApp service doesn't support this file type or the current configuration doesn't allow media sending."
                else:
                    error_msg += "The system tried multiple sending methods but all failed. Please check:\n"
                    error_msg += "• File size and format\n"
                    error_msg += "• WhatsApp service status\n"
                    error_msg += "• Network connection"

                self.write({
                    'status': 'failed',
                    'error_message': error_msg,
                })
                raise UserError(_(error_msg))
            else:
                # For text messages, still show generic error
                self.write({
                    'status': 'failed',
                    'error_message': last_error or 'Failed to send message',
                })
                raise UserError(_('Failed to send message: %s') % (last_error or 'Unknown error'))

    def _get_api_message_type(self, message_type):
        """Map Odoo message types to WhatsApp API compatible types"""
        # Some WhatsApp services use different type names
        type_mapping = {
            'image': 'image',      # Try original first
            'video': 'video',      # Try original first
            'audio': 'audio',      # Try original first
            'document': 'document', # Try original first
            'file': 'document',    # Map file to document
            'photo': 'image',      # Map photo to image
        }

        api_type = type_mapping.get(message_type, message_type)
        _logger.info(f"Mapping message type '{message_type}' to API type '{api_type}'")
        return api_type

    def _get_media_formats(self, phone_number, api_message_type, mimetype, file_data_b64, filename):
        """Generate all possible media format combinations to try"""
        formats = []

        # Format 1: Media object structure (most common)
        format1 = {
            'to': phone_number,
            'type': api_message_type,
            'media': {
                'data': f"data:{mimetype};base64,{file_data_b64}",
                'filename': filename,
                'mimetype': mimetype
            }
        }
        if self.content:
            format1['caption'] = self.content
        formats.append(("Media Object Format", format1))

        # Format 2: Direct type field with base64
        format2 = {
            'to': phone_number,
            'type': api_message_type,
            api_message_type: f"data:{mimetype};base64,{file_data_b64}",
            'filename': filename
        }
        if self.content:
            format2['caption'] = self.content
        formats.append(("Direct Type Field Format", format2))

        # Format 3: File field structure
        format3 = {
            'to': phone_number,
            'type': api_message_type,
            'file': f"data:{mimetype};base64,{file_data_b64}",
            'filename': filename
        }
        if self.content:
            format3['caption'] = self.content
        formats.append(("File Field Format", format3))

        # Format 4: Generic 'media' type
        format4 = {
            'to': phone_number,
            'type': 'media',
            'media': f"data:{mimetype};base64,{file_data_b64}",
            'filename': filename,
            'mimetype': mimetype
        }
        if self.content:
            format4['caption'] = self.content
        formats.append(("Generic Media Type", format4))

        # Format 5: No type field (some services prefer this)
        format5 = {
            'to': phone_number,
            'media': f"data:{mimetype};base64,{file_data_b64}",
            'filename': filename,
            'mimetype': mimetype
        }
        if self.content:
            format5['message'] = self.content
        formats.append(("No Type Field Format", format5))

        # Format 6: Raw base64 without data URI
        format6 = {
            'to': phone_number,
            'type': api_message_type,
            'media': file_data_b64,
            'filename': filename,
            'mimetype': mimetype
        }
        if self.content:
            format6['caption'] = self.content
        formats.append(("Raw Base64 Format", format6))

        return formats

    def action_debug_message(self):
        """Debug method to check message details"""
        self.ensure_one()

        debug_info = []
        debug_info.append(f"Message ID: {self.id}")
        debug_info.append(f"Message Type: {self.message_type}")
        debug_info.append(f"Content: {self.content}")
        debug_info.append(f"Direction: {self.direction}")
        debug_info.append(f"Status: {self.status}")
        debug_info.append(f"Has Media: {bool(self.attachment_ids)}")
        debug_info.append(f"Attachment Count: {len(self.attachment_ids)}")

        if self.attachment_ids:
            for i, att in enumerate(self.attachment_ids):
                debug_info.append(f"Attachment {i+1}:")

                # Get name safely
                att_name = getattr(att, 'name', None) or getattr(att, 'filename', 'unknown')
                debug_info.append(f"  - Name: {att_name}")

                # Get MIME type safely
                att_mimetype = getattr(att, 'mimetype', 'unknown')
                debug_info.append(f"  - MIME Type: {att_mimetype}")

                # Check data availability
                has_datas = hasattr(att, 'datas') and bool(att.datas)
                has_file_data = hasattr(att, 'file_data') and bool(att.file_data)
                debug_info.append(f"  - Has Datas: {has_datas}")
                debug_info.append(f"  - Has File Data: {has_file_data}")

                # Get data size
                if has_datas:
                    debug_info.append(f"  - Datas Size: {len(att.datas)} bytes")
                if has_file_data:
                    debug_info.append(f"  - File Data Size: {len(att.file_data)} bytes")

                # Additional attributes
                if hasattr(att, 'filename'):
                    debug_info.append(f"  - Filename: {att.filename}")
                if hasattr(att, 'file_size'):
                    debug_info.append(f"  - File Size: {att.file_size}")

        message = "Message Debug Information:\n\n" + "\n".join(debug_info)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Message Debug Info'),
                'message': message,
                'type': 'info',
                'sticky': True,
            }
        }







    @api.model
    def create_incoming_message(self, conversation_id, message_data):
        """Create incoming message from WhatsApp service data"""
        message_vals = {
            'conversation_id': conversation_id,
            'direction': 'incoming',
            'content': message_data.get('content', ''),
            'message_type': message_data.get('type', 'text'),
            'message_id': message_data.get('id'),
            'date_sent': fields.Datetime.now(),
            'status': 'delivered',
        }

        # Handle different message types
        if message_data.get('type') == 'location':
            message_vals.update({
                'latitude': message_data.get('latitude'),
                'longitude': message_data.get('longitude'),
                'location_name': message_data.get('location_name'),
                'location_address': message_data.get('location_address'),
            })
        elif message_data.get('type') == 'contact':
            message_vals.update({
                'contact_name': message_data.get('contact_name'),
                'contact_phone': message_data.get('contact_phone'),
                'contact_vcard': message_data.get('contact_vcard'),
            })

        message = self.create(message_vals)

        # Handle media attachments
        if message_data.get('media'):
            self._create_attachment(message, message_data['media'])

        return message

    def _create_attachment(self, message, media_data):
        """Create attachment for media message"""
        attachment_vals = {
            'message_id': message.id,
            'filename': media_data.get('filename', 'media_file'),
            'file_data': media_data.get('data'),
            'mimetype': media_data.get('mimetype'),
            'file_size': len(base64.b64decode(media_data.get('data', ''))),
        }

        self.env['whatsapp.attachment'].create(attachment_vals)
