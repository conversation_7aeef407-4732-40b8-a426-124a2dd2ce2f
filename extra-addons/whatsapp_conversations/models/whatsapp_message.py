# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import requests
import json
import logging
import base64

_logger = logging.getLogger(__name__)


class WhatsAppMessage(models.Model):
    _name = 'whatsapp.message'
    _description = 'WhatsApp Message'
    _order = 'date_sent desc'
    _rec_name = 'display_name'

    # Basic Information
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )
    
    # Message Content
    content = fields.Text(
        string='Message Content',
        help='Text content of the message'
    )
    message_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('document', 'Document'),
        ('location', 'Location'),
        ('contact', 'Contact'),
        ('sticker', 'Sticker'),
        ('voice', 'Voice Note'),
        ('system', 'System Message')
    ], string='Message Type', default='text', required=True)
    
    # Direction and Status
    direction = fields.Selection([
        ('incoming', 'Incoming'),
        ('outgoing', 'Outgoing')
    ], string='Direction', required=True)
    
    status = fields.Selection([
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
        ('failed', 'Failed')
    ], string='Status', default='pending')
    
    is_read = fields.Boolean(
        string='Is Read',
        default=False,
        help='Whether this message has been read'
    )
    
    # Timestamps
    date_sent = fields.Datetime(
        string='Date Sent',
        default=fields.Datetime.now,
        required=True
    )
    date_delivered = fields.Datetime(
        string='Date Delivered'
    )
    date_read = fields.Datetime(
        string='Date Read'
    )
    
    # Relations
    conversation_id = fields.Many2one(
        'whatsapp.conversation',
        string='Conversation',
        required=True,
        ondelete='cascade'
    )
    partner_id = fields.Many2one(
        'res.partner',
        string='Contact',
        related='conversation_id.partner_id',
        store=True
    )
    instance_id = fields.Many2one(
        'whatsapp.instance',
        string='Instance',
        related='conversation_id.instance_id',
        store=True
    )
    
    # Message Identifiers
    message_id = fields.Char(
        string='WhatsApp Message ID',
        help='Unique message ID from WhatsApp'
    )
    quoted_message_id = fields.Many2one(
        'whatsapp.message',
        string='Quoted Message',
        help='Message being replied to'
    )
    
    # Media and Attachments
    attachment_ids = fields.One2many(
        'whatsapp.attachment',
        'message_id',
        string='Attachments'
    )
    has_media = fields.Boolean(
        string='Has Media',
        compute='_compute_has_media',
        store=True
    )
    
    # Location Data (for location messages)
    latitude = fields.Float(string='Latitude')
    longitude = fields.Float(string='Longitude')
    location_name = fields.Char(string='Location Name')
    location_address = fields.Text(string='Location Address')
    
    # Contact Data (for contact messages)
    contact_name = fields.Char(string='Contact Name')
    contact_phone = fields.Char(string='Contact Phone')
    contact_vcard = fields.Text(string='Contact vCard')
    
    # User Information
    user_id = fields.Many2one(
        'res.users',
        string='Sent By User',
        help='Odoo user who sent this message (for outgoing messages)'
    )
    
    # Error Information
    error_message = fields.Text(
        string='Error Message',
        help='Error details if message failed to send'
    )
    
    @api.depends('content', 'message_type', 'direction', 'date_sent')
    def _compute_display_name(self):
        for record in self:
            direction_icon = '→' if record.direction == 'outgoing' else '←'
            content_preview = ''
            
            if record.message_type == 'text' and record.content:
                content_preview = record.content[:50]
                if len(record.content) > 50:
                    content_preview += '...'
            else:
                content_preview = f'[{record.message_type.title()}]'
            
            date_str = record.date_sent.strftime('%H:%M') if record.date_sent else ''
            record.display_name = f"{direction_icon} {content_preview} ({date_str})"
    
    @api.depends('attachment_ids', 'message_type')
    def _compute_has_media(self):
        for record in self:
            record.has_media = bool(record.attachment_ids) or record.message_type != 'text'

    def action_mark_as_read(self):
        """Mark this message as read"""
        self.ensure_one()
        if not self.is_read and self.direction == 'incoming':
            self.write({
                'is_read': True,
                'date_read': fields.Datetime.now()
            })

    def action_reply_message(self):
        """Reply to this message"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Reply Message'),
            'res_model': 'whatsapp.send.message',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_conversation_id': self.conversation_id.id,
                'default_partner_id': self.partner_id.id,
                'default_phone_number': self.conversation_id.phone_number,
                'default_quoted_message_id': self.id,
            }
        }

    def action_resend_message(self):
        """Resend failed message"""
        self.ensure_one()
        if self.direction == 'outgoing' and self.status == 'failed':
            try:
                self._send_to_whatsapp()
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Message resent successfully'),
                        'type': 'success',
                    }
                }
            except Exception as e:
                raise UserError(_('Failed to resend message: %s') % str(e))

    def _send_to_whatsapp(self):
        """Send message to WhatsApp service"""
        self.ensure_one()

        if self.direction != 'outgoing':
            raise UserError(_('Can only send outgoing messages'))

        instance = self.instance_id
        if not instance:
            raise UserError(_('No WhatsApp instance configured'))

        # Check if message has attachments (media) - define early
        has_media = bool(self.attachment_ids)

        # Use only the standard send endpoint - this service doesn't have specialized media endpoints
        possible_endpoints = [
            f"{instance.api_url}/instances/{instance.session_id}/send",
        ]

        headers = {'Content-Type': 'application/json'}
        if instance.api_token:
            headers['Authorization'] = f'Bearer {instance.api_token}'

        # Prepare message data with proper phone number formatting
        phone_number = self.conversation_id.phone_number

        # Ensure phone number is in correct WhatsApp format
        if not phone_number.endswith('@c.us'):
            # Remove any existing formatting
            clean_phone = ''.join(filter(str.isdigit, phone_number))

            # Add country code if missing (assuming India +91 as default)
            if len(clean_phone) == 10:
                clean_phone = '91' + clean_phone
            elif len(clean_phone) == 11 and clean_phone.startswith('0'):
                clean_phone = '91' + clean_phone[1:]

            phone_number = clean_phone + '@c.us'

        # Try different data formats and endpoints
        success = False
        last_error = None

        _logger.info(f"Attempting to send message to {phone_number} via instance {instance.session_id}")

        for url in possible_endpoints:
            try:
                _logger.info(f"Trying endpoint: {url}")

                # Handle media messages differently
                if has_media and self.message_type != 'text':
                    attachment = self.attachment_ids[0]  # Use first attachment

                    # Handle both WhatsApp attachments and standard Odoo attachments
                    if hasattr(attachment, 'file_data'):
                        # WhatsApp attachment model
                        file_data = attachment.file_data
                        filename = attachment.filename
                        mimetype = attachment.mimetype
                    else:
                        # Standard Odoo attachment
                        file_data = attachment.datas
                        filename = attachment.name
                        mimetype = attachment.mimetype

                    # Ensure file_data is base64 string and validate
                    import base64
                    if isinstance(file_data, bytes):
                        file_data_b64 = base64.b64encode(file_data).decode('utf-8')
                    elif file_data:
                        # Assume it's already base64 encoded
                        file_data_b64 = file_data
                    else:
                        _logger.error(f"No file data available for attachment: {filename}")
                        continue  # Skip this endpoint and try next one

                    # Validate that we have actual file data
                    if not file_data_b64 or len(file_data_b64) < 10:
                        _logger.error(f"Invalid or empty file data for: {filename}")
                        continue  # Skip this endpoint and try next one

                    _logger.info(f"Processing media file: {filename}, size: {len(file_data_b64)} chars, type: {mimetype}")

                    # Use the standard send endpoint with proper media format
                    # Map Odoo message types to WhatsApp API types
                    api_message_type = self._get_api_message_type(self.message_type)

                    # Format 1: Try with media object structure
                    data = {
                        'to': phone_number,
                        'type': api_message_type,
                        'media': {
                            'data': f"data:{mimetype};base64,{file_data_b64}",
                            'filename': filename,
                            'mimetype': mimetype
                        }
                    }

                    # Add caption if provided
                    if self.content:
                        data['caption'] = self.content

                    _logger.info(f"Trying media format 1 for {filename} with API type: {api_message_type}")

                    # If this format fails, we'll try alternative formats in the error handling
                else:
                    # Text message handling
                    if 'sendText' in url:
                        data = {
                            'number': phone_number.replace('@c.us', ''),
                            'text': self.content
                        }
                    elif 'send' in url:
                        data = {
                            'to': phone_number,
                            'type': 'text',
                            'message': self.content
                        }
                    else:
                        data = {
                            'chatId': phone_number,
                            'message': self.content,
                            'session': instance.session_id
                        }

                # Log data (but hide sensitive file data)
                log_data = data.copy()
                if 'media' in log_data and isinstance(log_data['media'], str) and 'base64' in log_data['media']:
                    log_data['media'] = f"[BASE64 DATA - {len(log_data['media'])} chars]"
                _logger.info(f"Sending data to {url}: {log_data}")

                response = requests.post(url, headers=headers, json=data, timeout=30)
                _logger.info(f"Response status: {response.status_code}, Response: {response.text[:500]}...")

                if response.status_code in [200, 201]:
                    try:
                        result = response.json()
                    except:
                        # If response is not JSON, treat as success if status is 200
                        result = {'success': True, 'messageId': 'sent'}

                    # Check for success in different response formats
                    is_success = (
                        result.get('success') == True or
                        result.get('status') == 'success' or
                        result.get('sent') == True or
                        'messageId' in result or
                        'id' in result or
                        (result.get('error') is None and response.status_code == 200)
                    )

                    # Additional check for media messages - some APIs return different success indicators
                    if has_media and not is_success:
                        is_success = (
                            'media' in str(result).lower() or
                            'file' in str(result).lower() or
                            'uploaded' in str(result).lower()
                        )

                    if is_success:

                        self.write({
                            'status': 'sent',
                            'message_id': result.get('messageId') or result.get('id') or 'sent',
                            'error_message': False,
                        })
                        _logger.info(f"Message sent successfully via {url}")
                        success = True
                        break
                    else:
                        last_error = result.get('error') or result.get('message') or 'Unknown error'
                        _logger.warning(f"API returned error: {last_error}")
                else:
                    last_error = f'HTTP {response.status_code}: {response.text}'
                    _logger.warning(f"HTTP error: {last_error}")

                    # For media messages, try alternative data formats if first attempt failed
                    if has_media and self.message_type != 'text' and response.status_code in [400, 422, 415]:
                        _logger.info(f"Trying alternative media formats for {filename}")

                        # Alternative format 2: Direct base64 in type field
                        alt_data_2 = {
                            'to': phone_number,
                            'type': api_message_type,
                            api_message_type: f"data:{mimetype};base64,{file_data_b64}",
                            'filename': filename
                        }
                        if self.content:
                            alt_data_2['caption'] = self.content

                        # Alternative format 3: Simple structure with file field
                        alt_data_3 = {
                            'to': phone_number,
                            'message': self.content or filename,
                            'type': api_message_type,
                            'file': f"data:{mimetype};base64,{file_data_b64}",
                            'filename': filename
                        }

                        # Alternative format 4: Try with 'media' type for all files
                        alt_data_4 = {
                            'to': phone_number,
                            'type': 'media',
                            'media': f"data:{mimetype};base64,{file_data_b64}",
                            'filename': filename,
                            'mimetype': mimetype
                        }
                        if self.content:
                            alt_data_4['caption'] = self.content

                        # Alternative format 5: Try without type field
                        alt_data_5 = {
                            'to': phone_number,
                            'media': f"data:{mimetype};base64,{file_data_b64}",
                            'filename': filename,
                            'mimetype': mimetype
                        }
                        if self.content:
                            alt_data_5['message'] = self.content

                        # Try alternative formats
                        for alt_format, alt_data in [
                            ("Format 2", alt_data_2),
                            ("Format 3", alt_data_3),
                            ("Format 4", alt_data_4),
                            ("Format 5", alt_data_5)
                        ]:
                            try:
                                _logger.info(f"Trying {alt_format} for media sending")
                                alt_response = requests.post(url, headers=headers, json=alt_data, timeout=30)

                                if alt_response.status_code in [200, 201]:
                                    try:
                                        alt_result = alt_response.json()
                                        if (alt_result.get('success') or alt_result.get('status') == 'success' or
                                            'messageId' in alt_result or 'id' in alt_result):
                                            _logger.info(f"{alt_format} worked! Media sent successfully")
                                            self.write({
                                                'status': 'sent',
                                                'message_id': alt_result.get('messageId') or alt_result.get('id') or 'sent',
                                            })
                                            return
                                    except:
                                        if alt_response.status_code == 200:
                                            _logger.info(f"{alt_format} worked! (Non-JSON response)")
                                            self.write({'status': 'sent', 'message_id': 'sent'})
                                            return
                                else:
                                    _logger.warning(f"{alt_format} failed: {alt_response.status_code}")
                            except Exception as alt_e:
                                _logger.warning(f"{alt_format} error: {alt_e}")
                                continue

            except requests.exceptions.RequestException as e:
                last_error = str(e)
                continue

        if not success:
            # For media messages, provide detailed error instead of fallback
            if has_media and self.message_type != 'text':
                _logger.error(f"Media message sending failed for {filename}. Last error: {last_error}")

                # Don't fallback to text for media messages - this causes confusion
                # Instead, provide clear error message
                error_msg = f"Failed to send {self.message_type} file '{filename}'. "
                if 'HTTP 404' in str(last_error):
                    error_msg += "The WhatsApp service doesn't support media sending endpoints. This may be a limitation of the current WhatsApp service setup."
                elif 'HTTP 413' in str(last_error):
                    error_msg += "File too large. Please use a smaller file."
                elif 'HTTP 415' in str(last_error) or 'HTTP 422' in str(last_error):
                    error_msg += "File format not supported or invalid data format. The WhatsApp service may not support this file type or the API format is incorrect."
                elif 'HTTP 400' in str(last_error):
                    error_msg += "Invalid request format. The WhatsApp service API may require a different data structure for media files."
                else:
                    error_msg += f"Error: {last_error}. This may be due to WhatsApp service limitations or configuration issues."

                self.write({
                    'status': 'failed',
                    'error_message': error_msg,
                })
                raise UserError(_(error_msg))
            else:
                # For text messages, still show generic error
                self.write({
                    'status': 'failed',
                    'error_message': last_error or 'Failed to send message',
                })
                raise UserError(_('Failed to send message: %s') % (last_error or 'Unknown error'))

    def _get_api_message_type(self, message_type):
        """Map Odoo message types to WhatsApp API compatible types"""
        # Some WhatsApp services use different type names
        type_mapping = {
            'image': 'image',      # Try original first
            'video': 'video',      # Try original first
            'audio': 'audio',      # Try original first
            'document': 'document', # Try original first
            'file': 'document',    # Map file to document
            'photo': 'image',      # Map photo to image
        }

        api_type = type_mapping.get(message_type, message_type)
        _logger.info(f"Mapping message type '{message_type}' to API type '{api_type}'")
        return api_type

    def action_test_media_endpoints(self):
        """Test available media endpoints for debugging"""
        self.ensure_one()

        if not self.instance_id:
            raise UserError(_('No WhatsApp instance configured'))

        instance = self.instance_id
        headers = {'Content-Type': 'application/json'}
        if instance.api_token:
            headers['Authorization'] = f'Bearer {instance.api_token}'

        # Test endpoints
        test_endpoints = [
            f"{instance.api_url}/instances/{instance.session_id}/send",
            f"{instance.api_url}/instances/{instance.session_id}/sendText",
            f"{instance.api_url}/instances/{instance.session_id}/sendMedia",
            f"{instance.api_url}/instances/{instance.session_id}/sendFile",
        ]

        results = []
        for url in test_endpoints:
            try:
                # Test with a simple GET request to see if endpoint exists
                response = requests.get(url.replace('/send', '/status'), headers=headers, timeout=5)
                status = f"Status: {response.status_code}"
                results.append(f"✓ {url}: {status}")
            except Exception as e:
                results.append(f"✗ {url}: {str(e)}")

        message = "API Endpoint Test Results:\n\n" + "\n".join(results)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('API Endpoint Test'),
                'message': message,
                'type': 'info',
                'sticky': True,
            }
        }

    def action_test_media_sending(self):
        """Test media sending with detailed logging"""
        self.ensure_one()

        if not self.attachment_ids:
            raise UserError(_('No attachments found to test'))

        if not self.instance_id:
            raise UserError(_('No WhatsApp instance configured'))

        attachment = self.attachment_ids[0]
        filename = attachment.filename if hasattr(attachment, 'filename') else attachment.name

        # Test file data availability
        if hasattr(attachment, 'file_data'):
            file_data = attachment.file_data
            mimetype = attachment.mimetype
        else:
            file_data = attachment.datas
            mimetype = attachment.mimetype

        # Validate file data
        if not file_data:
            raise UserError(_('No file data available for attachment: %s') % filename)

        import base64
        if isinstance(file_data, bytes):
            file_size = len(file_data)
            file_data_b64 = base64.b64encode(file_data).decode('utf-8')
        else:
            file_data_b64 = file_data
            try:
                decoded = base64.b64decode(file_data_b64)
                file_size = len(decoded)
            except:
                file_size = len(file_data_b64)

        message = f"""Media File Test Results:

📁 File: {filename}
📊 Type: {mimetype}
📏 Size: {file_size} bytes
🔢 Base64 Length: {len(file_data_b64)} chars
📝 Message Type: {self.message_type}

✅ File data is available and properly encoded.

You can now try sending this message. If it still fails, check the WhatsApp service logs for specific API errors."""

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Media File Test'),
                'message': message,
                'type': 'info',
                'sticky': True,
            }
        }

    def action_test_image_formats(self):
        """Test different image sending formats to find what works"""
        self.ensure_one()

        if not self.attachment_ids:
            raise UserError(_('No attachments found to test'))

        if not self.instance_id:
            raise UserError(_('No WhatsApp instance configured'))

        attachment = self.attachment_ids[0]
        filename = attachment.filename if hasattr(attachment, 'filename') else attachment.name

        # Get file data
        if hasattr(attachment, 'file_data'):
            file_data = attachment.file_data
            mimetype = attachment.mimetype
        else:
            file_data = attachment.datas
            mimetype = attachment.mimetype

        if not file_data:
            raise UserError(_('No file data available'))

        import base64
        if isinstance(file_data, bytes):
            file_data_b64 = base64.b64encode(file_data).decode('utf-8')
        else:
            file_data_b64 = file_data

        instance = self.instance_id
        headers = {'Content-Type': 'application/json'}
        if instance.api_token:
            headers['Authorization'] = f'Bearer {instance.api_token}'

        url = f"{instance.api_url}/instances/{instance.session_id}/send"
        phone_number = "<EMAIL>"  # Test number

        # Test different type values for images
        test_formats = [
            {'type': 'image', 'name': 'Standard image type'},
            {'type': 'media', 'name': 'Generic media type'},
            {'type': 'file', 'name': 'File type'},
            {'type': None, 'name': 'No type field'},
        ]

        results = []
        for test_format in test_formats:
            try:
                if test_format['type']:
                    test_data = {
                        'to': phone_number,
                        'type': test_format['type'],
                        'media': f"data:{mimetype};base64,{file_data_b64}",
                        'filename': filename
                    }
                else:
                    test_data = {
                        'to': phone_number,
                        'media': f"data:{mimetype};base64,{file_data_b64}",
                        'filename': filename
                    }

                response = requests.post(url, headers=headers, json=test_data, timeout=10)

                if response.status_code in [200, 201]:
                    results.append(f"✅ {test_format['name']}: SUCCESS (Status {response.status_code})")
                else:
                    results.append(f"❌ {test_format['name']}: FAILED (Status {response.status_code})")

            except Exception as e:
                results.append(f"❌ {test_format['name']}: ERROR - {str(e)}")

        message = f"Image Format Test Results:\n\n" + "\n".join(results)
        message += f"\n\nTested with file: {filename}\nMIME type: {mimetype}"

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Image Format Test'),
                'message': message,
                'type': 'info',
                'sticky': True,
            }
        }

    @api.model
    def create_incoming_message(self, conversation_id, message_data):
        """Create incoming message from WhatsApp service data"""
        message_vals = {
            'conversation_id': conversation_id,
            'direction': 'incoming',
            'content': message_data.get('content', ''),
            'message_type': message_data.get('type', 'text'),
            'message_id': message_data.get('id'),
            'date_sent': fields.Datetime.now(),
            'status': 'delivered',
        }

        # Handle different message types
        if message_data.get('type') == 'location':
            message_vals.update({
                'latitude': message_data.get('latitude'),
                'longitude': message_data.get('longitude'),
                'location_name': message_data.get('location_name'),
                'location_address': message_data.get('location_address'),
            })
        elif message_data.get('type') == 'contact':
            message_vals.update({
                'contact_name': message_data.get('contact_name'),
                'contact_phone': message_data.get('contact_phone'),
                'contact_vcard': message_data.get('contact_vcard'),
            })

        message = self.create(message_vals)

        # Handle media attachments
        if message_data.get('media'):
            self._create_attachment(message, message_data['media'])

        return message

    def _create_attachment(self, message, media_data):
        """Create attachment for media message"""
        attachment_vals = {
            'message_id': message.id,
            'filename': media_data.get('filename', 'media_file'),
            'file_data': media_data.get('data'),
            'mimetype': media_data.get('mimetype'),
            'file_size': len(base64.b64decode(media_data.get('data', ''))),
        }

        self.env['whatsapp.attachment'].create(attachment_vals)
