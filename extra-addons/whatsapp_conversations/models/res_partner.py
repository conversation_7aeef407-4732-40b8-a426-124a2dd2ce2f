# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import re
import logging

_logger = logging.getLogger(__name__)


class ResPartner(models.Model):
    _inherit = 'res.partner'

    # WhatsApp Information
    whatsapp_number = fields.Char(
        string='WhatsApp Number',
        help='WhatsApp phone number for this contact'
    )
    whatsapp_name = fields.Char(
        string='WhatsApp Name',
        help='Name as it appears in WhatsApp'
    )
    has_whatsapp = fields.Bo<PERSON>an(
        string='Has WhatsApp',
        compute='_compute_has_whatsapp',
        store=True,
        help='Whether this contact has WhatsApp'
    )
    
    # WhatsApp Conversation Statistics
    whatsapp_conversation_count = fields.Integer(
        string='WhatsApp Conversations',
        compute='_compute_whatsapp_statistics'
    )
    whatsapp_message_count = fields.Integer(
        string='WhatsApp Messages',
        compute='_compute_whatsapp_statistics',
        store=True
    )
    whatsapp_unread_count = fields.Integer(
        string='Unread Messages',
        compute='_compute_whatsapp_statistics',
        store=True
    )
    last_whatsapp_message = fields.Datetime(
        string='Last WhatsApp Message',
        compute='_compute_whatsapp_statistics',
        store=True
    )
    
    # WhatsApp Payment Statistics
    whatsapp_payment_count = fields.Integer(
        string='WhatsApp Payments',
        compute='_compute_payment_statistics',
        store=True
    )
    whatsapp_total_payments = fields.Monetary(
        string='Total Payments',
        compute='_compute_payment_statistics',
        currency_field='currency_id',
        store=True
    )
    whatsapp_pending_payments = fields.Monetary(
        string='Pending Payments',
        compute='_compute_payment_statistics',
        currency_field='currency_id',
        store=True
    )
    
    # Relations
    whatsapp_conversation_ids = fields.One2many(
        'whatsapp.conversation',
        'partner_id',
        string='WhatsApp Conversations'
    )
    whatsapp_payment_ids = fields.One2many(
        'whatsapp.payment',
        'partner_id',
        string='WhatsApp Payments'
    )
    
    # WhatsApp Preferences
    whatsapp_opt_in = fields.Boolean(
        string='WhatsApp Opt-in',
        default=True,
        help='Whether this contact has opted in for WhatsApp messages'
    )
    whatsapp_language = fields.Selection(
        selection='_get_languages',
        string='WhatsApp Language',
        default='en_US',
        help='Preferred language for WhatsApp messages'
    )
    whatsapp_timezone = fields.Selection(
        selection='_get_timezones',
        string='WhatsApp Timezone',
        help='Timezone for WhatsApp message scheduling'
    )
    
    # WhatsApp Status
    whatsapp_status = fields.Selection([
        ('active', 'Active'),
        ('blocked', 'Blocked'),
        ('spam', 'Spam'),
        ('inactive', 'Inactive')
    ], string='WhatsApp Status', default='active')
    
    whatsapp_notes = fields.Text(
        string='WhatsApp Notes',
        help='Internal notes about WhatsApp communication with this contact'
    )
    
    @api.model
    def _get_languages(self):
        """Get available languages"""
        return self.env['res.lang'].get_installed()
    
    @api.model
    def _get_timezones(self):
        """Get available timezones"""
        import pytz
        return [(tz, tz) for tz in pytz.all_timezones]
    
    @api.depends('whatsapp_number', 'phone', 'mobile')
    def _compute_has_whatsapp(self):
        for record in self:
            record.has_whatsapp = bool(
                record.whatsapp_number or
                record.phone or
                record.mobile
            )

    @api.depends('whatsapp_conversation_ids', 'whatsapp_conversation_ids.message_ids', 'whatsapp_conversation_ids.message_ids.is_read', 'whatsapp_conversation_ids.message_ids.direction')
    def _compute_whatsapp_statistics(self):
        for record in self:
            conversations = record.whatsapp_conversation_ids
            record.whatsapp_conversation_count = len(conversations)
            
            # Message statistics
            all_messages = conversations.mapped('message_ids')
            record.whatsapp_message_count = len(all_messages)
            
            # Unread messages
            unread_messages = all_messages.filtered(
                lambda m: not m.is_read and m.direction == 'incoming'
            )
            record.whatsapp_unread_count = len(unread_messages)
            
            # Last message date
            if all_messages:
                last_message = all_messages.sorted('date_sent', reverse=True)[0]
                record.last_whatsapp_message = last_message.date_sent
            else:
                record.last_whatsapp_message = False
    
    @api.depends('whatsapp_payment_ids', 'whatsapp_payment_ids.amount', 'whatsapp_payment_ids.payment_status')
    def _compute_payment_statistics(self):
        for record in self:
            payments = record.whatsapp_payment_ids
            record.whatsapp_payment_count = len(payments)
            
            # Total payments (confirmed only)
            confirmed_payments = payments.filtered(lambda p: p.payment_status == 'confirmed')
            record.whatsapp_total_payments = sum(confirmed_payments.mapped('amount'))
            
            # Pending payments
            pending_payments = payments.filtered(lambda p: p.payment_status == 'pending')
            record.whatsapp_pending_payments = sum(pending_payments.mapped('amount'))
    
    def get_whatsapp_number(self):
        """Get the best WhatsApp number for this contact"""
        self.ensure_one()
        
        # Priority: whatsapp_number > mobile > phone
        if self.whatsapp_number:
            return self._format_phone_number(self.whatsapp_number)
        elif self.mobile:
            return self._format_phone_number(self.mobile)
        elif self.phone:
            return self._format_phone_number(self.phone)
        else:
            return None
    
    def _format_phone_number(self, phone):
        """Format phone number for WhatsApp"""
        if not phone:
            return None
        
        # Remove all non-digit characters
        clean_phone = re.sub(r'\D', '', phone)
        
        # Add country code if missing (assuming India +91)
        if len(clean_phone) == 10:
            clean_phone = '91' + clean_phone
        elif len(clean_phone) == 11 and clean_phone.startswith('0'):
            clean_phone = '91' + clean_phone[1:]
        
        # Add @c.us suffix for WhatsApp
        return clean_phone + '@c.us'
    
    def action_send_whatsapp_message(self):
        """Send WhatsApp message to this contact"""
        self.ensure_one()
        
        if not self.has_whatsapp:
            raise UserError(_('This contact does not have a WhatsApp number'))
        
        if self.whatsapp_status == 'blocked':
            raise UserError(_('This contact is blocked from WhatsApp'))
        
        # Get or create conversation
        instance = self.env['whatsapp.instance'].get_default_instance()
        if not instance:
            raise UserError(_('No WhatsApp instance configured'))
        
        phone_number = self.get_whatsapp_number()
        conversation = self.env['whatsapp.conversation'].create_or_get_conversation(
            phone_number=phone_number,
            instance_id=instance.id,
            partner_id=self.id
        )
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Send WhatsApp Message'),
            'res_model': 'whatsapp.send.message',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_partner_id': self.id,
                'default_conversation_id': conversation.id,
                'default_phone_number': phone_number,
            }
        }
    
    def action_view_whatsapp_conversations(self):
        """View WhatsApp conversations for this contact"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('WhatsApp Conversations'),
            'res_model': 'whatsapp.conversation',
            'view_mode': 'tree,form',
            'domain': [('partner_id', '=', self.id)],
            'context': {
                'default_partner_id': self.id,
            }
        }
    
    def action_view_whatsapp_payments(self):
        """View WhatsApp payments for this contact"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('WhatsApp Payments'),
            'res_model': 'whatsapp.payment',
            'view_mode': 'tree,form',
            'domain': [('partner_id', '=', self.id)],
            'context': {
                'default_partner_id': self.id,
            }
        }
    
    def action_create_whatsapp_payment(self):
        """Create new WhatsApp payment for this contact"""
        self.ensure_one()
        
        # Get or create conversation
        instance = self.env['whatsapp.instance'].get_default_instance()
        if not instance:
            raise UserError(_('No WhatsApp instance configured'))
        
        phone_number = self.get_whatsapp_number()
        if not phone_number:
            raise UserError(_('This contact does not have a WhatsApp number'))
        
        conversation = self.env['whatsapp.conversation'].create_or_get_conversation(
            phone_number=phone_number,
            instance_id=instance.id,
            partner_id=self.id
        )
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Create WhatsApp Payment'),
            'res_model': 'whatsapp.payment',
            'view_mode': 'form',
            'target': 'current',
            'context': {
                'default_partner_id': self.id,
                'default_conversation_id': conversation.id,
            }
        }
    
    def action_whatsapp_payment_reminder(self):
        """Send payment reminder via WhatsApp"""
        self.ensure_one()
        
        if not self.whatsapp_pending_payments:
            raise UserError(_('No pending payments for this contact'))
        
        # Get payment reminder template
        template = self.env['whatsapp.template'].search([
            ('code', '=', 'payment_reminder'),
            ('is_active', '=', True)
        ], limit=1)
        
        if not template:
            raise UserError(_('Payment reminder template not found'))
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Send Payment Reminder'),
            'res_model': 'whatsapp.send.message',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_partner_id': self.id,
                'default_template_id': template.id,
                'default_phone_number': self.get_whatsapp_number(),
            }
        }
    
    def toggle_whatsapp_opt_in(self):
        """Toggle WhatsApp opt-in status"""
        self.ensure_one()
        self.whatsapp_opt_in = not self.whatsapp_opt_in
        
        status = 'opted in' if self.whatsapp_opt_in else 'opted out'
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('WhatsApp Status Updated'),
                'message': _('Contact has been %s for WhatsApp messages') % status,
                'type': 'success',
            }
        }
    
    @api.model
    def sync_whatsapp_contacts(self):
        """Sync contacts from WhatsApp instances"""
        instances = self.env['whatsapp.instance'].search([('is_active', '=', True)])
        
        for instance in instances:
            try:
                # This would sync contacts from WhatsApp service
                # Implementation depends on the WhatsApp service API
                pass
            except Exception as e:
                _logger.error(f"Failed to sync contacts from instance {instance.name}: {e}")
        
        return True
