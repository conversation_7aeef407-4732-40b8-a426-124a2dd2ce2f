# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import requests
import json
import logging

_logger = logging.getLogger(__name__)


class WhatsAppInstance(models.Model):
    _name = 'whatsapp.instance'
    _description = 'WhatsApp Instance Configuration'
    _order = 'name'
    _rec_name = 'name'

    name = fields.Char(
        string='Instance Name',
        required=True,
        help='Friendly name for this WhatsApp instance'
    )
    session_id = fields.Char(
        string='Session ID',
        required=True,
        help='WhatsApp session identifier'
    )
    phone_number = fields.Char(
        string='Phone Number',
        help='WhatsApp phone number for this instance'
    )
    api_url = fields.Char(
        string='API URL',
        default='http://localhost:3001',
        required=True,
        help='WhatsApp service API URL'
    )
    api_token = fields.Char(
        string='API Token',
        help='Authentication token for API access'
    )
    status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connecting', 'Connecting'),
        ('connected', 'Connected'),
        ('error', 'Error')
    ], string='Status', default='disconnected', readonly=True)
    
    is_active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this instance is active'
    )
    is_default = fields.Boolean(
        string='Default Instance',
        help='Use this instance as default for sending messages'
    )
    
    # Statistics
    total_conversations = fields.Integer(
        string='Total Conversations',
        compute='_compute_statistics',
        store=True
    )
    total_messages = fields.Integer(
        string='Total Messages',
        compute='_compute_statistics',
        store=True
    )
    messages_today = fields.Integer(
        string='Messages Today',
        compute='_compute_statistics'
    )
    
    # Configuration
    auto_sync = fields.Boolean(
        string='Auto Sync',
        default=True,
        help='Automatically sync messages from WhatsApp'
    )
    sync_interval = fields.Integer(
        string='Sync Interval (minutes)',
        default=5,
        help='How often to sync messages (in minutes)'
    )
    
    # Timestamps
    last_sync = fields.Datetime(
        string='Last Sync',
        readonly=True
    )
    created_date = fields.Datetime(
        string='Created Date',
        default=fields.Datetime.now,
        readonly=True
    )
    
    # Relations
    conversation_ids = fields.One2many(
        'whatsapp.conversation',
        'instance_id',
        string='Conversations'
    )
    
    @api.depends('conversation_ids', 'conversation_ids.message_ids')
    def _compute_statistics(self):
        for record in self:
            conversations = record.conversation_ids
            record.total_conversations = len(conversations)
            record.total_messages = sum(len(conv.message_ids) for conv in conversations)
            
            # Messages today
            today = fields.Date.today()
            today_messages = conversations.mapped('message_ids').filtered(
                lambda m: m.date_sent and m.date_sent.date() == today
            )
            record.messages_today = len(today_messages)
    
    @api.constrains('is_default')
    def _check_default_instance(self):
        """Ensure only one default instance exists"""
        for record in self:
            if record.is_default:
                other_defaults = self.search([
                    ('is_default', '=', True),
                    ('id', '!=', record.id)
                ])
                if other_defaults:
                    raise ValidationError(_('Only one instance can be set as default.'))
    
    @api.model
    def get_default_instance(self):
        """Get the default WhatsApp instance"""
        default = self.search([('is_default', '=', True), ('is_active', '=', True)], limit=1)
        if not default:
            default = self.search([('is_active', '=', True)], limit=1)
        return default
    
    def action_test_connection(self):
        """Test connection to WhatsApp service"""
        self.ensure_one()
        try:
            # First test service health
            url = f"{self.api_url}/health"
            headers = {}
            if self.api_token:
                headers['Authorization'] = f'Bearer {self.api_token}'

            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                # Now check WhatsApp instance status
                status_url = f"{self.api_url}/instances/{self.session_id}/status"
                status_response = requests.get(status_url, headers=headers, timeout=10)

                if status_response.status_code == 200:
                    status_data = status_response.json()
                    if status_data.get('success'):
                        instance_status = status_data.get('status', {}).get('status', 'unknown')

                        if instance_status == 'connected':
                            self.status = 'connected'
                            message = _('Connection successful! WhatsApp is connected.')
                            msg_type = 'success'
                        elif instance_status in ['qr_ready', 'connecting']:
                            self.status = 'connecting'
                            message = _('Service connected but WhatsApp needs QR scan.')
                            msg_type = 'warning'
                        else:
                            self.status = 'disconnected'
                            message = _('Service connected but WhatsApp is disconnected.')
                            msg_type = 'info'
                    else:
                        self.status = 'disconnected'
                        message = _('Service connected but instance not found.')
                        msg_type = 'warning'
                else:
                    self.status = 'disconnected'
                    message = _('Service connected but cannot check WhatsApp status.')
                    msg_type = 'warning'

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Connection Test'),
                        'message': message,
                        'type': msg_type,
                    }
                }
            else:
                self.status = 'error'
                raise UserError(_('Connection failed: %s') % response.text)

        except Exception as e:
            self.status = 'error'
            raise UserError(_('Connection error: %s') % str(e))
    
    def action_sync_messages(self):
        """Manually sync messages from WhatsApp service"""
        self.ensure_one()
        try:
            # This will be implemented to sync with the WhatsApp service
            self._sync_conversations()
            self.last_sync = fields.Datetime.now()
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Messages synced successfully!'),
                    'type': 'success',
                }
            }
        except Exception as e:
            raise UserError(_('Sync error: %s') % str(e))
    
    def _sync_conversations(self):
        """Sync conversations from WhatsApp service"""
        self.ensure_one()

        try:
            headers = {}
            if self.api_token:
                headers['Authorization'] = f'Bearer {self.api_token}'

            # Since chats endpoint doesn't exist, we'll extract conversations from messages
            # This service doesn't have a separate chats endpoint

            # Get messages from the working endpoint
            msg_url = f"{self.api_url}/instances/{self.session_id}/messages"

            try:
                _logger.info(f"Getting messages from: {msg_url}")
                msg_response = requests.get(msg_url, headers=headers, timeout=30)
                _logger.info(f"Message response status: {msg_response.status_code}")

                if msg_response.status_code == 200:
                    msg_data = msg_response.json()
                    if msg_data.get('success') and msg_data.get('messages'):
                        messages = msg_data['messages']
                        _logger.info(f"Found {len(messages)} messages")

                        # Process messages and extract conversations
                        self._process_incoming_messages(messages)

                        return {
                            'type': 'ir.actions.client',
                            'tag': 'display_notification',
                            'params': {
                                'title': _('Sync Complete'),
                                'message': _('Successfully synced %d messages') % len(messages),
                                'type': 'success',
                            }
                        }
                    else:
                        raise Exception(f"API returned: {msg_data}")
                else:
                    raise Exception(f"HTTP {msg_response.status_code}: {msg_response.text}")

            except Exception as e:
                _logger.error(f"Failed to sync messages: {e}")
                raise Exception(f"Failed to sync messages: {e}")

        except Exception as e:
            _logger.error(f"Error syncing conversations for instance {self.name}: {e}")
            raise

    def _process_chats(self, chats):
        """Process chat list from WhatsApp service"""
        for chat_data in chats:
            try:
                phone_number = chat_data.get('id', '').replace('@c.us', '')
                if not phone_number:
                    continue

                # Get or create conversation
                conversation = self.env['whatsapp.conversation'].create_or_get_conversation(
                    phone_number=phone_number,
                    instance_id=self.id,
                    contact_name=chat_data.get('name', '')
                )

                # Update conversation info
                conversation.write({
                    'contact_name': chat_data.get('name', ''),
                    'unread_count': chat_data.get('unreadCount', 0),
                })

            except Exception as e:
                _logger.error(f"Error processing chat {chat_data.get('id')}: {e}")
                continue

    def _process_incoming_messages(self, messages):
        """Process incoming messages from WhatsApp service"""
        for msg_data in messages:
            try:
                phone_number = msg_data.get('from', '').replace('@c.us', '')
                if not phone_number:
                    continue

                # Get or create conversation
                conversation = self.env['whatsapp.conversation'].create_or_get_conversation(
                    phone_number=phone_number,
                    instance_id=self.id
                )

                # Check if message already exists
                existing_msg = self.env['whatsapp.message'].search([
                    ('message_id', '=', msg_data.get('id')),
                    ('conversation_id', '=', conversation.id)
                ], limit=1)

                if not existing_msg:
                    # Create new message
                    self.env['whatsapp.message'].create_incoming_message(
                        conversation.id, msg_data
                    )

            except Exception as e:
                _logger.error(f"Error processing message {msg_data.get('id')}: {e}")
                continue
    
    @api.model
    def cron_sync_messages(self):
        """Cron job to sync messages for all active instances"""
        instances = self.search([
            ('is_active', '=', True),
            ('auto_sync', '=', True)
        ])
        for instance in instances:
            try:
                instance._sync_conversations()
                instance.last_sync = fields.Datetime.now()
            except Exception as e:
                _logger.error(f"Failed to sync instance {instance.name}: {e}")

    def action_create_whatsapp_instance(self):
        """Create a new WhatsApp instance on the service"""
        self.ensure_one()

        try:
            url = f"{self.api_url}/instances"
            headers = {'Content-Type': 'application/json'}
            if self.api_token:
                headers['Authorization'] = f'Bearer {self.api_token}'

            data = {
                'sessionId': self.session_id,
                'phoneNumber': self.phone_number
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.status = 'connecting'
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Success'),
                            'message': _('WhatsApp instance created successfully! Please scan QR code.'),
                            'type': 'success',
                        }
                    }
                else:
                    raise UserError(_('Failed to create instance: %s') % result.get('error'))
            else:
                raise UserError(_('Failed to create instance: HTTP %s') % response.status_code)

        except Exception as e:
            self.status = 'error'
            raise UserError(_('Error creating instance: %s') % str(e))

    def action_get_qr_code(self):
        """Get QR code for WhatsApp authentication"""
        self.ensure_one()

        try:
            url = f"{self.api_url}/instances/{self.session_id}/qr"
            headers = {}
            if self.api_token:
                headers['Authorization'] = f'Bearer {self.api_token}'

            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('image'):
                    # Open QR code in browser
                    qr_url = f"http://140.238.231.90:3002/qr/{self.session_id}"
                    return {
                        'type': 'ir.actions.act_url',
                        'url': qr_url,
                        'target': 'new',
                    }
                else:
                    raise UserError(_('QR code not available: %s') % result.get('error', 'Unknown error'))
            else:
                raise UserError(_('Failed to get QR code: HTTP %s') % response.status_code)

        except Exception as e:
            raise UserError(_('Error getting QR code: %s') % str(e))

    def action_check_instance_status(self):
        """Check the status of WhatsApp instance"""
        self.ensure_one()

        try:
            url = f"{self.api_url}/instances/{self.session_id}/status"
            headers = {}
            if self.api_token:
                headers['Authorization'] = f'Bearer {self.api_token}'

            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    status_info = result.get('status', {})
                    instance_status = status_info.get('status', 'unknown')

                    # Update Odoo status based on WhatsApp service status
                    if instance_status == 'connected':
                        self.status = 'connected'
                    elif instance_status in ['qr_ready', 'connecting']:
                        self.status = 'connecting'
                    else:
                        self.status = 'disconnected'

                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Status Updated'),
                            'message': _('Instance status: %s') % instance_status,
                            'type': 'info',
                        }
                    }
                else:
                    raise UserError(_('Failed to get status: %s') % result.get('error'))
            else:
                raise UserError(_('Failed to check status: HTTP %s') % response.status_code)

        except Exception as e:
            raise UserError(_('Error checking status: %s') % str(e))

    def action_configure_webhook(self):
        """Configure webhook for real-time message syncing"""
        self.ensure_one()

        try:
            # Get the base URL for webhook
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            webhook_url = f"{base_url}/whatsapp/webhook/{self.session_id}"

            # Try different webhook endpoints based on the service
            webhook_endpoints = [
                f"{self.api_url}/instances/{self.session_id}/webhook",
                f"{self.api_url}/webhook/{self.session_id}",
                f"{self.api_url}/instances/{self.session_id}/setWebhook",
                f"{self.api_url}/setWebhook",
                f"{self.api_url}/instances/{self.session_id}/settings",
                f"{self.api_url}/webhook"
            ]

            headers = {'Content-Type': 'application/json'}
            if self.api_token:
                headers['Authorization'] = f'Bearer {self.api_token}'
            else:
                raise UserError(_('JWT Token is required for webhook configuration. Please get your JWT token from the WhatsApp Dashboard and set it in the instance settings.'))

            # Different data formats for different endpoints
            webhook_data_formats = [
                {
                    'webhook': webhook_url,
                    'events': ['message', 'status', 'qr']
                },
                {
                    'webhook_url': webhook_url,
                    'events': ['message', 'status', 'qr']
                },
                {
                    'url': webhook_url,
                    'events': ['message', 'status', 'qr']
                },
                {
                    'webhookUrl': webhook_url,
                    'session': self.session_id
                },
                {
                    'webhook': webhook_url
                }
            ]

            success = False
            last_error = None

            for endpoint in webhook_endpoints:
                for data_format in webhook_data_formats:
                    try:
                        _logger.info(f"Trying webhook endpoint: {endpoint} with data: {data_format}")
                        response = requests.post(endpoint, headers=headers, json=data_format, timeout=30)
                        _logger.info(f"Webhook response status: {response.status_code}, Response: {response.text[:500]}")

                        if response.status_code in [200, 201]:
                            try:
                                result = response.json()
                                if result.get('success', True):  # Some APIs don't return success field
                                    success = True
                                    _logger.info(f"Webhook configured successfully via {endpoint}")
                                    break
                                else:
                                    last_error = result.get('error', 'Unknown error')
                                    _logger.warning(f"Webhook API returned error: {last_error}")
                            except Exception as json_error:
                                # Some APIs return plain text success
                                if 'success' in response.text.lower() or response.status_code == 200:
                                    success = True
                                    _logger.info(f"Webhook configured successfully via {endpoint} (non-JSON response)")
                                    break
                                else:
                                    last_error = f'Invalid JSON response: {response.text[:200]}'
                                    _logger.warning(f"JSON parse error: {json_error}")
                        elif response.status_code == 401:
                            last_error = 'Authentication failed. Please check your API token.'
                            _logger.warning(f"Authentication failed for {endpoint}")
                        elif response.status_code == 404:
                            last_error = f'Endpoint not found: {endpoint}'
                            _logger.warning(f"Endpoint not found: {endpoint}")
                        else:
                            last_error = f'HTTP {response.status_code}: {response.text[:200]}'
                            _logger.warning(f"HTTP error {response.status_code} for {endpoint}")
                    except requests.exceptions.Timeout:
                        last_error = f'Timeout connecting to {endpoint}'
                        _logger.warning(f"Timeout for {endpoint}")
                        continue
                    except requests.exceptions.ConnectionError:
                        last_error = f'Connection error to {endpoint}'
                        _logger.warning(f"Connection error for {endpoint}")
                        continue
                    except Exception as e:
                        last_error = str(e)
                        _logger.warning(f"Failed webhook attempt {endpoint}: {e}")
                        continue

                if success:
                    break

            if success:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Webhook configured successfully!\nURL: %s') % webhook_url,
                        'type': 'success',
                    }
                }
            else:
                raise UserError(_('Failed to configure webhook: %s') % last_error)

        except Exception as e:
            raise UserError(_('Error configuring webhook: %s') % str(e))

    def action_test_webhook(self):
        """Test webhook endpoint"""
        self.ensure_one()

        try:
            base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            webhook_test_url = f"{base_url}/whatsapp/webhook/test"

            response = requests.get(webhook_test_url, timeout=10)
            if response.status_code == 200:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Webhook endpoint is working!\nURL: %s\nResponse: %s') % (webhook_test_url, response.text),
                        'type': 'success',
                    }
                }
            else:
                raise UserError(_('Webhook test failed: HTTP %s') % response.status_code)

        except Exception as e:
            raise UserError(_('Error testing webhook: %s') % str(e))

    def action_send_test_message(self):
        """Send a test message to verify functionality"""
        self.ensure_one()

        return {
            'type': 'ir.actions.act_window',
            'name': _('Send Test Message'),
            'res_model': 'whatsapp.send.message',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_instance_id': self.id,
                'default_message_content': 'Hello! This is a test message from Odoo WhatsApp module.',
            }
        }

    def action_discover_api_endpoints(self):
        """Discover available API endpoints for this WhatsApp service"""
        self.ensure_one()

        try:
            headers = {}
            if self.api_token:
                headers['Authorization'] = f'Bearer {self.api_token}'

            # Test common GET endpoints first
            get_endpoints = [
                f"{self.api_url}/health",
                f"{self.api_url}/instances",
                f"{self.api_url}/instances/{self.session_id}",
                f"{self.api_url}/instances/{self.session_id}/status",
                f"{self.api_url}/instances/{self.session_id}/qr",
                f"{self.api_url}/instances/{self.session_id}/chats",
                f"{self.api_url}/instances/{self.session_id}/messages",
                f"{self.api_url}/chats",
                f"{self.api_url}/messages",
                f"{self.api_url}/getAllChats",
                f"{self.api_url}/getAllMessages"
            ]

            results = []
            results.append("=== GET ENDPOINTS ===")

            for endpoint in get_endpoints:
                try:
                    response = requests.get(endpoint, headers=headers, timeout=10)
                    if response.status_code < 400:
                        status = f"✅ {response.status_code}"
                        # Try to show some response data
                        try:
                            data = response.json()
                            if isinstance(data, dict) and len(str(data)) < 100:
                                status += f" - {data}"
                            elif isinstance(data, list):
                                status += f" - Array with {len(data)} items"
                        except:
                            if len(response.text) < 50:
                                status += f" - {response.text}"
                    else:
                        status = f"❌ {response.status_code}"
                    results.append(f"{status} - {endpoint}")
                except Exception as e:
                    results.append(f"❌ ERROR - {endpoint} - {str(e)[:50]}")

            # Test webhook endpoints with POST (but don't actually configure)
            results.append("\n=== WEBHOOK ENDPOINTS (POST test) ===")
            webhook_endpoints = [
                f"{self.api_url}/instances/{self.session_id}/webhook",
                f"{self.api_url}/instances/{self.session_id}/setWebhook",
                f"{self.api_url}/webhook",
                f"{self.api_url}/setWebhook"
            ]

            for endpoint in webhook_endpoints:
                try:
                    # Send minimal test data
                    test_data = {"test": "discovery"}
                    response = requests.post(endpoint, headers=headers, json=test_data, timeout=10)
                    status = f"✅ {response.status_code}" if response.status_code < 500 else f"❌ {response.status_code}"
                    results.append(f"{status} - {endpoint}")
                except Exception as e:
                    results.append(f"❌ ERROR - {endpoint} - {str(e)[:50]}")

            message = "\n".join(results)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('API Endpoint Discovery'),
                    'message': message,
                    'type': 'info',
                    'sticky': True,
                }
            }

        except Exception as e:
            raise UserError(_('Error discovering endpoints: %s') % str(e))

    def action_get_api_token(self):
        """Guide user to get API token from WhatsApp Dashboard"""
        self.ensure_one()

        # Calculate dashboard URL based on API URL
        dashboard_url = self.api_url.replace(':3001', ':3002') if ':3001' in self.api_url else f"{self.api_url.rstrip('/')}/dashboard"

        return {
            'type': 'ir.actions.act_window',
            'name': _('Get API Token'),
            'res_model': 'whatsapp.token.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_instance_id': self.id,
                'default_dashboard_url': dashboard_url,
            }
        }
