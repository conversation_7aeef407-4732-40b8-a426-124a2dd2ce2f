import dateutil
from odoo import models, fields, api
from odoo.exceptions import UserError
from datetime import datetime


class Services(models.Model):
    _name = 'x_services'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Services'

    x_agreed_fee = fields.Float(string='Agreed Fee', store=True)
    x_assigned_to = fields.Many2many('res.users', string='Assigned To', store=True,
                                     relation='x_res_users_x_services_rel', column1='x_services_id',
                                     column2='res_users_id')
    x_email_subject = fields.Char(string='Email Subject', store=True)
    x_email_template = fields.Html(string='Email Template', store=True)
    x_execute_every = fields.Selection(
        [('Days', 'Days'), ('Weeks', 'Weeks'), ('Months', 'Months'), ('Quarter', 'Quarter'), ('Years', 'Years')],
        string='Execute Every', store=True)
    x_interval_number = fields.Integer(string='Interval Number', store=True)
    name = fields.Char(string='Name', store=True)
    x_nextcall = fields.Datetime(string='Next Call', store=True)
    x_numbercall = fields.Integer(string='No of Calls', store=True)
    x_planned_hours = fields.Float(string='Planned Hours', store=True)
    x_previouscall = fields.Datetime(string='Previous Call', store=True)
    x_project_id = fields.Many2one('project.project', string='Project', store=True)
    x_task_description = fields.Html(string='Task Description', store=True)
    x_task_manager = fields.Many2one('res.users', string='Task Manager', store=True)
    x_tasks_ref = fields.Many2many('project.task', string='Tasks Ref', store=True, relation='x_service_ref')

    @api.model
    def calculate_next_date(self,x_interval_number, x_interval_type, x_previous_call, x_nextcall):
        from datetime import timedelta
        if x_interval_number and x_interval_type and (x_previous_call or x_nextcall):
            # raise Warning("Hello")
            start_date = x_previous_call
            if x_nextcall:
                start_date = x_nextcall
            next_date = x_nextcall
            # x_interval_type = x_interval_type
            # x_interval_number = x_interval_number
            if x_interval_type == 'Days':
                next_date = start_date + timedelta(days=x_interval_number)
            elif x_interval_type == 'Weeks':
                weeks = x_interval_number * 7
                next_date = start_date + timedelta(days=weeks)
            elif x_interval_type == 'Months':
                a_month = dateutil.relativedelta.relativedelta(months=x_interval_number)
                next_date = start_date + a_month
            elif x_interval_type == 'Quarter':
                x_interval_number = x_interval_number * 3
                a_month = dateutil.relativedelta.relativedelta(months=x_interval_number)
                next_date = start_date + a_month
            elif x_interval_type == 'Years':
                a_month = dateutil.relativedelta.relativedelta(months=x_interval_number * 12)
                next_date = start_date + a_month
            # elif x_next_date = start_date
            # next_date = start_date + timedelta(days=a_month)
            # raise Warning(next_date)
            return next_date
            # record.write({
            #   'x_nextcall' : next_date
            # })

    # all_projects = env['project.project'].search([['x_auto_task','=',True],['x_nextcall','=',todate]])
    # for p in all_projects:
    #   # Call Function to get next date
    #   if p.x_interval_number_type and p.x_repeat_every and (p.x_previous_call or p.x_nextcall):
    #     next_date = calculate_next_date(p.x_interval_number_type, p.x_repeat_every, p.x_previous_call, p.x_nextcall)
    #     p.write({
    #       'x_nextcall' : next_date
    #     })


