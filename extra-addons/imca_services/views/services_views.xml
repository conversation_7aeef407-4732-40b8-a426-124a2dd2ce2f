<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_x_services_form" model="ir.ui.view">
        <field name="name">x_services.form</field>
        <field name="model">x_services</field>
        <field name="arch" type="xml">
           <form string="Services">
                <sheet>
                    <group>
                        <group name="services_add" string="Services ">
                            <field name="name"/>
                            <field name="x_assigned_to" widget="many2many_tags"/>
                            <field name="x_task_manager"/>
                            <field name="x_planned_hours"/>
                            <field name="x_project_id"/>
                            <field name="x_agreed_fee"/>
                            <!--<field name="tag_ids" widget="many2many_tags"/>-->
                            <!--<field name="date"/>-->
                            <!--<field name="company_id" groups="base.group_multi_company"/>-->
                        </group>
                        <group>
                            <h4>
                              Schedule Info
                            </h4>

                            <field name="x_nextcall"/>
                            <field name="x_previouscall"/>
                            <field name="x_interval_number"/>
                            <field name="x_execute_every"/>
                            <field name="x_numbercall"/>

                          </group>
                        </group>
                        <group name="task_description" string="Task Description">
                            <field name="x_email_subject"/>
                            <field name="x_email_template"/>
                            <field name="x_task_description"/>
                            <field name="x_tasks_ref"/>

                        <!--    <field name="product_uom_category_id" invisible="1"/>-->
                        <!--    <field name="product_uom_id" class="oe_inline"/>-->
                        <!--    <field name="currency_id" invisible="1"/>-->
                        </group>

                </sheet>
               <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                </div>
                </form>


        </field>
    </record>

    <record id="view_x_services_tree" model="ir.ui.view">
        <field name="name">x_services.tree</field>
        <field name="model">x_services</field>
        <field name="arch" type="xml">
            <tree>
   <!--editable="bottom"-->
                    <!--<field name="sequence" widget="handle"/>-->
                    <field name="id"/>
                    <field name="name"/>
                    <!--<field name="excluded_journal_ids" widget="many2many_tags" options="{'no_create': True}"/>-->
                    <!--<field name="company_id" groups="base.group_multi_company"/>-->
                </tree>
        </field>
    </record>

    <menuitem id="menu_x_services" name="Services" action="action_x_services"/>

</odoo>
