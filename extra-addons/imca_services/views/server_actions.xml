<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
    <record id="action_x_execute_every" model="ir.actions.server">
        <field name="name">X_execute_every</field>
        <field name="type">ir.actions.server</field>
        <field name="model_id" ref="model_x_services"/>
        <field name="state">code</field>
        <field name="code">
<![CDATA[
todate = datetime.date.today()
all_services = env['x_services'].search([['x_nextcall','=',todate]])
# all_services = env['x_services'].search([['id','=',1]])
for p in all_services:
  no_call = p.x_numbercall
  if no_call != 0:
    if p.x_interval_number and p.x_execute_every and (p.x_previouscall or p.x_nextcall):
      next_date = calculate_next_date(p.x_interval_number, p.x_execute_every, p.x_previouscall, p.x_nextcall)
      if next_date < todate:
        next_date = todate

      if no_call > 0:
        no_call = no_call - 1

      p.write({
        'x_nextcall' : next_date,
        'x_previouscall' : p.x_nextcall,
        'x_numbercall' : no_call
      })
      ]]>
        </field>
    </record>


    <record id="action_x_create_task_from_service_and_customer" model="ir.actions.server">
        <field name="name">x_create Task from Service and Customer</field>
        <field name="type">ir.actions.server</field>
        <field name="model_id" ref="model_x_services"/>
        <field name="state">code</field>
        <field name="code">
<![CDATA[
todate = datetime.date.today()
all_services = env['x_services'].search([['x_nextcall','=', todate]])

for s in all_services:
  customers = env['res.partner'].search([['x_service_ref','in',[s.id]],['x_auto_task','=',True]])
  for c in customers:
    task = {}
    task['name'] = s.name + ' : ' + c.name + ', ' + todate.strftime("%d - %b - %Y")



    task['x_service_ref'] = s.id
    task['partner_id'] = c.id
    if s.x_project_id:
      task['project_id'] = s.x_project_id.id
    assigned = []
    for usr in s.x_assigned_to:
      assigned.append((4, usr.id))

    if assigned:
      task['user_ids'] = assigned
    if s.x_planned_hours:
      task['planned_hours'] = s.x_planned_hours
    if s.x_task_description:
      task['description'] = s.x_task_description
    if s.x_agreed_fee:
      task['x_agreed_fee'] = s.x_agreed_fee

    env['project.task'].create(task)
      ]]>
        </field>
    </record>
    </data>
</odoo>