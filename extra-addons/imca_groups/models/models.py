from odoo import models, fields, api

class ResPartner(models.Model):

    _inherit = 'res.partner'

    x_group_id = fields.Many2one('x_groups',string="Group Ref",store=True)

class Groups(models.Model):
    _name = 'x_groups'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Groups'

    name = fields.Char(string='Name', store=True)
    x_partners = fields.One2many('res.partner',  'x_group_id',string='Clients', store=True)
