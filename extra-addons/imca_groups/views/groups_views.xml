<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_x_groups_form" model="ir.ui.view">
        <field name="name">x_groups.form</field>
        <field name="model">x_groups</field>
        <field name="arch" type="xml">
           <form string="Groups">
                <sheet>
                    <group>
                        <group name="groups_add" string="Groups ">
                            <field name="name"/>
                            <field name="x_partners"/>
                            <!--<field name="account_id"/>-->
                            <!--<field name="tag_ids" widget="many2many_tags"/>-->
                            <!--<field name="date"/>-->
                            <!--<field name="company_id" groups="base.group_multi_company"/>-->
                        </group>
                        <!--<group name="amount" string="Amount">-->
                        <!--    <field name="amount"/>-->
                        <!--    <field name="unit_amount"/>-->
                        <!--    <field name="product_uom_category_id" invisible="1"/>-->
                        <!--    <field name="product_uom_id" class="oe_inline"/>-->
                        <!--    <field name="currency_id" invisible="1"/>-->
                        <!--</group>-->
                    </group>
                </sheet>
               <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                </div>
                </form>


        </field>
    </record>

    <record id="view_x_groups_tree" model="ir.ui.view">
        <field name="name">x_groups.tree</field>
        <field name="model">x_groups</field>
        <field name="arch" type="xml">
            <tree>
   <!--editable="bottom"-->
                    <!--<field name="sequence" widget="handle"/>-->
                    <field name="name"/>
                    <!--<field name="excluded_journal_ids" widget="many2many_tags" options="{'no_create': True}"/>-->
                    <!--<field name="company_id" groups="base.group_multi_company"/>-->
                </tree>

        </field>
    </record>

    <menuitem id="menu_x_groups" name="Groups" action="action_x_groups"/>

</odoo>
