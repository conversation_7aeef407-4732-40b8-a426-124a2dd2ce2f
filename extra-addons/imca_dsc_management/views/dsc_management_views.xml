<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_x_dsc_form" model="ir.ui.view">
        <field name="name">x_dsc.form</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <form string="DSC management">
                <sheet>
                    <group string="Client Info">
                        <field name="name"/>
                        <field name="x_manager" string="Signer Name" domain="[['parent_id','!=',False]]"/>
                        <field name="x_client"/>
                        <field name="x_mobile"/>
                        <field name="x_email"/>
                    </group>
                    <group string="DSC Info">
                        <field name="x_location"/>
                        <field name="x_notes"/>
                        <field name="x_expiry_date"/>

                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread" options="{'post_refresh': 'recipients'}"/>
                </div>
            </form>


        </field>
    </record>

    <record id="view_x_dsc_tree" model="ir.ui.view">
        <field name="name">x_dsc.tree</field>
        <field name="model">x_dsc</field>
        <field name="arch" type="xml">
            <tree>

                <field name="name"/>
                <field name="x_client"/>
                <field name="x_mobile"/>
                <field name="x_email"/>
                <field name="x_manager" string="Signer Name"/>
                <field name="x_location"/>
                <field name="x_notes"/>
                <field name="x_expiry_date"/>

            </tree>
        </field>
    </record>

    <menuitem id="menu_x_dsc" name="DSC Management" action="action_x_dsc"/>

</odoo>
