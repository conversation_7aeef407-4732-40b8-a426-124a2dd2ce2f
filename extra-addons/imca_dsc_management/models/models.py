from odoo import models, fields, api
from odoo.exceptions import UserError



class DSCManagement(models.Model):
    _name = 'x_dsc'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'DSC Management'

    x_manager = fields.Many2one('res.partner', string='Manager', store=True, ondelete='set null')
    x_client = fields.Char(string='Client Name', readonly=True, store=True, related='x_manager.name')
    x_email = fields.Char(string='Email', store=True, related='x_manager.email', readonly=True)
    x_mobile = fields.Char(string='Mobile', store=True, related='x_manager.mobile', readonly=True)
    x_expiry_date = fields.Date(string='Expiry Date', store=True)
    x_location = fields.Selection([('Client', 'Client'), ('Office', 'Office')], string='Location', store=True)
    name = fields.Char(string='Name', store=True)
    x_notes = fields.Text(string='Notes', store=True)
