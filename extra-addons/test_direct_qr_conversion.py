#!/usr/bin/env python3
"""
Test direct QR conversion from mudslide terminal output
"""

from PIL import Image, ImageDraw
from io import BytesIO
import base64
import os

def test_direct_qr_conversion():
    """Test converting terminal QR characters directly to image"""
    print("🧪 TESTING DIRECT QR CONVERSION")
    print("=" * 50)
    
    # Sample mudslide terminal QR output (from your previous example)
    sample_qr_lines = [
        "▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄",
        "█ ▄▄▄▄▄ █▀▄▀▀▄█▄█▀ █▄█▄ ▀▀█▄ ▄▀▄▀▀█ █▀▀▀▄▀█▄▄ ██ ▄▄▄▄▄ █",
        "█ █ █ █ █▀▄█▄ ▄▄█ ▀██▄█ ▀██▄▀ ▄▄▀█▀▀█▄▄▄▀ ▀█ ▄ ██ █ █ █ █",
        "█ █▄▄▄█ █▀▀█▀ ▄▄▄█▄▀▄ ▄▀▀ █ ▄▄▄ ▄▄█▀▀▀█▀ ▄ ▄█▀▀▀▄██ █▄▄▄█ █",
        "█▄▄▄▄▄▄▄█▄▀ █ ▀▄▀▄█ ▀ █▄█ █ █▄█ ▀▄█ ▀▄█ █ █▄█ █ ▀▄█▄▄▄▄▄▄▄█",
        "█ ▀▀▀▀▀ █▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄█",
        "█▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄█"
    ]
    
    try:
        print(f"📝 Sample QR lines: {len(sample_qr_lines)}")
        print(f"📏 Max width: {max(len(line) for line in sample_qr_lines)}")
        
        # Calculate image dimensions
        max_width = max(len(line) for line in sample_qr_lines)
        height = len(sample_qr_lines)
        
        # Create image - each character becomes a pixel block
        pixel_size = 8  # Size of each QR "pixel"
        img_width = max_width * pixel_size
        img_height = height * pixel_size
        
        print(f"🖼️  Image dimensions: {img_width}x{img_height}")
        
        # Create white background
        img = Image.new('RGB', (img_width, img_height), 'white')
        draw = ImageDraw.Draw(img)

        # Convert each terminal character to pixels
        black_pixels = 0
        total_pixels = 0
        
        for y, line in enumerate(sample_qr_lines):
            for x, char in enumerate(line):
                total_pixels += 1
                # Map terminal QR characters to black/white pixels
                if char in ['█', '▄', '▀', '▌', '▐', '▆', '▇']:
                    black_pixels += 1
                    # Black pixel for QR code parts
                    x1 = x * pixel_size
                    y1 = y * pixel_size
                    x2 = x1 + pixel_size
                    y2 = y1 + pixel_size
                    draw.rectangle([x1, y1, x2, y2], fill='black')

        # Add border around the QR code
        border_size = 20
        bordered_img = Image.new('RGB', 
                               (img_width + 2*border_size, img_height + 2*border_size), 
                               'white')
        bordered_img.paste(img, (border_size, border_size))

        # Save the test QR image
        test_qr_file = '/mnt/extra-addons/test_mudslide_qr.png'
        bordered_img.save(test_qr_file)
        
        # Convert to base64
        buffer = BytesIO()
        bordered_img.save(buffer, format='PNG')
        qr_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        print(f"✅ QR image created: {test_qr_file}")
        print(f"📊 Final image size: {bordered_img.size}")
        print(f"📊 Black pixels: {black_pixels}/{total_pixels} ({black_pixels/total_pixels*100:.1f}%)")
        print(f"📊 Base64 length: {len(qr_data)} characters")
        
        # Test if the image is valid
        test_img = Image.open(test_qr_file)
        print(f"✅ Image validation: {test_img.size}, mode: {test_img.mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in QR conversion test: {e}")
        return False

def main():
    print("🔍 DIRECT QR CONVERSION TEST")
    print("=" * 60)
    
    success = test_direct_qr_conversion()
    
    print(f"\n🎯 TEST RESULT:")
    if success:
        print("✅ Direct QR conversion working!")
        print("🚀 Ready to use in Odoo module")
        print("📁 Check the generated image: /mnt/extra-addons/test_mudslide_qr.png")
    else:
        print("❌ Direct QR conversion failed")
        print("🔧 Check PIL/Pillow installation")

if __name__ == '__main__':
    main()
