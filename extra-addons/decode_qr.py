#!/usr/bin/env python3
"""
Decode QR code from the downloaded image
"""

import sys
import os

def decode_qr_simple():
    """Simple QR decoder using qrcode library"""
    try:
        from PIL import Image
        import qrcode
        from pyzbar import pyzbar
        
        # Try to read the QR code
        img_path = '/mnt/extra-addons/1.png'
        
        if not os.path.exists(img_path):
            print(f"❌ Image file not found: {img_path}")
            return None
            
        # Load image
        img = Image.open(img_path)
        print(f"✅ Image loaded: {img.size} pixels")
        
        # Decode QR codes
        qr_codes = pyzbar.decode(img)
        
        if qr_codes:
            for qr in qr_codes:
                qr_data = qr.data.decode('utf-8')
                print('\n' + '='*60)
                print('QR CODE CONTENT ANALYSIS')
                print('='*60)
                print(qr_data)
                print('='*60)
                print(f"Data length: {len(qr_data)} characters")
                print(f"QR type: {qr.type}")
                print(f"QR rect: {qr.rect}")
                return qr_data
        else:
            print('❌ No QR code found in image')
            return None
            
    except ImportError as e:
        print(f"❌ Missing library: {e}")
        print("Install with: pip3 install pillow pyzbar")
        return None
    except Exception as e:
        print(f"❌ Error decoding QR: {e}")
        return None

def analyze_qr_content(content):
    """Analyze the QR code content"""
    if not content:
        return
        
    print('\n' + '='*60)
    print('QR CONTENT ANALYSIS')
    print('='*60)
    
    # Check if it's a test QR code
    if 'WhatsApp Web QR Code Test' in content:
        print("🔍 TYPE: Test QR Code")
        print("📝 PURPOSE: System verification")
        print("⚠️  WARNING: Not for actual WhatsApp scanning")
    elif 'REAL WHATSAPP QR CODE' in content:
        print("🔍 TYPE: Mudslide-captured QR Code")
        print("📝 PURPOSE: Contains mudslide terminal output info")
        print("⚠️  WARNING: Informational only, not actual WhatsApp Web URL")
    elif content.startswith('https://web.whatsapp.com'):
        print("🔍 TYPE: Real WhatsApp Web QR Code")
        print("📝 PURPOSE: Actual WhatsApp Web authentication")
        print("✅ STATUS: Can be scanned by WhatsApp mobile app")
    else:
        print("🔍 TYPE: Unknown QR Code")
        print("📝 CONTENT: Custom data")
    
    # Check for specific information
    if 'Account:' in content:
        account_line = [line for line in content.split('\n') if 'Account:' in line]
        if account_line:
            print(f"📱 ACCOUNT: {account_line[0].strip()}")
    
    if 'URL:' in content:
        url_line = [line for line in content.split('\n') if 'URL:' in line]
        if url_line:
            print(f"🔗 URL: {url_line[0].strip()}")
    
    if 'Session:' in content:
        session_line = [line for line in content.split('\n') if 'Session:' in line]
        if session_line:
            print(f"🔧 SESSION: {session_line[0].strip()}")
    
    if 'Timestamp:' in content:
        timestamp_line = [line for line in content.split('\n') if 'Timestamp:' in line]
        if timestamp_line:
            print(f"⏰ TIMESTAMP: {timestamp_line[0].strip()}")
    
    print('='*60)

def main():
    print("🔍 QR Code Analysis Tool")
    print("Analyzing: /mnt/extra-addons/1.png")
    print("-" * 40)
    
    # Decode QR code
    content = decode_qr_simple()
    
    # Analyze content
    analyze_qr_content(content)
    
    if content:
        print("\n📋 SUMMARY:")
        print("✅ QR code decoded successfully")
        print("✅ Content extracted and analyzed")
        print("\n💡 RECOMMENDATION:")
        
        if 'Test' in content:
            print("This is a test QR code. For real WhatsApp integration:")
            print("1. Use 'Generate Real WhatsApp QR' button")
            print("2. Ensure mudslide captures actual WhatsApp Web URL")
            print("3. Implement proper QR decoding from mudslide terminal output")
        elif 'REAL WHATSAPP QR CODE' in content:
            print("This QR contains mudslide capture info but not actual WhatsApp URL.")
            print("Need to implement terminal QR decoding to extract real WhatsApp Web URL.")
        else:
            print("QR code content analysis complete.")
    else:
        print("\n❌ FAILED TO DECODE QR CODE")
        print("Check if the image file exists and contains a valid QR code.")

if __name__ == '__main__':
    main()
