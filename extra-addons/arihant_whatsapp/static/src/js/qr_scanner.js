/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, onMounted, onWillUnmount, useState } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

export class WhatsAppQRWidget extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        
        this.state = useState({
            qrCodeUrl: null,
            status: 'disconnected',
            isPolling: false
        });
        
        onMounted(() => {
            this.startPolling();
        });
        
        onWillUnmount(() => {
            this.stopPolling();
        });
    }
    
    startPolling() {
        if (this.state.isPolling) return;
        
        this.state.isPolling = true;
        this.pollStatus();
    }
    
    stopPolling() {
        this.state.isPolling = false;
        if (this.pollTimeout) {
            clearTimeout(this.pollTimeout);
        }
    }
    
    async pollStatus() {
        if (!this.state.isPolling) return;
        
        try {
            const accountId = this.props.record.resId;
            const result = await this.rpc("/whatsapp/status/" + accountId, {});
            
            if (result.error) {
                console.error("Error polling WhatsApp status:", result.error);
                return;
            }
            
            // Update state
            this.state.status = result.status;
            this.state.qrCodeUrl = result.qr_code_url;
            
            // Update the form view if status changed
            if (this.props.record.data.status !== result.status) {
                await this.props.record.update({
                    status: result.status,
                    qr_code_url: result.qr_code_url,
                    whatsapp_name: result.whatsapp_name,
                    whatsapp_number: result.whatsapp_number
                });
            }
            
            // Show notification when connected
            if (result.status === 'connected' && this.props.record.data.status !== 'connected') {
                this.notification.add("WhatsApp connected successfully!", {
                    type: "success",
                    title: "WhatsApp Connected"
                });
            }
            
            // Continue polling if in connecting or qr_ready state
            if (result.status === 'connecting' || result.status === 'qr_ready') {
                this.pollTimeout = setTimeout(() => this.pollStatus(), 5000); // Poll every 5 seconds
            } else {
                this.state.isPolling = false;
            }
            
        } catch (error) {
            console.error("Error polling WhatsApp status:", error);
            this.state.isPolling = false;
        }
    }
    
    get qrCodeImageUrl() {
        if (this.state.qrCodeUrl) {
            return this.state.qrCodeUrl + "?t=" + Date.now(); // Add timestamp to prevent caching
        }
        return null;
    }
}

WhatsAppQRWidget.template = "arihant_whatsapp.QRWidget";

registry.category("fields").add("whatsapp_qr", WhatsAppQRWidget);

// Auto-refresh QR code functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh QR code every 30 seconds
    function refreshQRCode() {
        const qrImages = document.querySelectorAll('img[alt="WhatsApp QR Code"]');
        qrImages.forEach(img => {
            if (img.src) {
                const url = new URL(img.src);
                url.searchParams.set('t', Date.now());
                img.src = url.toString();
            }
        });
    }
    
    // Start auto-refresh if QR code is visible
    const qrContainer = document.getElementById('qr_code_container');
    if (qrContainer) {
        setInterval(refreshQRCode, 30000); // Refresh every 30 seconds
    }
});
