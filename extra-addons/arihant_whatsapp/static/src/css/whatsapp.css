/* WhatsApp Integration Styles */

.whatsapp_qr_container {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
}

.whatsapp_qr_code {
    max-width: 300px;
    width: 100%;
    height: auto;
    border: 2px solid #25d366;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin: 20px auto;
    display: block;
}

.whatsapp_qr_loading {
    padding: 50px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    text-align: center;
    color: #6c757d;
}

.whatsapp_qr_instructions {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    color: #0066cc;
}

.whatsapp_qr_instructions h4 {
    margin-top: 0;
    color: #0066cc;
}

.whatsapp_qr_instructions ol {
    margin: 10px 0;
    padding-left: 20px;
}

.whatsapp_status_badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.whatsapp_status_connected {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.whatsapp_status_connecting {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.whatsapp_status_qr_ready {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.whatsapp_status_disconnected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.whatsapp_status_error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.whatsapp_connection_info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.whatsapp_connection_info .info_item {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
}

.whatsapp_connection_info .info_item:last-child {
    border-bottom: none;
}

.whatsapp_connection_info .info_label {
    font-weight: bold;
    color: #495057;
}

.whatsapp_connection_info .info_value {
    color: #6c757d;
}

/* Button Styles */
.btn-whatsapp {
    background-color: #25d366;
    border-color: #25d366;
    color: white;
}

.btn-whatsapp:hover {
    background-color: #128c7e;
    border-color: #128c7e;
    color: white;
}

.btn-whatsapp:focus {
    box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
    .whatsapp_qr_code {
        max-width: 250px;
    }
    
    .whatsapp_qr_container {
        padding: 15px;
        margin: 15px 0;
    }
    
    .whatsapp_connection_info .info_item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .whatsapp_connection_info .info_value {
        margin-top: 5px;
    }
}

/* Animation for loading spinner */
@keyframes whatsapp_spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.whatsapp_loading_spinner {
    animation: whatsapp_spin 1s linear infinite;
    color: #25d366;
}

/* QR Code refresh animation */
.whatsapp_qr_refreshing {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

/* Success/Error messages */
.whatsapp_message {
    padding: 12px 16px;
    border-radius: 6px;
    margin: 15px 0;
    font-weight: 500;
}

.whatsapp_message_success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.whatsapp_message_error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.whatsapp_message_info {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}
