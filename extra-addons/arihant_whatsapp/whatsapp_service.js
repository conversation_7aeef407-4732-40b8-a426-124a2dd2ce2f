#!/usr/bin/env node

/**
 * WhatsApp Web Service for Odoo Integration
 * This service handles WhatsApp Web sessions and QR code generation
 */

const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const fs = require('fs');
const path = require('path');
const http = require('http');
const url = require('url');

class WhatsAppService {
    constructor() {
        this.clients = new Map();
        this.qrCodes = new Map();
        this.server = null;
        this.port = process.env.WHATSAPP_PORT || 3001;
    }

    /**
     * Initialize a WhatsApp client for a session
     */
    async initializeClient(sessionId, phoneNumber) {
        try {
            console.log(`Initializing WhatsApp client for session: ${sessionId}`);
            
            // Create session directory
            const sessionDir = path.join('/tmp/whatsapp_sessions', sessionId);
            if (!fs.existsSync(sessionDir)) {
                fs.mkdirSync(sessionDir, { recursive: true });
            }

            // Initialize WhatsApp client
            const client = new Client({
                authStrategy: new LocalAuth({
                    clientId: sessionId,
                    dataPath: sessionDir
                }),
                puppeteer: {
                    headless: true,
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--single-process',
                        '--disable-gpu'
                    ]
                }
            });

            // Handle QR code generation
            client.on('qr', async (qr) => {
                console.log(`QR Code generated for session: ${sessionId}`);
                
                try {
                    // Generate QR code as base64 image
                    const qrImage = await qrcode.toDataURL(qr, {
                        errorCorrectionLevel: 'M',
                        type: 'image/png',
                        quality: 0.92,
                        margin: 1,
                        color: {
                            dark: '#000000',
                            light: '#FFFFFF'
                        },
                        width: 300
                    });

                    // Store QR code
                    this.qrCodes.set(sessionId, {
                        qr: qr,
                        image: qrImage,
                        timestamp: Date.now()
                    });

                    // Save QR code to file
                    const qrPath = path.join(sessionDir, 'qr.png');
                    const base64Data = qrImage.replace(/^data:image\/png;base64,/, '');
                    fs.writeFileSync(qrPath, base64Data, 'base64');

                    // Save QR code data to JSON
                    const qrDataPath = path.join(sessionDir, 'qr_data.json');
                    fs.writeFileSync(qrDataPath, JSON.stringify({
                        qr: qr,
                        image: qrImage,
                        timestamp: Date.now(),
                        sessionId: sessionId,
                        phoneNumber: phoneNumber
                    }));

                    console.log(`QR Code saved for session: ${sessionId}`);
                } catch (error) {
                    console.error(`Error generating QR code for session ${sessionId}:`, error);
                }
            });

            // Handle authentication
            client.on('authenticated', () => {
                console.log(`WhatsApp authenticated for session: ${sessionId}`);
                this.updateSessionStatus(sessionId, 'authenticated');
            });

            // Handle ready state
            client.on('ready', () => {
                console.log(`WhatsApp client ready for session: ${sessionId}`);
                this.updateSessionStatus(sessionId, 'connected');
                
                // Clear QR code as it's no longer needed
                this.qrCodes.delete(sessionId);
            });

            // Handle disconnection
            client.on('disconnected', (reason) => {
                console.log(`WhatsApp disconnected for session ${sessionId}:`, reason);
                this.updateSessionStatus(sessionId, 'disconnected');
                this.clients.delete(sessionId);
            });

            // Handle authentication failure
            client.on('auth_failure', (msg) => {
                console.error(`WhatsApp auth failure for session ${sessionId}:`, msg);
                this.updateSessionStatus(sessionId, 'error');
            });

            // Store client
            this.clients.set(sessionId, client);

            // Initialize the client
            await client.initialize();

            return true;
        } catch (error) {
            console.error(`Error initializing WhatsApp client for session ${sessionId}:`, error);
            this.updateSessionStatus(sessionId, 'error');
            return false;
        }
    }

    /**
     * Update session status in file system
     */
    updateSessionStatus(sessionId, status) {
        try {
            const sessionDir = path.join('/tmp/whatsapp_sessions', sessionId);
            const statusPath = path.join(sessionDir, 'status.json');
            
            const statusData = {
                status: status,
                timestamp: Date.now(),
                sessionId: sessionId
            };

            fs.writeFileSync(statusPath, JSON.stringify(statusData));
            console.log(`Status updated for session ${sessionId}: ${status}`);
        } catch (error) {
            console.error(`Error updating status for session ${sessionId}:`, error);
        }
    }

    /**
     * Get QR code for a session
     */
    getQRCode(sessionId) {
        return this.qrCodes.get(sessionId);
    }

    /**
     * Disconnect a session
     */
    async disconnectSession(sessionId) {
        try {
            const client = this.clients.get(sessionId);
            if (client) {
                await client.destroy();
                this.clients.delete(sessionId);
            }
            
            this.qrCodes.delete(sessionId);
            this.updateSessionStatus(sessionId, 'disconnected');
            
            console.log(`Session ${sessionId} disconnected`);
            return true;
        } catch (error) {
            console.error(`Error disconnecting session ${sessionId}:`, error);
            return false;
        }
    }

    /**
     * Start HTTP server for API endpoints
     */
    startServer() {
        this.server = http.createServer((req, res) => {
            const parsedUrl = url.parse(req.url, true);
            const pathname = parsedUrl.pathname;
            const query = parsedUrl.query;

            // Set CORS headers
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }

            // Handle different endpoints
            if (pathname === '/start-session' && req.method === 'POST') {
                this.handleStartSession(req, res);
            } else if (pathname === '/get-qr' && req.method === 'GET') {
                this.handleGetQR(req, res, query);
            } else if (pathname === '/disconnect-session' && req.method === 'POST') {
                this.handleDisconnectSession(req, res);
            } else if (pathname === '/status' && req.method === 'GET') {
                this.handleGetStatus(req, res, query);
            } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Endpoint not found' }));
            }
        });

        this.server.listen(this.port, () => {
            console.log(`WhatsApp Service running on port ${this.port}`);
        });
    }

    /**
     * Handle start session request
     */
    async handleStartSession(req, res) {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', async () => {
            try {
                const data = JSON.parse(body);
                const { sessionId, phoneNumber } = data;

                if (!sessionId || !phoneNumber) {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'sessionId and phoneNumber are required' }));
                    return;
                }

                const success = await this.initializeClient(sessionId, phoneNumber);
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ 
                    success: success,
                    sessionId: sessionId,
                    message: success ? 'Session started' : 'Failed to start session'
                }));
            } catch (error) {
                console.error('Error handling start session:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Internal server error' }));
            }
        });
    }

    /**
     * Handle get QR code request
     */
    handleGetQR(req, res, query) {
        try {
            const sessionId = query.sessionId;
            
            if (!sessionId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'sessionId is required' }));
                return;
            }

            const qrData = this.getQRCode(sessionId);
            
            if (qrData) {
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    qr: qrData.qr,
                    image: qrData.image,
                    timestamp: qrData.timestamp
                }));
            } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ 
                    success: false,
                    error: 'QR code not found or expired' 
                }));
            }
        } catch (error) {
            console.error('Error handling get QR:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Internal server error' }));
        }
    }

    /**
     * Handle disconnect session request
     */
    async handleDisconnectSession(req, res) {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', async () => {
            try {
                const data = JSON.parse(body);
                const { sessionId } = data;

                if (!sessionId) {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: 'sessionId is required' }));
                    return;
                }

                const success = await this.disconnectSession(sessionId);
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ 
                    success: success,
                    sessionId: sessionId,
                    message: success ? 'Session disconnected' : 'Failed to disconnect session'
                }));
            } catch (error) {
                console.error('Error handling disconnect session:', error);
                res.writeHead(500, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Internal server error' }));
            }
        });
    }

    /**
     * Handle get status request
     */
    handleGetStatus(req, res, query) {
        try {
            const sessionId = query.sessionId;
            
            if (!sessionId) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'sessionId is required' }));
                return;
            }

            const sessionDir = path.join('/tmp/whatsapp_sessions', sessionId);
            const statusPath = path.join(sessionDir, 'status.json');
            
            if (fs.existsSync(statusPath)) {
                const statusData = JSON.parse(fs.readFileSync(statusPath, 'utf8'));
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(statusData));
            } else {
                res.writeHead(404, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ 
                    status: 'not_found',
                    sessionId: sessionId 
                }));
            }
        } catch (error) {
            console.error('Error handling get status:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Internal server error' }));
        }
    }
}

// Start the service
if (require.main === module) {
    const service = new WhatsAppService();
    service.startServer();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log('Shutting down WhatsApp Service...');
        
        // Disconnect all sessions
        for (const [sessionId, client] of service.clients) {
            try {
                await client.destroy();
                console.log(`Disconnected session: ${sessionId}`);
            } catch (error) {
                console.error(`Error disconnecting session ${sessionId}:`, error);
            }
        }
        
        if (service.server) {
            service.server.close();
        }
        
        process.exit(0);
    });
}

module.exports = WhatsAppService;
