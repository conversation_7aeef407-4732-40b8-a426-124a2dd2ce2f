#!/usr/bin/env python3
"""
Fix QR code display issue by generating a proper test QR code
"""

import sys
import os

# Add Odoo to Python path
sys.path.append('/usr/lib/python3/dist-packages')

import odoo
from odoo import api, SUPERUSER_ID
import base64
import qrcode
from io import Bytes<PERSON>

def fix_qr_code():
    """Fix QR code for account ID 1"""
    
    # Initialize Odoo
    odoo.tools.config.parse_config(['-d', 'arihantai.com'])
    
    with odoo.registry('arihantai.com').cursor() as cr:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Find account ID 1
        account = env['whatsapp.account'].browse(1)
        
        if not account.exists():
            print("❌ Account ID 1 not found")
            return False
        
        print(f"✅ Found account: {account.name}")
        print(f"Current status: {account.status}")
        print(f"Has QR code: {bool(account.qr_code)}")
        
        # Generate a working test QR code
        try:
            qr_content = f"""WhatsApp Web QR Code Test

Account: {account.name}
Status: Ready for scanning
URL: https://arihantai.com/whatsapp/qr/1

This is a test QR code to verify the display system.
For real WhatsApp integration, generate a new QR code.

Timestamp: 2025-06-02 11:00:00
System: Arihant AI WhatsApp Integration"""

            qr = qrcode.QRCode(
                version=2,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_content)
            qr.make(fit=True)

            # Create QR code image
            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Update account with new QR code
            account.write({
                'qr_code': qr_data,
                'status': 'qr_ready',
                'last_qr_update': env['ir.fields'].Datetime.now()
            })
            
            cr.commit()
            
            print(f"✅ QR code updated successfully")
            print(f"✅ Base64 length: {len(qr_data)} characters")
            print(f"✅ Status: {account.status}")
            print(f"✅ URL should now work: https://arihantai.com/whatsapp/qr/1")
            
            return True
            
        except Exception as e:
            print(f"❌ Error generating QR code: {e}")
            return False

if __name__ == '__main__':
    print("=== Fixing QR Code Display Issue ===")
    success = fix_qr_code()
    if success:
        print("\n✅ QR code fixed! Try accessing https://arihantai.com/whatsapp/qr/1 now")
    else:
        print("\n❌ Failed to fix QR code")
