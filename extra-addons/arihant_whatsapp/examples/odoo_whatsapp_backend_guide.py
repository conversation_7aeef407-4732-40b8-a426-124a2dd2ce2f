# -*- coding: utf-8 -*-

"""
Complete Guide: Sending & Receiving WhatsApp Messages from Odoo Backend

This file provides comprehensive examples of how to integrate WhatsApp messaging
into your Odoo backend workflows.

Author: Arihant AI Solutions
Version: 1.0.0
"""

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64
import json
import logging

_logger = logging.getLogger(__name__)


class WhatsAppBackendGuide(models.TransientModel):
    """
    Complete examples for WhatsApp integration in Odoo backend
    """
    _name = 'whatsapp.backend.guide'
    _description = 'WhatsApp Backend Integration Guide'

    # ============================================================================
    # 1. BASIC TEXT MESSAGING
    # ============================================================================

    def example_send_text_message(self):
        """Example: Send a simple text message"""
        
        # Get default WhatsApp account
        account = self.env['whatsapp.account'].get_default_account()
        if not account:
            raise UserError(_("No WhatsApp account configured"))
        
        # Send text message
        message = account.send_text_message(
            recipient='************',  # Phone number
            message_text='Hello from Odoo! This is a test message.',
            res_model='sale.order',  # Optional: link to a record
            res_id=123  # Optional: record ID
        )
        
        _logger.info(f"Text message sent: {message.id}")
        return message

    def example_send_text_to_multiple_recipients(self):
        """Example: Send text message to multiple recipients"""
        
        account = self.env['whatsapp.account'].get_default_account()
        recipients = ['************', '************', '************']
        
        messages = []
        for recipient in recipients:
            message = account.send_text_message(
                recipient=recipient,
                message_text=f'Hello {recipient}! This is a bulk message from Odoo.'
            )
            messages.append(message)
        
        _logger.info(f"Sent {len(messages)} messages")
        return messages

    # ============================================================================
    # 2. MEDIA MESSAGING (Images, Files, Documents)
    # ============================================================================

    def example_send_image_message(self):
        """Example: Send an image with caption"""
        
        account = self.env['whatsapp.account'].get_default_account()
        
        # Read image file (example: company logo)
        try:
            with open('/path/to/your/image.jpg', 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
        except:
            # Fallback: create a simple image or use attachment from record
            image_data = self._get_sample_image_data()
        
        message = account.send_image_message(
            recipient='************',
            image_data=image_data,
            image_name='company_logo.jpg',
            caption='Here is our company logo!'
        )
        
        return message

    def example_send_pdf_document(self):
        """Example: Send a PDF document"""
        
        account = self.env['whatsapp.account'].get_default_account()
        
        # Generate or get PDF data (example: invoice PDF)
        pdf_data = self._generate_sample_pdf()
        
        message = account.send_file_message(
            recipient='************',
            file_data=pdf_data,
            file_name='invoice_INV001.pdf',
            file_type='document'
        )
        
        return message

    # ============================================================================
    # 3. TEMPLATE MESSAGING
    # ============================================================================

    def example_send_template_message(self):
        """Example: Send message using template"""
        
        account = self.env['whatsapp.account'].get_default_account()
        
        # Find or create template
        template = self.env['whatsapp.template'].search([
            ('name', '=', 'Order Confirmation')
        ], limit=1)
        
        if not template:
            template = self._create_order_confirmation_template()
        
        # Template variables
        variables = {
            'customer_name': 'John Doe',
            'order_number': 'SO001',
            'total_amount': '₹5,000',
            'delivery_date': '2025-01-15'
        }
        
        message = account.send_template_message(
            recipient='************',
            template_id=template.id,
            variables=variables
        )
        
        return message

    # ============================================================================
    # 4. INTEGRATION WITH BUSINESS MODELS
    # ============================================================================

    def example_sale_order_integration(self):
        """Example: Send WhatsApp message when sale order is confirmed"""
        
        # Get a sale order (example)
        sale_order = self.env['sale.order'].search([('state', '=', 'sale')], limit=1)
        if not sale_order:
            raise UserError(_("No confirmed sale orders found"))
        
        # Get customer phone number
        customer_phone = sale_order.partner_id.mobile or sale_order.partner_id.phone
        if not customer_phone:
            raise UserError(_("Customer has no phone number"))
        
        # Send order confirmation
        account = self.env['whatsapp.account'].get_default_account()
        message_text = f"""
Dear {sale_order.partner_id.name},

Your order {sale_order.name} has been confirmed!

Order Details:
- Total Amount: {sale_order.currency_id.symbol}{sale_order.amount_total}
- Order Date: {sale_order.date_order.strftime('%Y-%m-%d')}

Thank you for your business!

Best regards,
{sale_order.company_id.name}
        """.strip()
        
        message = account.send_text_message(
            recipient=customer_phone,
            message_text=message_text,
            res_model='sale.order',
            res_id=sale_order.id
        )
        
        return message

    def example_invoice_with_pdf(self):
        """Example: Send invoice with PDF attachment"""
        
        # Get an invoice
        invoice = self.env['account.move'].search([
            ('move_type', '=', 'out_invoice'),
            ('state', '=', 'posted')
        ], limit=1)
        
        if not invoice:
            raise UserError(_("No posted invoices found"))
        
        # Generate invoice PDF
        pdf_content, _ = self.env['ir.actions.report']._render_qweb_pdf(
            'account.account_invoices', invoice.ids
        )
        pdf_data = base64.b64encode(pdf_content).decode('utf-8')
        
        # Send invoice via WhatsApp
        account = self.env['whatsapp.account'].get_default_account()
        customer_phone = invoice.partner_id.mobile or invoice.partner_id.phone
        
        if customer_phone:
            # Send text message first
            text_message = account.send_text_message(
                recipient=customer_phone,
                message_text=f"Dear {invoice.partner_id.name}, please find your invoice {invoice.name} attached.",
                res_model='account.move',
                res_id=invoice.id
            )
            
            # Send PDF attachment
            pdf_message = account.send_file_message(
                recipient=customer_phone,
                file_data=pdf_data,
                file_name=f'Invoice_{invoice.name}.pdf',
                res_model='account.move',
                res_id=invoice.id
            )
            
            return [text_message, pdf_message]
        
        return []

    # ============================================================================
    # 5. RECEIVING MESSAGES (Webhook Integration)
    # ============================================================================

    @api.model
    def process_incoming_message(self, webhook_data):
        """
        Example: Process incoming WhatsApp messages from webhook
        
        This method would be called by the webhook controller when
        messages are received from the WhatsApp service.
        """
        
        try:
            # Extract message data
            session_id = webhook_data.get('sessionId')
            from_number = webhook_data.get('from')
            message_text = webhook_data.get('message', {}).get('body', '')
            message_type = webhook_data.get('message', {}).get('type', 'text')
            
            # Find WhatsApp account
            account = self.env['whatsapp.account'].search([
                ('phone_number', '=', session_id)
            ], limit=1)
            
            if not account:
                _logger.warning(f"No account found for session {session_id}")
                return
            
            # Create incoming message record
            incoming_message = self.env['whatsapp.message'].create({
                'account_id': account.id,
                'message_type': message_type,
                'recipient_phone': from_number.replace('@c.us', ''),
                'message_text': message_text,
                'state': 'delivered',  # Incoming message
                'sent_date': fields.Datetime.now()
            })
            
            # Process message based on content
            self._process_message_content(account, from_number, message_text, incoming_message)
            
            _logger.info(f"Processed incoming message from {from_number}")
            
        except Exception as e:
            _logger.error(f"Error processing incoming message: {str(e)}")

    def _process_message_content(self, account, from_number, message_text, incoming_message):
        """Process incoming message content and send appropriate response"""
        
        message_lower = message_text.lower()
        
        # Auto-responses based on keywords
        if 'hello' in message_lower or 'hi' in message_lower:
            response = "Hello! Thank you for contacting us. How can we help you today?"
            
        elif 'order' in message_lower and 'status' in message_lower:
            response = "To check your order status, please provide your order number."
            
        elif 'support' in message_lower or 'help' in message_lower:
            response = "Our support team will assist you. Please describe your issue in detail."
            
        elif 'price' in message_lower or 'cost' in message_lower:
            response = "For pricing information, please visit our website or contact our sales team."
            
        else:
            response = "Thank you for your message. Our team will get back to you soon."
        
        # Send auto-response
        account.send_text_message(
            recipient=from_number,
            message_text=response,
            res_model='whatsapp.message',
            res_id=incoming_message.id
        )

    # ============================================================================
    # HELPER METHODS
    # ============================================================================

    def _create_order_confirmation_template(self):
        """Create order confirmation template"""
        return self.env['whatsapp.template'].create({
            'name': 'Order Confirmation',
            'template_type': 'text',
            'category': 'order',
            'message_text': '''Hello {{customer_name}},

Your order {{order_number}} has been confirmed!

Order Details:
- Total Amount: {{total_amount}}
- Expected Delivery: {{delivery_date}}

Thank you for your business!

Best regards,
Team''',
            'active': True
        })

    def _get_sample_image_data(self):
        """Get sample image data (placeholder)"""
        # This would return base64 encoded image data
        # For demo purposes, return empty string
        return ""

    def _generate_sample_pdf(self):
        """Generate sample PDF data"""
        # This would generate actual PDF content
        # For demo purposes, return empty string
        return ""


# ============================================================================
# USAGE EXAMPLES IN DIFFERENT MODULES
# ============================================================================

class SaleOrderWhatsApp(models.Model):
    """Example: Adding WhatsApp to Sale Orders"""
    _inherit = 'sale.order'

    def action_send_whatsapp_confirmation(self):
        """Send WhatsApp confirmation when order is confirmed"""
        self.ensure_one()
        
        if not self.partner_id.mobile:
            raise UserError(_("Customer has no mobile number"))
        
        account = self.env['whatsapp.account'].get_default_account()
        if not account:
            raise UserError(_("No WhatsApp account configured"))
        
        message_text = f"""
Dear {self.partner_id.name},

Your order {self.name} has been confirmed!

Order Details:
- Total: {self.currency_id.symbol}{self.amount_total}
- Date: {self.date_order.strftime('%Y-%m-%d')}

Thank you for choosing us!
        """.strip()
        
        message = account.send_text_message(
            recipient=self.partner_id.mobile,
            message_text=message_text,
            res_model=self._name,
            res_id=self.id
        )
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('WhatsApp Sent'),
                'message': _('Order confirmation sent via WhatsApp'),
                'type': 'success',
            }
        }


class AccountMoveWhatsApp(models.Model):
    """Example: Adding WhatsApp to Invoices"""
    _inherit = 'account.move'

    def action_send_whatsapp_invoice(self):
        """Send invoice via WhatsApp"""
        self.ensure_one()
        
        if self.move_type != 'out_invoice':
            raise UserError(_("Only customer invoices can be sent"))
        
        if not self.partner_id.mobile:
            raise UserError(_("Customer has no mobile number"))
        
        # Generate PDF
        pdf_content, _ = self.env['ir.actions.report']._render_qweb_pdf(
            'account.account_invoices', self.ids
        )
        pdf_data = base64.b64encode(pdf_content).decode('utf-8')
        
        # Send via WhatsApp
        account = self.env['whatsapp.account'].get_default_account()
        
        # Send text message
        text_message = account.send_text_message(
            recipient=self.partner_id.mobile,
            message_text=f"Dear {self.partner_id.name}, please find your invoice {self.name} attached.",
            res_model=self._name,
            res_id=self.id
        )
        
        # Send PDF
        pdf_message = account.send_file_message(
            recipient=self.partner_id.mobile,
            file_data=pdf_data,
            file_name=f'Invoice_{self.name}.pdf',
            res_model=self._name,
            res_id=self.id
        )
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Invoice Sent'),
                'message': _('Invoice sent via WhatsApp successfully'),
                'type': 'success',
            }
        }
