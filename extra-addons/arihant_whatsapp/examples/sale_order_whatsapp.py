# -*- coding: utf-8 -*-

"""
Example integration: Adding WhatsApp messaging to Sale Orders

This file demonstrates how to integrate WhatsApp messaging with existing Odoo models.
To use this example:

1. Uncomment the code below
2. Add 'sale' to the depends in __manifest__.py
3. Add this file to the models/__init__.py imports
4. Install/upgrade the module

This will add WhatsApp messaging capabilities to sale orders.
"""

# from odoo import models, fields, api, _
# from odoo.exceptions import UserError


# class SaleOrder(models.Model):
#     _name = 'sale.order'
#     _inherit = ['sale.order', 'whatsapp.mixin']

#     def _get_whatsapp_recipients(self):
#         """Get WhatsApp recipients for this sale order"""
#         recipients = []
        
#         # Add customer mobile number if available
#         if self.partner_id.mobile:
#             # Clean phone number (remove spaces, dashes, etc.)
#             import re
#             phone = re.sub(r'[^\d+]', '', self.partner_id.mobile)
#             recipients.append(phone)
        
#         # Add customer phone if mobile not available
#         elif self.partner_id.phone:
#             import re
#             phone = re.sub(r'[^\d+]', '', self.partner_id.phone)
#             recipients.append(phone)
        
#         return recipients

#     def _get_whatsapp_template_variables(self):
#         """Get template variables for this sale order"""
#         return {
#             'customer_name': self.partner_id.name,
#             'order_number': self.name,
#             'order_date': self.date_order.strftime('%Y-%m-%d') if self.date_order else '',
#             'total_amount': self.amount_total,
#             'currency': self.currency_id.name,
#             'salesperson': self.user_id.name if self.user_id else '',
#             'company_name': self.company_id.name,
#             'delivery_date': self.commitment_date.strftime('%Y-%m-%d') if self.commitment_date else '',
#         }

#     def action_send_order_confirmation(self):
#         """Send order confirmation via WhatsApp"""
#         self.ensure_one()
        
#         # Find order confirmation template
#         template = self.env['whatsapp.template'].search([
#             ('category', '=', 'order'),
#             ('name', 'ilike', 'confirmation'),
#             ('active', '=', True)
#         ], limit=1)
        
#         if not template:
#             # Create a basic template if none exists
#             template = self.env['whatsapp.template'].create({
#                 'name': 'Order Confirmation',
#                 'template_type': 'text',
#                 'category': 'order',
#                 'message_text': '''Hello {{customer_name}},

# Your order {{order_number}} has been confirmed!

# Order Details:
# - Order Date: {{order_date}}
# - Total Amount: {{currency}} {{total_amount}}
# - Salesperson: {{salesperson}}

# Thank you for your business!

# Best regards,
# {{company_name}}''',
#             })
        
#         # Send template message
#         try:
#             messages = self.send_whatsapp_template(template.id)
#             return {
#                 'type': 'ir.actions.client',
#                 'tag': 'display_notification',
#                 'params': {
#                     'title': _('Order Confirmation Sent'),
#                     'message': _('WhatsApp order confirmation sent to customer.'),
#                     'type': 'success',
#                     'sticky': False,
#                 }
#             }
#         except Exception as e:
#             raise UserError(_("Failed to send WhatsApp message: %s") % str(e))

#     def action_send_delivery_notification(self):
#         """Send delivery notification via WhatsApp"""
#         self.ensure_one()
        
#         if self.state != 'sale':
#             raise UserError(_("Order must be confirmed to send delivery notification."))
        
#         # Find delivery notification template
#         template = self.env['whatsapp.template'].search([
#             ('category', '=', 'order'),
#             ('name', 'ilike', 'delivery'),
#             ('active', '=', True)
#         ], limit=1)
        
#         if not template:
#             # Create a basic template if none exists
#             template = self.env['whatsapp.template'].create({
#                 'name': 'Delivery Notification',
#                 'template_type': 'text',
#                 'category': 'order',
#                 'message_text': '''Hello {{customer_name}},

# Great news! Your order {{order_number}} is ready for delivery.

# Expected Delivery: {{delivery_date}}

# Please ensure someone is available to receive the delivery.

# Thank you!
# {{company_name}}''',
#             })
        
#         # Send template message
#         try:
#             messages = self.send_whatsapp_template(template.id)
#             return {
#                 'type': 'ir.actions.client',
#                 'tag': 'display_notification',
#                 'params': {
#                     'title': _('Delivery Notification Sent'),
#                     'message': _('WhatsApp delivery notification sent to customer.'),
#                     'type': 'success',
#                     'sticky': False,
#                 }
#             }
#         except Exception as e:
#             raise UserError(_("Failed to send WhatsApp message: %s") % str(e))


# # Add buttons to sale order form view
# class SaleOrderWhatsAppView(models.Model):
#     _inherit = 'sale.order'

#     def get_whatsapp_button_visibility(self):
#         """Check if WhatsApp buttons should be visible"""
#         return bool(self._get_whatsapp_recipients())


"""
To add WhatsApp buttons to the sale order form view, create this XML:

<record id="view_order_form_whatsapp" model="ir.ui.view">
    <field name="name">sale.order.form.whatsapp</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <xpath expr="//div[@name='button_box']" position="inside">
            <button name="action_send_whatsapp_message" type="object" 
                    class="oe_stat_button" icon="fa-whatsapp"
                    attrs="{'invisible': [('whatsapp_message_count', '=', 0)]}">
                <field name="whatsapp_message_count" widget="statinfo" string="WhatsApp"/>
            </button>
        </xpath>
        <xpath expr="//header" position="inside">
            <button name="action_send_order_confirmation" type="object" 
                    string="Send Confirmation" class="btn-primary"
                    attrs="{'invisible': ['|', ('state', '!=', 'sale'), ('partner_id', '=', False)]}"/>
            <button name="action_send_delivery_notification" type="object" 
                    string="Send Delivery Notice" class="btn-secondary"
                    attrs="{'invisible': ['|', ('state', '!=', 'sale'), ('partner_id', '=', False)]}"/>
        </xpath>
    </field>
</record>
"""


"""
Example usage in Python code:

# Send a quick text message
order = self.env['sale.order'].browse(order_id)
order.send_whatsapp_text("Your order has been processed!")

# Send using a template
template = self.env['whatsapp.template'].search([('name', '=', 'Order Confirmation')], limit=1)
order.send_whatsapp_template(template.id)

# Send an image (e.g., order receipt)
import base64
with open('/path/to/receipt.pdf', 'rb') as f:
    file_data = base64.b64encode(f.read())
order.send_whatsapp_file(file_data, 'order_receipt.pdf', 'document')

# Create a bulk messaging queue
template = self.env['whatsapp.template'].search([('name', '=', 'Promotion')], limit=1)
orders = self.env['sale.order'].search([('state', '=', 'sale')])
for order in orders:
    queue = order.create_whatsapp_queue(template.id, f"Promotion for {order.name}")
    queue.action_schedule()
"""


"""
Example WhatsApp Templates for Sale Orders:

1. Order Confirmation:
   Type: Text
   Category: Order
   Content: "Hello {{customer_name}}, your order {{order_number}} for {{currency}} {{total_amount}} has been confirmed. Expected delivery: {{delivery_date}}. Thank you!"

2. Payment Reminder:
   Type: Text
   Category: Payment
   Content: "Dear {{customer_name}}, this is a friendly reminder that payment for order {{order_number}} ({{currency}} {{total_amount}}) is due. Please contact us if you have any questions."

3. Delivery Notification:
   Type: Text
   Category: Order
   Content: "Hi {{customer_name}}! Your order {{order_number}} is out for delivery and should arrive today. Please ensure someone is available to receive it."

4. Order Completion:
   Type: Text
   Category: Order
   Content: "Thank you {{customer_name}}! Your order {{order_number}} has been delivered. We hope you're satisfied with your purchase. Please rate your experience!"
"""
