#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add security access rights for new WhatsApp models.
Run this after upgrading the module to add the necessary access rights.

Usage:
1. Upgrade the arihant_whatsapp module first
2. Run this script from the Odoo shell:
   python3 add_security.py

Or run from Odoo shell:
env['ir.model.access'].create_whatsapp_access_rights()
"""

def create_whatsapp_access_rights(env):
    """Create access rights for WhatsApp models"""
    
    # Define access rights
    access_rights = [
        ('whatsapp.message.user', 'whatsapp.message', 'base.group_user'),
        ('whatsapp.template.user', 'whatsapp.template', 'base.group_user'),
        ('whatsapp.template.variable.user', 'whatsapp.template.variable', 'base.group_user'),
        ('whatsapp.contact.user', 'whatsapp.contact', 'base.group_user'),
        ('whatsapp.queue.user', 'whatsapp.queue', 'base.group_user'),
        ('whatsapp.queue.recipient.user', 'whatsapp.queue.recipient', 'base.group_user'),
    ]
    
    created_count = 0
    
    for access_name, model_name, group_ref in access_rights:
        # Check if model exists
        model = env['ir.model'].search([('model', '=', model_name)], limit=1)
        if not model:
            print(f"Model {model_name} not found, skipping...")
            continue
        
        # Get group
        try:
            group = env.ref(group_ref)
        except ValueError:
            print(f"Group {group_ref} not found, skipping...")
            continue
        
        # Check if access right already exists
        existing = env['ir.model.access'].search([
            ('name', '=', access_name),
            ('model_id', '=', model.id)
        ])
        
        if existing:
            print(f"Access right {access_name} already exists, skipping...")
            continue
        
        # Create access right
        env['ir.model.access'].create({
            'name': access_name,
            'model_id': model.id,
            'group_id': group.id,
            'perm_read': True,
            'perm_write': True,
            'perm_create': True,
            'perm_unlink': True,
        })
        
        created_count += 1
        print(f"Created access right: {access_name}")
    
    print(f"\nCreated {created_count} access rights successfully!")
    return created_count

if __name__ == "__main__":
    print("This script should be run from within Odoo shell.")
    print("\nTo run from Odoo shell:")
    print("1. Start Odoo shell: python3 odoo-bin shell -d your_database")
    print("2. Run: exec(open('/path/to/add_security.py').read())")
    print("3. Run: create_whatsapp_access_rights(env)")
    print("\nOr simply run:")
    print("env['ir.model.access'].create_whatsapp_access_rights()")
