# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64
import logging

_logger = logging.getLogger(__name__)


class WhatsAppMessageWizard(models.TransientModel):
    _name = 'whatsapp.message.wizard'
    _description = 'Send WhatsApp Message'

    # Account and recipient
    account_id = fields.Many2one('whatsapp.account', string='WhatsApp Account', 
                                default=lambda self: self.env['whatsapp.account'].get_default_account(),
                                required=True)
    recipient_phone = fields.Char('Recipient Phone', required=True,
                                 help="Phone number with country code (e.g., ************)")
    
    # Message content
    message_type = fields.Selection([
        ('text', 'Text Message'),
        ('image', 'Image'),
        ('file', 'File/Document'),
    ], string='Message Type', default='text', required=True)
    
    message_text = fields.Text('Message Text', required=True)
    
    # File attachment
    attachment = fields.Binary('Attachment')
    attachment_name = fields.Char('File Name')
    caption = fields.Text('Caption', help="Caption for images/files")
    
    # Template
    use_template = fields.Boolean('Use Template')
    template_id = fields.Many2one('whatsapp.template', string='Template')
    template_variables = fields.Text('Template Variables (JSON)', 
                                   help='{"customer_name": "John", "order_number": "SO001"}')
    
    # Context
    res_model = fields.Char('Related Model')
    res_id = fields.Integer('Related Record ID')

    @api.onchange('template_id')
    def _onchange_template_id(self):
        """Update message text when template changes"""
        if self.template_id:
            self.message_text = self.template_id.message_text

    @api.onchange('use_template')
    def _onchange_use_template(self):
        """Clear template when not using template"""
        if not self.use_template:
            self.template_id = False
            self.template_variables = False

    @api.onchange('message_type')
    def _onchange_message_type(self):
        """Clear attachment when message type changes"""
        if self.message_type == 'text':
            self.attachment = False
            self.attachment_name = False
            self.caption = False

    def action_send_message(self):
        """Send the WhatsApp message"""
        self.ensure_one()
        
        if not self.account_id:
            raise UserError(_("Please select a WhatsApp account"))
        
        if self.account_id.status != 'connected':
            raise UserError(_("WhatsApp account is not connected. Please connect first."))
        
        try:
            message = None
            
            if self.message_type == 'text':
                if self.use_template and self.template_id:
                    # Send template message
                    variables = {}
                    if self.template_variables:
                        import json
                        try:
                            variables = json.loads(self.template_variables)
                        except json.JSONDecodeError:
                            raise UserError(_("Invalid JSON format in template variables"))
                    
                    message = self.account_id.send_template_message(
                        recipient=self.recipient_phone,
                        template_id=self.template_id.id,
                        variables=variables,
                        res_model=self.res_model,
                        res_id=self.res_id
                    )
                else:
                    # Send text message
                    message = self.account_id.send_text_message(
                        recipient=self.recipient_phone,
                        message_text=self.message_text,
                        res_model=self.res_model,
                        res_id=self.res_id
                    )
            
            elif self.message_type == 'image':
                if not self.attachment:
                    raise UserError(_("Please attach an image file"))
                
                message = self.account_id.send_image_message(
                    recipient=self.recipient_phone,
                    image_data=self.attachment,
                    image_name=self.attachment_name or 'image.jpg',
                    caption=self.caption,
                    res_model=self.res_model,
                    res_id=self.res_id
                )
            
            elif self.message_type == 'file':
                if not self.attachment:
                    raise UserError(_("Please attach a file"))
                
                message = self.account_id.send_file_message(
                    recipient=self.recipient_phone,
                    file_data=self.attachment,
                    file_name=self.attachment_name or 'document.pdf',
                    res_model=self.res_model,
                    res_id=self.res_id
                )
            
            if message:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Message Sent'),
                        'message': _('WhatsApp message sent successfully to %s') % self.recipient_phone,
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                raise UserError(_("Failed to send message"))
                
        except Exception as e:
            _logger.error(f"Error sending WhatsApp message: {str(e)}")
            raise UserError(_("Failed to send WhatsApp message: %s") % str(e))

    def action_send_and_close(self):
        """Send message and close wizard"""
        result = self.action_send_message()
        return {'type': 'ir.actions.act_window_close'}

    @api.model
    def default_get(self, fields_list):
        """Set default values from context"""
        defaults = super().default_get(fields_list)
        
        # Get context values
        context = self.env.context
        if context.get('default_res_model'):
            defaults['res_model'] = context['default_res_model']
        if context.get('default_res_id'):
            defaults['res_id'] = context['default_res_id']
        
        # Try to get recipient from active record
        if context.get('active_model') and context.get('active_id'):
            try:
                active_record = self.env[context['active_model']].browse(context['active_id'])
                if hasattr(active_record, 'mobile') and active_record.mobile:
                    defaults['recipient_phone'] = active_record.mobile
                elif hasattr(active_record, 'phone') and active_record.phone:
                    defaults['recipient_phone'] = active_record.phone
                elif hasattr(active_record, 'partner_id') and active_record.partner_id:
                    if active_record.partner_id.mobile:
                        defaults['recipient_phone'] = active_record.partner_id.mobile
                    elif active_record.partner_id.phone:
                        defaults['recipient_phone'] = active_record.partner_id.phone
                
                defaults['res_model'] = context['active_model']
                defaults['res_id'] = context['active_id']
            except:
                pass
        
        return defaults
