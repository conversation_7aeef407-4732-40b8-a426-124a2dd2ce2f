# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import logging

_logger = logging.getLogger(__name__)


class WhatsAppSyncWizard(models.TransientModel):
    _name = 'whatsapp.sync.wizard'
    _description = 'Sync with Existing WhatsApp Instance'

    account_id = fields.Many2one('whatsapp.account', string='WhatsApp Account', required=True)
    session_id = fields.Char('Session ID', required=True, default='test123',
                            help="Enter the session ID from your WhatsApp dashboard (e.g., test123)")
    instance_status = fields.Text('Instance Status', readonly=True)
    available_instances = fields.Text('Available Instances', readonly=True)

    @api.onchange('session_id')
    def _onchange_session_id(self):
        """Check instance status when session ID changes"""
        if self.session_id:
            self._check_instance_status()

    def _check_instance_status(self):
        """Check the status of the instance"""
        try:
            # Get all instances from WhatsApp service
            api_url = "http://**************:3001/api/instances"
            response = requests.get(api_url, timeout=10)
            
            if response.status_code == 200:
                instances = response.json().get('data', [])
                
                # Format available instances
                instance_list = []
                target_instance = None
                
                for instance in instances:
                    session_id = instance.get('sessionId', 'Unknown')
                    status = instance.get('status', 'Unknown')
                    phone = instance.get('phoneNumber', 'Not connected')
                    
                    instance_info = f"• {session_id} - Status: {status} - Phone: {phone}"
                    instance_list.append(instance_info)
                    
                    if session_id == self.session_id:
                        target_instance = instance
                
                self.available_instances = "Available Instances:\n" + "\n".join(instance_list)
                
                if target_instance:
                    status = target_instance.get('status', 'Unknown')
                    phone = target_instance.get('phoneNumber', 'Not connected')
                    message_count = target_instance.get('messageCount', 0)
                    
                    self.instance_status = f"""Instance Found: {self.session_id}
Status: {status}
Phone Number: {phone}
Message Count: {message_count}
Ready to sync: {'Yes' if status == 'CONNECTED' else 'No - Instance not connected'}"""
                else:
                    self.instance_status = f"Instance '{self.session_id}' not found in WhatsApp service"
            else:
                self.instance_status = f"Failed to connect to WhatsApp service (Status: {response.status_code})"
                
        except Exception as e:
            self.instance_status = f"Error checking instance: {str(e)}"

    def action_sync_instance(self):
        """Sync the Odoo account with the existing WhatsApp instance"""
        self.ensure_one()
        
        try:
            # Check if instance exists and is connected
            api_url = "http://**************:3001/api/instances"
            response = requests.get(api_url, timeout=10)
            
            if response.status_code != 200:
                raise UserError(_("Cannot connect to WhatsApp service"))
            
            instances = response.json().get('data', [])
            target_instance = None
            
            for instance in instances:
                if instance.get('sessionId') == self.session_id:
                    target_instance = instance
                    break
            
            if not target_instance:
                raise UserError(_("Instance '%s' not found in WhatsApp service") % self.session_id)
            
            # Update the Odoo account
            update_vals = {
                'session_id': self.session_id,
                'phone_number': self.session_id,  # Use session ID as identifier
                'whatsapp_number': target_instance.get('phoneNumber'),
                'status': 'connected' if target_instance.get('status') == 'CONNECTED' else 'disconnected',
                'is_default': True  # Make this the default account
            }
            
            # Clear other default accounts
            other_accounts = self.env['whatsapp.account'].search([
                ('id', '!=', self.account_id.id),
                ('is_default', '=', True)
            ])
            other_accounts.write({'is_default': False})
            
            # Update current account
            self.account_id.write(update_vals)
            
            # Test the connection by getting recent messages
            self._sync_recent_messages()
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Sync Successful'),
                    'message': _('WhatsApp instance "%s" has been synced successfully!') % self.session_id,
                    'type': 'success',
                    'sticky': False,
                }
            }
            
        except Exception as e:
            _logger.error(f"Error syncing instance: {str(e)}")
            raise UserError(_("Failed to sync instance: %s") % str(e))

    def _sync_recent_messages(self):
        """Sync recent messages from the WhatsApp service"""
        try:
            # This would typically fetch recent messages from the service
            # For now, we'll just log that sync was successful
            _logger.info(f"Successfully synced with WhatsApp instance {self.session_id}")
            
        except Exception as e:
            _logger.warning(f"Could not sync recent messages: {str(e)}")

    def action_refresh_status(self):
        """Refresh the instance status"""
        self._check_instance_status()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Status Refreshed'),
                'message': _('Instance status has been updated'),
                'type': 'info',
                'sticky': False,
            }
        }
