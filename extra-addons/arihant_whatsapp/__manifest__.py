{
    'name': 'Arihant AI WhatsApp Integration',
    'version': '********.2',
    'category': 'Communication',
    'summary': 'Centralized WhatsApp messaging system using Mudslide API',
    'description': '''
        Arihant AI WhatsApp Integration Module
        =====================================

        This module provides a centralized WhatsApp messaging system for Arihant AI.

        Features:
        * Multiple WhatsApp account management
        * QR Code scanning for account authentication
        * Message queue and history tracking
        * Template management for common messages
        * API integration with other Odoo modules
        * Real-time status monitoring
        * Integration with Mudslide WhatsApp API

        Requirements:
        * Node.js installed on server
        * Mudslide package (npm install -g mudslide)
        * Internet connection for WhatsApp Web
    ''',
    'author': 'Arihant AI',
    'website': 'https://arihantai.com',
    'depends': ['base', 'web', 'mail'],
    'data': [
        'security/ir.model.access.csv',
        'views/whatsapp_account_views.xml',
        'views/whatsapp_message_views.xml',
        'views/whatsapp_template_views.xml',
        'views/whatsapp_contact_views.xml',
        'views/whatsapp_queue_views.xml',

        'views/qr_templates.xml',
        'views/menu_views.xml',
        'data/cron_jobs.xml',
    ],

    'demo': [
        'data/demo_templates.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'arihant_whatsapp/static/src/css/whatsapp.css',
            'arihant_whatsapp/static/src/js/qr_scanner.js',
        ],
    },

    'installable': True,
    'auto_install': False,
    'application': True,
    'license': 'LGPL-3',
}
