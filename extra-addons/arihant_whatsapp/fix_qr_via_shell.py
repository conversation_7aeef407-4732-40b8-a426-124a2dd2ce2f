#!/usr/bin/env python3
"""
Fix QR code using Odoo shell
"""

import base64
import qrcode
from io import BytesIO

# Generate test QR code
qr_content = """WhatsApp Web QR Code Test

Account: Main WhatsApp Business  
URL: https://arihantai.com/whatsapp/qr/1

This is a test QR code to verify the display system.
For real WhatsApp integration, generate a new QR code in Odoo.

Instructions:
1. This QR code should display properly in your browser
2. If you can see this QR code, the system is working  
3. Generate a real QR code using the "Generate QR Code" button in Odoo

System: Arihant AI WhatsApp Integration
Status: Test QR Code - Not for actual WhatsApp scanning
Timestamp: 2025-06-02 11:15:00"""

qr = qrcode.QRCode(
    version=2,
    error_correction=qrcode.constants.ERROR_CORRECT_L,
    box_size=10,
    border=4,
)
qr.add_data(qr_content)
qr.make(fit=True)

# Create QR code image
img = qr.make_image(fill_color="black", back_color="white")

# Convert to base64
buffer = BytesIO()
img.save(buffer, format='PNG')
qr_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

print(f"Generated QR code: {len(qr_data)} characters")

# Find or create account
account = env['whatsapp.account'].search([('id', '=', 1)], limit=1)

if not account:
    print("Creating new account...")
    account = env['whatsapp.account'].create({
        'name': 'Main WhatsApp Business',
        'phone_number': '+**********',
        'qr_code': qr_data,
        'status': 'qr_ready'
    })
    print(f"Created account ID: {account.id}")
else:
    print(f"Found account: {account.name}")
    account.write({
        'qr_code': qr_data,
        'status': 'qr_ready',
        'last_qr_update': fields.Datetime.now()
    })
    print("Updated account with new QR code")

env.cr.commit()
print("✅ SUCCESS! QR code should now be visible at:")
print("🔗 https://arihantai.com/whatsapp/qr/1")
