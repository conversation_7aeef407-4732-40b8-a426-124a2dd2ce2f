# -*- coding: utf-8 -*-

import logging
from odoo import models, fields, api, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class WhatsAppMixin(models.AbstractModel):
    """
    Mixin class to add WhatsApp messaging capabilities to any model.
    
    Usage:
    1. Inherit from this mixin in your model
    2. Implement _get_whatsapp_recipients() method
    3. Optionally implement _get_whatsapp_template_variables() method
    4. Use the provided methods to send WhatsApp messages
    
    Example:
        class SaleOrder(models.Model):
            _name = 'sale.order'
            _inherit = ['sale.order', 'whatsapp.mixin']
            
            def _get_whatsapp_recipients(self):
                recipients = []
                if self.partner_id.mobile:
                    recipients.append(self.partner_id.mobile)
                return recipients
            
            def _get_whatsapp_template_variables(self):
                return {
                    'customer_name': self.partner_id.name,
                    'order_number': self.name,
                    'total_amount': self.amount_total,
                }
    """
    _name = 'whatsapp.mixin'
    _description = 'WhatsApp Messaging Mixin'

    # WhatsApp Integration Fields
    whatsapp_enabled = fields.Boolean('WhatsApp Enabled', default=True,
                                     help="Enable WhatsApp messaging for this record")
    whatsapp_account_id = fields.Many2one('whatsapp.account', string='WhatsApp Account',
                                         help="WhatsApp account to use for sending messages")
    whatsapp_message_ids = fields.One2many('whatsapp.message', 'res_id', 
                                          domain=lambda self: [('res_model', '=', self._name)],
                                          string='WhatsApp Messages')
    whatsapp_message_count = fields.Integer('WhatsApp Messages', compute='_compute_whatsapp_message_count')

    @api.depends('whatsapp_message_ids')
    def _compute_whatsapp_message_count(self):
        for record in self:
            record.whatsapp_message_count = len(record.whatsapp_message_ids)

    def _get_whatsapp_account(self):
        """Get WhatsApp account to use for sending messages"""
        if self.whatsapp_account_id:
            return self.whatsapp_account_id
        
        # Get default account
        return self.env['whatsapp.account'].get_default_account()

    def _get_whatsapp_recipients(self):
        """
        Override this method to return list of recipients for this record.
        Should return a list of phone numbers, WhatsApp IDs, or contact IDs.
        
        Returns:
            list: List of recipients (phone numbers, WhatsApp IDs, or contact IDs)
        """
        raise NotImplementedError("Please implement _get_whatsapp_recipients() method")

    def _get_whatsapp_template_variables(self):
        """
        Override this method to return template variables for this record.
        
        Returns:
            dict: Dictionary of template variables
        """
        return {
            'record_name': self.display_name if hasattr(self, 'display_name') else str(self),
            'record_id': self.id,
            'model_name': self._description,
        }

    def send_whatsapp_text(self, message_text, recipients=None, account_id=None):
        """Send WhatsApp text message"""
        self.ensure_one()
        
        if not self.whatsapp_enabled:
            raise UserError(_("WhatsApp messaging is disabled for this record."))
        
        # Get account
        account = self.env['whatsapp.account'].browse(account_id) if account_id else self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        if not recipients:
            recipients = self._get_whatsapp_recipients()
        
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        messages = []
        for recipient in recipients:
            message = account.send_text_message(
                recipient=recipient,
                message_text=message_text,
                res_model=self._name,
                res_id=self.id
            )
            messages.append(message)
        
        return messages

    def send_whatsapp_template(self, template_id, recipients=None, variables=None, account_id=None):
        """Send WhatsApp message from template"""
        self.ensure_one()
        
        if not self.whatsapp_enabled:
            raise UserError(_("WhatsApp messaging is disabled for this record."))
        
        # Get account
        account = self.env['whatsapp.account'].browse(account_id) if account_id else self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        if not recipients:
            recipients = self._get_whatsapp_recipients()
        
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        # Get template variables
        if not variables:
            variables = self._get_whatsapp_template_variables()
        
        messages = []
        for recipient in recipients:
            message = account.send_template_message(
                recipient=recipient,
                template_id=template_id,
                variables=variables,
                res_model=self._name,
                res_id=self.id
            )
            messages.append(message)
        
        return messages

    def send_whatsapp_image(self, image_data, image_name, caption=None, recipients=None, account_id=None):
        """Send WhatsApp image message"""
        self.ensure_one()
        
        if not self.whatsapp_enabled:
            raise UserError(_("WhatsApp messaging is disabled for this record."))
        
        # Get account
        account = self.env['whatsapp.account'].browse(account_id) if account_id else self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        if not recipients:
            recipients = self._get_whatsapp_recipients()
        
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        messages = []
        for recipient in recipients:
            message = account.send_image_message(
                recipient=recipient,
                image_data=image_data,
                image_name=image_name,
                caption=caption,
                res_model=self._name,
                res_id=self.id
            )
            messages.append(message)
        
        return messages

    def send_whatsapp_file(self, file_data, file_name, file_type='document', recipients=None, account_id=None):
        """Send WhatsApp file message"""
        self.ensure_one()
        
        if not self.whatsapp_enabled:
            raise UserError(_("WhatsApp messaging is disabled for this record."))
        
        # Get account
        account = self.env['whatsapp.account'].browse(account_id) if account_id else self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        if not recipients:
            recipients = self._get_whatsapp_recipients()
        
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        messages = []
        for recipient in recipients:
            message = account.send_file_message(
                recipient=recipient,
                file_data=file_data,
                file_name=file_name,
                file_type=file_type,
                res_model=self._name,
                res_id=self.id
            )
            messages.append(message)
        
        return messages

    def send_whatsapp_location(self, latitude, longitude, location_name=None, recipients=None, account_id=None):
        """Send WhatsApp location message"""
        self.ensure_one()
        
        if not self.whatsapp_enabled:
            raise UserError(_("WhatsApp messaging is disabled for this record."))
        
        # Get account
        account = self.env['whatsapp.account'].browse(account_id) if account_id else self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        if not recipients:
            recipients = self._get_whatsapp_recipients()
        
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        messages = []
        for recipient in recipients:
            message = account.send_location_message(
                recipient=recipient,
                latitude=latitude,
                longitude=longitude,
                location_name=location_name,
                res_model=self._name,
                res_id=self.id
            )
            messages.append(message)
        
        return messages

    def send_whatsapp_poll(self, question, options, selectable_count=1, recipients=None, account_id=None):
        """Send WhatsApp poll message"""
        self.ensure_one()
        
        if not self.whatsapp_enabled:
            raise UserError(_("WhatsApp messaging is disabled for this record."))
        
        # Get account
        account = self.env['whatsapp.account'].browse(account_id) if account_id else self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        if not recipients:
            recipients = self._get_whatsapp_recipients()
        
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        messages = []
        for recipient in recipients:
            message = account.send_poll_message(
                recipient=recipient,
                question=question,
                options=options,
                selectable_count=selectable_count,
                res_model=self._name,
                res_id=self.id
            )
            messages.append(message)
        
        return messages

    def action_send_whatsapp_message(self):
        """Open WhatsApp message composer"""
        self.ensure_one()
        
        # Get default account
        account = self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        recipients = self._get_whatsapp_recipients()
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        # Create message with first recipient
        message_vals = {
            'account_id': account.id,
            'res_model': self._name,
            'res_id': self.id,
        }
        
        # Set first recipient
        first_recipient = recipients[0]
        if isinstance(first_recipient, str):
            if '@' in first_recipient:
                message_vals['recipient_whatsapp_id'] = first_recipient
            else:
                message_vals['recipient_phone'] = first_recipient
        elif isinstance(first_recipient, int):
            message_vals['contact_id'] = first_recipient
        
        message = self.env['whatsapp.message'].create(message_vals)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Send WhatsApp Message'),
            'res_model': 'whatsapp.message',
            'res_id': message.id,
            'view_mode': 'form',
            'target': 'new',
        }

    def action_view_whatsapp_messages(self):
        """View WhatsApp messages for this record"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('WhatsApp Messages'),
            'res_model': 'whatsapp.message',
            'view_mode': 'tree,form',
            'domain': [('res_model', '=', self._name), ('res_id', '=', self.id)],
            'context': {
                'default_res_model': self._name,
                'default_res_id': self.id,
            },
        }

    @api.model
    def get_whatsapp_templates_for_model(self):
        """Get available WhatsApp templates for this model"""
        model = self.env['ir.model'].search([('model', '=', self._name)], limit=1)
        if model:
            return self.env['whatsapp.template'].search([
                ('active', '=', True),
                '|',
                ('model_ids', 'in', model.ids),
                ('model_ids', '=', False)
            ])
        return self.env['whatsapp.template'].search([('active', '=', True)])

    def create_whatsapp_queue(self, template_id, name=None):
        """Create WhatsApp queue for bulk messaging"""
        self.ensure_one()
        
        # Get default account
        account = self._get_whatsapp_account()
        if not account:
            raise UserError(_("No WhatsApp account available."))
        
        # Get recipients
        recipients = self._get_whatsapp_recipients()
        if not recipients:
            raise UserError(_("No WhatsApp recipients found for this record."))
        
        # Create queue
        queue_vals = {
            'name': name or f"Queue for {self.display_name}",
            'account_id': account.id,
            'template_id': template_id,
        }
        
        queue = self.env['whatsapp.queue'].create(queue_vals)
        
        # Add recipients
        variables = self._get_whatsapp_template_variables()
        for recipient in recipients:
            recipient_vals = {
                'queue_id': queue.id,
                'name': f"Recipient {recipient}",
            }
            
            if isinstance(recipient, str):
                if '@' in recipient:
                    recipient_vals['recipient_whatsapp_id'] = recipient
                else:
                    recipient_vals['recipient_phone'] = recipient
            elif isinstance(recipient, int):
                recipient_vals['contact_id'] = recipient
            
            if variables:
                import json
                recipient_vals['variables'] = json.dumps(variables)
            
            self.env['whatsapp.queue.recipient'].create(recipient_vals)
        
        return queue
