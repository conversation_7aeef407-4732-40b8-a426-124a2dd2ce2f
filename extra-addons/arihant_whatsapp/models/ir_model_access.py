# -*- coding: utf-8 -*-

from odoo import models, api


class IrModelAccess(models.Model):
    _inherit = 'ir.model.access'

    @api.model
    def create_whatsapp_access_rights(self):
        """Create access rights for WhatsApp models"""
        
        # Define access rights
        access_rights = [
            ('whatsapp.message.user', 'whatsapp.message', 'base.group_user'),
            ('whatsapp.template.user', 'whatsapp.template', 'base.group_user'),
            ('whatsapp.template.variable.user', 'whatsapp.template.variable', 'base.group_user'),
            ('whatsapp.contact.user', 'whatsapp.contact', 'base.group_user'),
            ('whatsapp.queue.user', 'whatsapp.queue', 'base.group_user'),
            ('whatsapp.queue.recipient.user', 'whatsapp.queue.recipient', 'base.group_user'),
        ]
        
        created_count = 0
        
        for access_name, model_name, group_ref in access_rights:
            # Check if model exists
            model = self.env['ir.model'].search([('model', '=', model_name)], limit=1)
            if not model:
                continue
            
            # Get group
            try:
                group = self.env.ref(group_ref)
            except ValueError:
                continue
            
            # Check if access right already exists
            existing = self.search([
                ('name', '=', access_name),
                ('model_id', '=', model.id)
            ])
            
            if existing:
                continue
            
            # Create access right
            self.create({
                'name': access_name,
                'model_id': model.id,
                'group_id': group.id,
                'perm_read': True,
                'perm_write': True,
                'perm_create': True,
                'perm_unlink': True,
            })
            
            created_count += 1
        
        return created_count
