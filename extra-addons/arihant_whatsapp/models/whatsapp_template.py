# -*- coding: utf-8 -*-

import logging
import json
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WhatsAppTemplate(models.Model):
    _name = 'whatsapp.template'
    _description = 'WhatsApp Message Template'
    _order = 'name'

    # Basic Information
    name = fields.Char('Template Name', required=True)
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)
    
    # Template Type and Content
    template_type = fields.Selection([
        ('text', 'Text Message'),
        ('image', 'Image with Caption'),
        ('file', 'File/Document'),
        ('location', 'Location'),
        ('poll', 'Poll'),
    ], string='Template Type', required=True, default='text')
    
    # Content
    message_text = fields.Text('Message Text', help="Use {{variable_name}} for dynamic content")
    caption = fields.Text('Caption', help="Caption for images/files. Use {{variable_name}} for dynamic content")
    
    # File Template
    default_attachment = fields.Binary('Default Attachment')
    default_attachment_name = fields.Char('Default Attachment Name')
    
    # Location Template
    default_latitude = fields.Float('Default Latitude', digits=(10, 6))
    default_longitude = fields.Float('Default Longitude', digits=(10, 6))
    default_location_name = fields.Char('Default Location Name')
    
    # Poll Template
    poll_question = fields.Char('Poll Question', help="Use {{variable_name}} for dynamic content")
    poll_options = fields.Text('Poll Options (JSON)', help="JSON array of poll options")
    poll_selectable_count = fields.Integer('Selectable Options', default=1)
    
    # Variables and Placeholders
    variable_ids = fields.One2many('whatsapp.template.variable', 'template_id', string='Variables')
    
    # Usage and Categories
    category = fields.Selection([
        ('notification', 'Notification'),
        ('marketing', 'Marketing'),
        ('support', 'Customer Support'),
        ('reminder', 'Reminder'),
        ('welcome', 'Welcome Message'),
        ('order', 'Order Related'),
        ('payment', 'Payment Related'),
        ('appointment', 'Appointment'),
        ('other', 'Other'),
    ], string='Category', default='notification')
    
    # Integration
    model_ids = fields.Many2many('ir.model', string='Related Models', 
                                help="Models that can use this template")
    
    # Statistics
    usage_count = fields.Integer('Usage Count', readonly=True, default=0)
    last_used = fields.Datetime('Last Used', readonly=True)

    @api.constrains('template_type', 'message_text', 'poll_question', 'poll_options')
    def _check_template_content(self):
        for record in self:
            if record.template_type == 'text' and not record.message_text:
                raise ValidationError(_("Text template requires message content."))
            elif record.template_type == 'poll':
                if not record.poll_question:
                    raise ValidationError(_("Poll template requires a question."))
                if record.poll_options:
                    try:
                        options = json.loads(record.poll_options)
                        if not isinstance(options, list) or len(options) < 2:
                            raise ValidationError(_("Poll options must be a JSON array with at least 2 options."))
                    except json.JSONDecodeError:
                        raise ValidationError(_("Poll options must be valid JSON array."))

    def action_create_message(self):
        """Create a new message from this template"""
        self.ensure_one()
        
        # Prepare values for new message
        vals = {
            'template_id': self.id,
            'is_template_message': True,
            'message_type': self.template_type,
        }
        
        # Copy template content
        if self.template_type == 'text':
            vals['message_text'] = self.message_text
        elif self.template_type == 'image':
            vals['attachment'] = self.default_attachment
            vals['attachment_name'] = self.default_attachment_name
            vals['caption'] = self.caption
        elif self.template_type == 'file':
            vals['attachment'] = self.default_attachment
            vals['attachment_name'] = self.default_attachment_name
        elif self.template_type == 'location':
            vals.update({
                'latitude': self.default_latitude,
                'longitude': self.default_longitude,
                'location_name': self.default_location_name,
            })
        elif self.template_type == 'poll':
            vals.update({
                'poll_question': self.poll_question,
                'poll_options': self.poll_options,
                'poll_selectable_count': self.poll_selectable_count,
            })
        
        # Create message
        message = self.env['whatsapp.message'].create(vals)
        
        # Update usage statistics
        self.write({
            'usage_count': self.usage_count + 1,
            'last_used': fields.Datetime.now()
        })
        
        # Return action to open the new message
        return {
            'type': 'ir.actions.act_window',
            'name': _('WhatsApp Message from Template'),
            'res_model': 'whatsapp.message',
            'res_id': message.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def render_template(self, variables=None):
        """Render template with variables"""
        variables = variables or {}
        
        rendered = {
            'message_text': self._render_text(self.message_text, variables),
            'caption': self._render_text(self.caption, variables),
            'poll_question': self._render_text(self.poll_question, variables),
            'location_name': self._render_text(self.default_location_name, variables),
        }
        
        # Render poll options
        if self.poll_options:
            try:
                options = json.loads(self.poll_options)
                rendered_options = [self._render_text(str(option), variables) for option in options]
                rendered['poll_options'] = json.dumps(rendered_options)
            except json.JSONDecodeError:
                rendered['poll_options'] = self.poll_options
        
        return rendered

    def _render_text(self, text, variables):
        """Render text with variables"""
        if not text:
            return text
        
        rendered_text = text
        for var_name, var_value in variables.items():
            placeholder = f"{{{{{var_name}}}}}"
            rendered_text = rendered_text.replace(placeholder, str(var_value))
        
        return rendered_text

    def create_message_from_template(self, account_id, recipient, variables=None, **kwargs):
        """Create and optionally send message from template"""
        self.ensure_one()
        
        # Render template with variables
        rendered = self.render_template(variables)
        
        # Prepare message values
        vals = {
            'account_id': account_id,
            'template_id': self.id,
            'is_template_message': True,
            'message_type': self.template_type,
            **kwargs
        }
        
        # Set recipient
        if isinstance(recipient, str):
            if '@' in recipient:
                vals['recipient_whatsapp_id'] = recipient
            else:
                vals['recipient_phone'] = recipient
        elif isinstance(recipient, int):
            vals['contact_id'] = recipient
        
        # Set content based on template type
        if self.template_type == 'text':
            vals['message_text'] = rendered['message_text']
        elif self.template_type == 'image':
            vals.update({
                'attachment': self.default_attachment,
                'attachment_name': self.default_attachment_name,
                'caption': rendered['caption'],
            })
        elif self.template_type == 'file':
            vals.update({
                'attachment': self.default_attachment,
                'attachment_name': self.default_attachment_name,
            })
        elif self.template_type == 'location':
            vals.update({
                'latitude': self.default_latitude,
                'longitude': self.default_longitude,
                'location_name': rendered['location_name'],
            })
        elif self.template_type == 'poll':
            vals.update({
                'poll_question': rendered['poll_question'],
                'poll_options': rendered['poll_options'],
                'poll_selectable_count': self.poll_selectable_count,
            })
        
        # Create message
        message = self.env['whatsapp.message'].create(vals)
        
        # Update usage statistics
        self.write({
            'usage_count': self.usage_count + 1,
            'last_used': fields.Datetime.now()
        })
        
        return message

    @api.model
    def get_templates_by_category(self, category=None, model_name=None):
        """Get templates filtered by category and/or model"""
        domain = [('active', '=', True)]
        
        if category:
            domain.append(('category', '=', category))
        
        if model_name:
            model = self.env['ir.model'].search([('model', '=', model_name)], limit=1)
            if model:
                domain.append(('model_ids', 'in', model.ids))
        
        return self.search(domain)


class WhatsAppTemplateVariable(models.Model):
    _name = 'whatsapp.template.variable'
    _description = 'WhatsApp Template Variable'
    _order = 'sequence, name'

    template_id = fields.Many2one('whatsapp.template', string='Template', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)
    name = fields.Char('Variable Name', required=True, help="Variable name without braces (e.g., customer_name)")
    description = fields.Char('Description', help="Description of what this variable represents")
    variable_type = fields.Selection([
        ('text', 'Text'),
        ('number', 'Number'),
        ('date', 'Date'),
        ('datetime', 'Date & Time'),
        ('boolean', 'Boolean'),
    ], string='Type', default='text')
    default_value = fields.Char('Default Value')
    required = fields.Boolean('Required', default=False)
    
    # Field mapping for automatic population
    field_name = fields.Char('Field Name', help="Field name from related model to auto-populate")
    model_id = fields.Many2one('ir.model', string='Model', help="Model to get field value from")

    @api.constrains('name')
    def _check_variable_name(self):
        for record in self:
            if not record.name.isidentifier():
                raise ValidationError(_("Variable name must be a valid identifier (letters, numbers, underscore only)."))

    def get_variable_value(self, record=None, default=None):
        """Get variable value from record or default"""
        if record and self.field_name and hasattr(record, self.field_name):
            value = getattr(record, self.field_name)
            
            # Format based on type
            if self.variable_type == 'date' and value:
                return value.strftime('%Y-%m-%d') if hasattr(value, 'strftime') else str(value)
            elif self.variable_type == 'datetime' and value:
                return value.strftime('%Y-%m-%d %H:%M:%S') if hasattr(value, 'strftime') else str(value)
            elif self.variable_type == 'boolean':
                return 'Yes' if value else 'No'
            else:
                return str(value) if value is not None else ''
        
        return default or self.default_value or ''
