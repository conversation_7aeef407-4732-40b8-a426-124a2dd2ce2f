# -*- coding: utf-8 -*-

import logging
import subprocess
import json
import base64
import os
import time
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WhatsAppMessage(models.Model):
    _name = 'whatsapp.message'
    _description = 'WhatsApp Message'
    _order = 'create_date desc'
    _rec_name = 'subject'

    # Basic Information
    subject = fields.Char('Subject', compute='_compute_subject', store=True)
    account_id = fields.Many2one('whatsapp.account', string='WhatsApp Account', required=True)
    
    # Message Type and Content
    message_type = fields.Selection([
        ('text', 'Text Message'),
        ('image', 'Image'),
        ('file', 'File/Document'),
        ('audio', 'Audio'),
        ('video', 'Video'),
        ('location', 'Location'),
        ('poll', 'Poll'),
        ('contact', 'Contact'),
    ], string='Message Type', required=True, default='text')
    
    # Recipients
    recipient_type = fields.Selection([
        ('individual', 'Individual'),
        ('group', 'Group'),
        ('broadcast', 'Broadcast List'),
    ], string='Recipient Type', required=True, default='individual')
    
    recipient_phone = fields.Char('Phone Number', help="International format (e.g., *************)")
    recipient_whatsapp_id = fields.Char('WhatsApp ID', help="WhatsApp ID for groups (e.g., <EMAIL>)")
    contact_id = fields.Many2one('whatsapp.contact', string='Contact')
    
    # Text Content
    message_text = fields.Text('Message Text')
    
    # File/Media Content
    attachment = fields.Binary('Attachment')
    attachment_name = fields.Char('Attachment Name')
    attachment_mimetype = fields.Char('MIME Type')
    caption = fields.Text('Caption', help="Caption for images/videos")
    
    # Location Content
    latitude = fields.Float('Latitude', digits=(10, 6))
    longitude = fields.Float('Longitude', digits=(10, 6))
    location_name = fields.Char('Location Name')
    
    # Poll Content
    poll_question = fields.Char('Poll Question')
    poll_options = fields.Text('Poll Options (JSON)', help="JSON array of poll options")
    poll_selectable_count = fields.Integer('Selectable Options', default=1, help="Number of options users can select")
    
    # Status and Tracking
    state = fields.Selection([
        ('draft', 'Draft'),
        ('queued', 'Queued'),
        ('sending', 'Sending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
        ('failed', 'Failed'),
    ], string='Status', default='draft', tracking=True)
    
    error_message = fields.Text('Error Message', readonly=True)
    sent_date = fields.Datetime('Sent Date', readonly=True)
    delivered_date = fields.Datetime('Delivered Date', readonly=True)
    read_date = fields.Datetime('Read Date', readonly=True)
    
    # Template and Automation
    template_id = fields.Many2one('whatsapp.template', string='Template')
    is_template_message = fields.Boolean('From Template', default=False)
    
    # Integration
    res_model = fields.Char('Related Model', help="Model name for integration")
    res_id = fields.Integer('Related Record ID', help="Record ID for integration")

    # Queue Management
    queue_id = fields.Many2one('whatsapp.queue', string='Message Queue', help="Queue this message belongs to")

    # Mudslide specific
    mudslide_command = fields.Text('Mudslide Command', readonly=True, help="Generated mudslide command")
    mudslide_response = fields.Text('Mudslide Response', readonly=True)

    @api.depends('message_type', 'message_text', 'attachment_name', 'poll_question')
    def _compute_subject(self):
        for record in self:
            if record.message_type == 'text':
                record.subject = (record.message_text or '')[:50] + ('...' if len(record.message_text or '') > 50 else '')
            elif record.message_type == 'poll':
                record.subject = f"Poll: {record.poll_question or 'Untitled'}"
            elif record.message_type in ['image', 'file', 'audio', 'video']:
                record.subject = f"{record.message_type.title()}: {record.attachment_name or 'Unnamed'}"
            elif record.message_type == 'location':
                record.subject = f"Location: {record.location_name or f'{record.latitude}, {record.longitude}'}"
            else:
                record.subject = f"{record.message_type.title()} Message"

    @api.constrains('recipient_phone', 'recipient_whatsapp_id', 'contact_id')
    def _check_recipient(self):
        for record in self:
            if not any([record.recipient_phone, record.recipient_whatsapp_id, record.contact_id]):
                raise ValidationError(_("Please specify at least one recipient (phone, WhatsApp ID, or contact)."))

    @api.constrains('message_type', 'message_text', 'attachment', 'latitude', 'longitude', 'poll_question')
    def _check_content(self):
        for record in self:
            if record.message_type == 'text' and not record.message_text:
                raise ValidationError(_("Text message requires message content."))
            elif record.message_type in ['image', 'file', 'audio', 'video'] and not record.attachment:
                raise ValidationError(_("Media message requires an attachment."))
            elif record.message_type == 'location' and (not record.latitude or not record.longitude):
                raise ValidationError(_("Location message requires latitude and longitude."))
            elif record.message_type == 'poll' and not record.poll_question:
                raise ValidationError(_("Poll message requires a question."))

    def action_send_message(self):
        """Send the WhatsApp message"""
        self.ensure_one()

        if self.state != 'draft':
            raise UserError(_("Only draft messages can be sent."))

        if not self.account_id:
            raise UserError(_("WhatsApp account is required."))

        try:
            self.write({'state': 'sending'})

            # Try API method first (for connected instances)
            if self.account_id.status == 'connected' and self.account_id.session_id:
                result = self._send_via_api()
                if result:
                    return result

            # Fallback to mudslide method
            return self._send_via_mudslide()

        except Exception as e:
            _logger.error(f"Error sending WhatsApp message {self.id}: {str(e)}")
            self.write({
                'state': 'failed',
                'error_message': str(e)
            })
            raise

    def _send_via_api(self):
        """Send message via WhatsApp service API"""
        try:
            import requests

            # Get recipient
            recipient = self._get_recipient()
            if not recipient.endswith('@c.us'):
                # Clean phone number and add WhatsApp format
                clean_phone = ''.join(filter(str.isdigit, recipient))
                if not clean_phone.startswith('91'):
                    clean_phone = '91' + clean_phone
                recipient = clean_phone + '@c.us'

            # WhatsApp service API endpoint
            api_url = f"http://**************:3001/api/instances/{self.account_id.session_id}/send"

            if self.message_type == 'text':
                payload = {
                    'to': recipient,
                    'message': self.message_text,
                    'type': 'text'
                }
            elif self.message_type in ['image', 'file', 'audio', 'video']:
                if not self.attachment:
                    raise UserError(_("Attachment is required for media messages"))

                # Determine mimetype
                if self.message_type == 'image':
                    mimetype = self.attachment_mimetype or 'image/jpeg'
                elif self.message_type == 'audio':
                    mimetype = self.attachment_mimetype or 'audio/mpeg'
                elif self.message_type == 'video':
                    mimetype = self.attachment_mimetype or 'video/mp4'
                else:
                    mimetype = self.attachment_mimetype or 'application/octet-stream'

                payload = {
                    'to': recipient,
                    'message': self.caption or '',
                    'type': 'media',
                    'media': {
                        'data': self.attachment,
                        'mimetype': mimetype,
                        'filename': self.attachment_name or 'file'
                    }
                }
            else:
                raise UserError(_("Message type %s not supported via API yet") % self.message_type)

            response = requests.post(api_url, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.write({
                        'state': 'sent',
                        'sent_date': fields.Datetime.now(),
                        'mudslide_response': f"API Success: {result}"
                    })

                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Message Sent'),
                            'message': _('WhatsApp message sent successfully via API.'),
                            'type': 'success',
                            'sticky': False,
                        }
                    }
                else:
                    raise Exception(result.get('error', 'Unknown API error'))
            else:
                raise Exception(f"API request failed with status {response.status_code}")

        except Exception as e:
            _logger.error(f"Error sending via API: {str(e)}")
            return None

    def _send_via_mudslide(self):
        """Send message via mudslide (fallback method)"""
        try:
            # Generate mudslide command based on message type
            command = self._generate_mudslide_command()

            if command:
                self.write({'mudslide_command': ' '.join(command)})

                # Execute mudslide command
                result = self._execute_mudslide_command(command)

                if result['success']:
                    self.write({
                        'state': 'sent',
                        'sent_date': fields.Datetime.now(),
                        'mudslide_response': result.get('output', '')
                    })

                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Message Sent'),
                            'message': _('WhatsApp message sent successfully via mudslide.'),
                            'type': 'success',
                            'sticky': False,
                        }
                    }
                else:
                    self.write({
                        'state': 'failed',
                        'error_message': result.get('error', 'Unknown error')
                    })
                    raise UserError(_("Failed to send message: %s") % result.get('error', 'Unknown error'))
            else:
                self.write({'state': 'failed', 'error_message': 'Could not generate mudslide command'})
                raise UserError(_("Could not generate mudslide command for this message type."))

        except Exception as e:
            _logger.error(f"Error sending via mudslide: {str(e)}")
            self.write({
                'state': 'failed',
                'error_message': str(e)
            })
            raise

    def _get_recipient(self):
        """Get the recipient identifier for mudslide"""
        if self.contact_id and self.contact_id.whatsapp_id:
            return self.contact_id.whatsapp_id
        elif self.recipient_whatsapp_id:
            return self.recipient_whatsapp_id
        elif self.recipient_phone:
            return self.recipient_phone
        else:
            raise UserError(_("No valid recipient found."))

    def _generate_mudslide_command(self):
        """Generate mudslide command based on message type"""
        try:
            recipient = self._get_recipient()
            base_command = ['mudslide']
            
            if self.message_type == 'text':
                return base_command + ['send', recipient, self.message_text]
                
            elif self.message_type == 'image':
                if not self.attachment:
                    raise UserError(_("Image attachment is required."))
                
                # Save attachment to temporary file
                temp_file = self._save_attachment_to_temp()
                command = base_command + ['send-image']
                
                if self.caption:
                    command.extend(['--caption', self.caption])
                
                command.extend([recipient, temp_file])
                return command
                
            elif self.message_type in ['file', 'audio', 'video']:
                if not self.attachment:
                    raise UserError(_("File attachment is required."))
                
                temp_file = self._save_attachment_to_temp()
                command = base_command + ['send-file']
                
                if self.message_type in ['audio', 'video']:
                    command.extend(['--type', self.message_type])
                
                command.extend([recipient, temp_file])
                return command
                
            elif self.message_type == 'location':
                return base_command + ['send-location', recipient, str(self.latitude), str(self.longitude)]
                
            elif self.message_type == 'poll':
                if not self.poll_options:
                    raise UserError(_("Poll options are required."))
                
                try:
                    options = json.loads(self.poll_options)
                except json.JSONDecodeError:
                    raise UserError(_("Poll options must be valid JSON array."))
                
                command = base_command + ['send-poll', recipient, self.poll_question]
                
                for option in options:
                    command.extend(['--item', str(option)])
                
                if self.poll_selectable_count > 1:
                    command.extend(['--selectable', str(self.poll_selectable_count)])
                
                return command
            
            return None
            
        except Exception as e:
            _logger.error(f"Error generating mudslide command: {str(e)}")
            raise UserError(_("Error generating command: %s") % str(e))

    def _save_attachment_to_temp(self):
        """Save attachment to temporary file and return path"""
        if not self.attachment:
            raise UserError(_("No attachment to save."))
        
        # Create temp directory if it doesn't exist
        temp_dir = '/tmp/whatsapp_attachments'
        os.makedirs(temp_dir, exist_ok=True)
        
        # Generate unique filename
        timestamp = int(time.time())
        filename = f"{timestamp}_{self.attachment_name or 'attachment'}"
        temp_path = os.path.join(temp_dir, filename)
        
        # Save attachment
        with open(temp_path, 'wb') as f:
            f.write(base64.b64decode(self.attachment))
        
        return temp_path

    def _execute_mudslide_command(self, command):
        """Execute mudslide command and return result"""
        try:
            _logger.info(f"Executing mudslide command: {' '.join(command)}")
            
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=60,  # 60 second timeout
                cwd='/tmp'
            )
            
            if result.returncode == 0:
                return {
                    'success': True,
                    'output': result.stdout,
                    'error': result.stderr
                }
            else:
                return {
                    'success': False,
                    'output': result.stdout,
                    'error': result.stderr or f"Command failed with return code {result.returncode}"
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Command timed out after 60 seconds'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def action_retry_send(self):
        """Retry sending failed message"""
        self.ensure_one()
        if self.state == 'failed':
            self.write({'state': 'draft', 'error_message': False})
            return self.action_send_message()
        else:
            raise UserError(_("Only failed messages can be retried."))

    @api.model
    def create_text_message(self, account_id, recipient, message_text, **kwargs):
        """Helper method to create text message"""
        vals = {
            'account_id': account_id,
            'message_type': 'text',
            'message_text': message_text,
            **kwargs
        }
        
        # Set recipient based on type
        if isinstance(recipient, str):
            if '@' in recipient:
                vals['recipient_whatsapp_id'] = recipient
            else:
                vals['recipient_phone'] = recipient
        elif isinstance(recipient, int):
            vals['contact_id'] = recipient
        
        return self.create(vals)

    @api.model
    def send_text_message(self, account_id, recipient, message_text, **kwargs):
        """Helper method to create and send text message immediately"""
        message = self.create_text_message(account_id, recipient, message_text, **kwargs)
        message.action_send_message()
        return message

    def action_view_related_record(self):
        """View the related record if available"""
        self.ensure_one()

        if not self.res_model or not self.res_id:
            raise UserError(_("No related record found."))

        return {
            'type': 'ir.actions.act_window',
            'name': _('Related Record'),
            'res_model': self.res_model,
            'res_id': self.res_id,
            'view_mode': 'form',
            'target': 'current',
        }
