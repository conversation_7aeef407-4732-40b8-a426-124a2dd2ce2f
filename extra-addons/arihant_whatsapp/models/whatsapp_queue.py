# -*- coding: utf-8 -*-

import logging
import time
from datetime import datetime, timedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WhatsAppQueue(models.Model):
    _name = 'whatsapp.queue'
    _description = 'WhatsApp Message Queue'
    _order = 'priority desc, scheduled_date, create_date'

    # Basic Information
    name = fields.Char('Queue Name', required=True)
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)
    
    # Queue Configuration
    account_id = fields.Many2one('whatsapp.account', string='WhatsApp Account', required=True)
    template_id = fields.Many2one('whatsapp.template', string='Message Template')
    
    # Scheduling
    scheduled_date = fields.Datetime('Scheduled Date', default=fields.Datetime.now)
    priority = fields.Selection([
        ('0', 'Low'),
        ('1', 'Normal'),
        ('2', 'High'),
        ('3', 'Urgent'),
    ], string='Priority', default='1')
    
    # Batch Settings
    batch_size = fields.Integer('Batch Size', default=10, help="Number of messages to send per batch")
    delay_between_messages = fields.Integer('Delay Between Messages (seconds)', default=2,
                                          help="Delay between individual messages to avoid rate limiting")
    delay_between_batches = fields.Integer('Delay Between Batches (minutes)', default=5,
                                         help="Delay between batches")
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('running', 'Running'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Progress Tracking
    total_recipients = fields.Integer('Total Recipients', compute='_compute_progress', store=True)
    messages_sent = fields.Integer('Messages Sent', default=0, readonly=True)
    messages_failed = fields.Integer('Messages Failed', default=0, readonly=True)
    progress_percentage = fields.Float('Progress %', compute='_compute_progress', store=True)
    
    # Execution Info
    started_date = fields.Datetime('Started Date', readonly=True)
    completed_date = fields.Datetime('Completed Date', readonly=True)
    last_batch_date = fields.Datetime('Last Batch Date', readonly=True)
    current_batch = fields.Integer('Current Batch', default=0, readonly=True)
    
    # Recipients
    recipient_ids = fields.One2many('whatsapp.queue.recipient', 'queue_id', string='Recipients')
    
    # Messages
    message_ids = fields.One2many('whatsapp.message', 'queue_id', string='Generated Messages')
    
    # Error Handling
    error_message = fields.Text('Error Message', readonly=True)
    retry_failed = fields.Boolean('Retry Failed Messages', default=True)
    max_retries = fields.Integer('Max Retries', default=3)

    @api.depends('recipient_ids', 'messages_sent', 'messages_failed')
    def _compute_progress(self):
        for record in self:
            record.total_recipients = len(record.recipient_ids)
            if record.total_recipients > 0:
                record.progress_percentage = ((record.messages_sent + record.messages_failed) / record.total_recipients) * 100
            else:
                record.progress_percentage = 0

    def action_schedule(self):
        """Schedule the queue for execution"""
        self.ensure_one()
        
        if self.state != 'draft':
            raise UserError(_("Only draft queues can be scheduled."))
        
        if not self.recipient_ids:
            raise UserError(_("Please add recipients before scheduling."))
        
        if not self.template_id:
            raise UserError(_("Please select a message template."))
        
        if self.account_id.status != 'connected':
            raise UserError(_("WhatsApp account must be connected."))
        
        self.write({
            'state': 'scheduled',
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Queue Scheduled'),
                'message': _('Message queue has been scheduled for execution.'),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_start(self):
        """Start queue execution"""
        self.ensure_one()
        
        if self.state not in ['scheduled', 'paused']:
            raise UserError(_("Only scheduled or paused queues can be started."))
        
        self.write({
            'state': 'running',
            'started_date': fields.Datetime.now() if not self.started_date else self.started_date,
        })
        
        # Start processing in background
        self._process_queue()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Queue Started'),
                'message': _('Message queue execution has started.'),
                'type': 'success',
                'sticky': False,
            }
        }

    def action_pause(self):
        """Pause queue execution"""
        self.ensure_one()
        
        if self.state != 'running':
            raise UserError(_("Only running queues can be paused."))
        
        self.write({'state': 'paused'})
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Queue Paused'),
                'message': _('Message queue execution has been paused.'),
                'type': 'info',
                'sticky': False,
            }
        }

    def action_cancel(self):
        """Cancel queue execution"""
        self.ensure_one()
        
        if self.state in ['completed', 'cancelled']:
            raise UserError(_("Queue is already completed or cancelled."))
        
        self.write({
            'state': 'cancelled',
            'completed_date': fields.Datetime.now(),
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Queue Cancelled'),
                'message': _('Message queue execution has been cancelled.'),
                'type': 'warning',
                'sticky': False,
            }
        }

    def _process_queue(self):
        """Process the queue (send messages in batches)"""
        try:
            # Get pending recipients
            pending_recipients = self.recipient_ids.filtered(lambda r: r.state == 'pending')
            
            if not pending_recipients:
                self.write({
                    'state': 'completed',
                    'completed_date': fields.Datetime.now(),
                })
                return
            
            # Process in batches
            batch_count = 0
            for i in range(0, len(pending_recipients), self.batch_size):
                # Check if queue is still running
                self.refresh()
                if self.state != 'running':
                    break
                
                batch = pending_recipients[i:i + self.batch_size]
                batch_count += 1
                
                _logger.info(f"Processing batch {batch_count} with {len(batch)} recipients")
                
                # Process batch
                self._process_batch(batch)
                
                # Update progress
                self.write({
                    'current_batch': batch_count,
                    'last_batch_date': fields.Datetime.now(),
                })
                
                # Delay between batches (except for last batch)
                if i + self.batch_size < len(pending_recipients) and self.delay_between_batches > 0:
                    time.sleep(self.delay_between_batches * 60)
            
            # Check if all messages are processed
            if not self.recipient_ids.filtered(lambda r: r.state == 'pending'):
                self.write({
                    'state': 'completed',
                    'completed_date': fields.Datetime.now(),
                })
            
        except Exception as e:
            _logger.error(f"Error processing queue {self.id}: {str(e)}")
            self.write({
                'state': 'failed',
                'error_message': str(e),
                'completed_date': fields.Datetime.now(),
            })

    def _process_batch(self, recipients):
        """Process a batch of recipients"""
        for recipient in recipients:
            try:
                # Check if queue is still running
                self.refresh()
                if self.state != 'running':
                    break
                
                # Create and send message
                message = self._create_message_for_recipient(recipient)
                message.action_send_message()
                
                # Update recipient status
                recipient.write({
                    'state': 'sent',
                    'message_id': message.id,
                    'sent_date': fields.Datetime.now(),
                })
                
                # Update queue counters
                self.write({'messages_sent': self.messages_sent + 1})
                
                # Delay between messages
                if self.delay_between_messages > 0:
                    time.sleep(self.delay_between_messages)
                
            except Exception as e:
                _logger.error(f"Error sending message to recipient {recipient.id}: {str(e)}")
                
                # Update recipient status
                recipient.write({
                    'state': 'failed',
                    'error_message': str(e),
                    'retry_count': recipient.retry_count + 1,
                })
                
                # Update queue counters
                self.write({'messages_failed': self.messages_failed + 1})

    def _create_message_for_recipient(self, recipient):
        """Create message for a specific recipient"""
        # Get variables for template rendering
        variables = {}
        if recipient.variables:
            try:
                import json
                variables = json.loads(recipient.variables)
            except json.JSONDecodeError:
                _logger.warning(f"Invalid JSON in recipient {recipient.id} variables")
        
        # Create message from template
        message = self.template_id.create_message_from_template(
            account_id=self.account_id.id,
            recipient=recipient.recipient_whatsapp_id or recipient.recipient_phone,
            variables=variables,
            queue_id=self.id,
        )
        
        return message

    def action_add_recipients_from_contacts(self):
        """Add recipients from WhatsApp contacts"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Add Recipients from Contacts'),
            'res_model': 'whatsapp.queue.add.recipients.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_queue_id': self.id},
        }

    def action_view_messages(self):
        """View generated messages"""
        self.ensure_one()
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Queue Messages'),
            'res_model': 'whatsapp.message',
            'view_mode': 'tree,form',
            'domain': [('queue_id', '=', self.id)],
        }

    @api.model
    def process_scheduled_queues(self):
        """Cron job to process scheduled queues"""
        scheduled_queues = self.search([
            ('state', '=', 'scheduled'),
            ('scheduled_date', '<=', fields.Datetime.now()),
        ])
        
        for queue in scheduled_queues:
            try:
                queue.action_start()
            except Exception as e:
                _logger.error(f"Error starting scheduled queue {queue.id}: {str(e)}")
                queue.write({
                    'state': 'failed',
                    'error_message': str(e),
                })


class WhatsAppQueueRecipient(models.Model):
    _name = 'whatsapp.queue.recipient'
    _description = 'WhatsApp Queue Recipient'
    _order = 'sequence, id'

    queue_id = fields.Many2one('whatsapp.queue', string='Queue', required=True, ondelete='cascade')
    sequence = fields.Integer('Sequence', default=10)
    
    # Recipient Info
    name = fields.Char('Recipient Name', required=True)
    recipient_phone = fields.Char('Phone Number')
    recipient_whatsapp_id = fields.Char('WhatsApp ID')
    contact_id = fields.Many2one('whatsapp.contact', string='Contact')
    
    # Template Variables
    variables = fields.Text('Template Variables (JSON)', help="JSON object with variables for template rendering")
    
    # Status
    state = fields.Selection([
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
        ('skipped', 'Skipped'),
    ], string='Status', default='pending')
    
    # Tracking
    message_id = fields.Many2one('whatsapp.message', string='Generated Message', readonly=True)
    sent_date = fields.Datetime('Sent Date', readonly=True)
    error_message = fields.Text('Error Message', readonly=True)
    retry_count = fields.Integer('Retry Count', default=0, readonly=True)

    @api.constrains('recipient_phone', 'recipient_whatsapp_id', 'contact_id')
    def _check_recipient(self):
        for record in self:
            if not any([record.recipient_phone, record.recipient_whatsapp_id, record.contact_id]):
                raise ValidationError(_("Please specify at least one recipient identifier."))

    def action_retry(self):
        """Retry sending to this recipient"""
        self.ensure_one()
        
        if self.state != 'failed':
            raise UserError(_("Only failed recipients can be retried."))
        
        if self.retry_count >= self.queue_id.max_retries:
            raise UserError(_("Maximum retry attempts reached."))
        
        self.write({
            'state': 'pending',
            'error_message': False,
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Retry Scheduled'),
                'message': _('Recipient has been marked for retry.'),
                'type': 'success',
                'sticky': False,
            }
        }
