# -*- coding: utf-8 -*-

import logging
import subprocess
import json
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class WhatsAppContact(models.Model):
    _name = 'whatsapp.contact'
    _description = 'WhatsApp Contact'
    _order = 'name'

    # Basic Information
    name = fields.Char('Contact Name', required=True)
    phone_number = fields.Char('Phone Number', help="International format (e.g., 3161234567890)")
    whatsapp_id = fields.Char('WhatsApp ID', help="WhatsApp ID (e.g., 123456789@c.<NAME_EMAIL>)")
    
    # Contact Type
    contact_type = fields.Selection([
        ('individual', 'Individual'),
        ('group', 'Group'),
    ], string='Contact Type', required=True, default='individual')
    
    # Additional Information
    description = fields.Text('Description')
    active = fields.Boolean('Active', default=True)
    
    # Group Information (if contact_type is 'group')
    group_subject = fields.Char('Group Subject')
    group_description = fields.Text('Group Description')
    is_group_admin = fields.Boolean('Is Group Admin', default=False)
    participant_count = fields.Integer('Participant Count', readonly=True)
    
    # Integration with Odoo contacts
    partner_id = fields.Many2one('res.partner', string='Related Partner')
    
    # Statistics
    message_count = fields.Integer('Message Count', compute='_compute_message_count', store=True)
    last_message_date = fields.Datetime('Last Message', compute='_compute_last_message', store=True)
    
    # Account Association
    account_ids = fields.Many2many('whatsapp.account', string='WhatsApp Accounts',
                                  help="Accounts that can send messages to this contact")

    @api.depends('whatsapp_id')
    def _compute_message_count(self):
        for record in self:
            if record.whatsapp_id:
                count = self.env['whatsapp.message'].search_count([
                    '|',
                    ('recipient_whatsapp_id', '=', record.whatsapp_id),
                    ('contact_id', '=', record.id)
                ])
                record.message_count = count
            else:
                record.message_count = 0

    @api.depends('whatsapp_id')
    def _compute_last_message(self):
        for record in self:
            if record.whatsapp_id:
                last_message = self.env['whatsapp.message'].search([
                    '|',
                    ('recipient_whatsapp_id', '=', record.whatsapp_id),
                    ('contact_id', '=', record.id)
                ], order='create_date desc', limit=1)
                record.last_message_date = last_message.create_date if last_message else False
            else:
                record.last_message_date = False

    @api.constrains('phone_number', 'whatsapp_id')
    def _check_contact_info(self):
        for record in self:
            if not record.phone_number and not record.whatsapp_id:
                raise ValidationError(_("Please provide either a phone number or WhatsApp ID."))

    @api.constrains('contact_type', 'whatsapp_id')
    def _check_group_id(self):
        for record in self:
            if record.contact_type == 'group' and record.whatsapp_id and not record.whatsapp_id.endswith('@g.us'):
                raise ValidationError(_("Group WhatsApp ID should end with '@g.us'."))

    def action_send_message(self):
        """Open message composer for this contact"""
        self.ensure_one()
        
        # Get default account
        default_account = self.env['whatsapp.account'].search([
            ('status', '=', 'connected'),
            ('is_default', '=', True)
        ], limit=1)
        
        if not default_account:
            default_account = self.env['whatsapp.account'].search([
                ('status', '=', 'connected')
            ], limit=1)
        
        if not default_account:
            raise UserError(_("No connected WhatsApp account found."))
        
        # Create message
        message_vals = {
            'account_id': default_account.id,
            'contact_id': self.id,
            'recipient_type': self.contact_type,
        }
        
        if self.whatsapp_id:
            message_vals['recipient_whatsapp_id'] = self.whatsapp_id
        elif self.phone_number:
            message_vals['recipient_phone'] = self.phone_number
        
        message = self.env['whatsapp.message'].create(message_vals)
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Send WhatsApp Message'),
            'res_model': 'whatsapp.message',
            'res_id': message.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_view_messages(self):
        """View all messages for this contact"""
        self.ensure_one()
        
        domain = [
            '|',
            ('recipient_whatsapp_id', '=', self.whatsapp_id),
            ('contact_id', '=', self.id)
        ]
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('WhatsApp Messages'),
            'res_model': 'whatsapp.message',
            'view_mode': 'tree,form',
            'domain': domain,
            'context': {'default_contact_id': self.id},
        }

    def action_refresh_group_info(self):
        """Refresh group information using mudslide"""
        self.ensure_one()
        
        if self.contact_type != 'group' or not self.whatsapp_id:
            raise UserError(_("This action is only available for groups with WhatsApp ID."))
        
        # Get connected account
        account = self.env['whatsapp.account'].search([
            ('status', '=', 'connected')
        ], limit=1)
        
        if not account:
            raise UserError(_("No connected WhatsApp account found."))
        
        try:
            # Use mudslide to get group info
            command = ['mudslide', 'list-group', self.whatsapp_id]
            
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=30,
                cwd='/tmp'
            )
            
            if result.returncode == 0:
                # Parse group info from output
                output = result.stdout.strip()
                lines = output.split('\n')
                
                # Extract participant count (this is a simplified parser)
                participant_count = 0
                for line in lines:
                    if 'participants' in line.lower():
                        # Try to extract number
                        import re
                        numbers = re.findall(r'\d+', line)
                        if numbers:
                            participant_count = int(numbers[0])
                        break
                
                self.write({
                    'participant_count': participant_count,
                })
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Group Info Updated'),
                        'message': _('Group information has been refreshed.'),
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                raise UserError(_("Failed to get group info: %s") % result.stderr)
                
        except subprocess.TimeoutExpired:
            raise UserError(_("Command timed out. Please try again."))
        except Exception as e:
            _logger.error(f"Error refreshing group info: {str(e)}")
            raise UserError(_("Error refreshing group info: %s") % str(e))

    @api.model
    def sync_groups_from_mudslide(self, account_id):
        """Sync groups from mudslide"""
        account = self.env['whatsapp.account'].browse(account_id)
        
        if not account.exists() or account.status != 'connected':
            raise UserError(_("WhatsApp account must be connected."))
        
        try:
            # Use mudslide to list groups
            command = ['mudslide', 'groups']
            
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=30,
                cwd='/tmp'
            )
            
            if result.returncode == 0:
                # Parse groups from output
                output = result.stdout.strip()
                lines = output.split('\n')
                
                groups_created = 0
                groups_updated = 0
                
                for line in lines:
                    if '@g.us' in line:
                        # Parse group ID and subject
                        parts = line.split()
                        if len(parts) >= 2:
                            group_id = parts[0]
                            group_subject = ' '.join(parts[1:])
                            
                            # Check if group exists
                            existing_group = self.search([
                                ('whatsapp_id', '=', group_id),
                                ('contact_type', '=', 'group')
                            ], limit=1)
                            
                            if existing_group:
                                existing_group.write({
                                    'group_subject': group_subject,
                                    'name': group_subject,
                                })
                                groups_updated += 1
                            else:
                                self.create({
                                    'name': group_subject,
                                    'whatsapp_id': group_id,
                                    'contact_type': 'group',
                                    'group_subject': group_subject,
                                    'account_ids': [(6, 0, [account_id])],
                                })
                                groups_created += 1
                
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Groups Synced'),
                        'message': _('Created: %d, Updated: %d groups.') % (groups_created, groups_updated),
                        'type': 'success',
                        'sticky': False,
                    }
                }
            else:
                raise UserError(_("Failed to list groups: %s") % result.stderr)
                
        except subprocess.TimeoutExpired:
            raise UserError(_("Command timed out. Please try again."))
        except Exception as e:
            _logger.error(f"Error syncing groups: {str(e)}")
            raise UserError(_("Error syncing groups: %s") % str(e))

    @api.model
    def create_from_partner(self, partner_id):
        """Create WhatsApp contact from res.partner"""
        partner = self.env['res.partner'].browse(partner_id)
        
        if not partner.exists():
            raise UserError(_("Partner not found."))
        
        # Check if contact already exists
        existing = self.search([('partner_id', '=', partner_id)], limit=1)
        if existing:
            return existing
        
        # Extract phone number (try mobile first, then phone)
        phone = partner.mobile or partner.phone
        if phone:
            # Clean phone number (remove spaces, dashes, etc.)
            import re
            phone = re.sub(r'[^\d+]', '', phone)
        
        vals = {
            'name': partner.name,
            'phone_number': phone,
            'partner_id': partner_id,
            'contact_type': 'individual',
            'description': f"Created from partner: {partner.name}",
        }
        
        return self.create(vals)

    def send_template_message(self, template_id, variables=None, account_id=None):
        """Send template message to this contact"""
        self.ensure_one()
        
        template = self.env['whatsapp.template'].browse(template_id)
        if not template.exists():
            raise UserError(_("Template not found."))
        
        # Get account
        if not account_id:
            account_id = self.account_ids[0].id if self.account_ids else None
        
        if not account_id:
            account = self.env['whatsapp.account'].search([
                ('status', '=', 'connected'),
                ('is_default', '=', True)
            ], limit=1)
            account_id = account.id if account else None
        
        if not account_id:
            raise UserError(_("No connected WhatsApp account found."))
        
        # Create message from template
        recipient = self.whatsapp_id or self.phone_number
        message = template.create_message_from_template(
            account_id=account_id,
            recipient=recipient,
            variables=variables
        )
        
        # Send message
        message.action_send_message()
        
        return message
