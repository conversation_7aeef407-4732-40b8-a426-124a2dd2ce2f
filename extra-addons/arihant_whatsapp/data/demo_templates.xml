<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo WhatsApp Templates -->
        
        <!-- Welcome Message Template -->
        <record id="template_welcome_message" model="whatsapp.template">
            <field name="name">Welcome Message</field>
            <field name="template_type">text</field>
            <field name="category">welcome</field>
            <field name="description">Welcome new customers or users</field>
            <field name="message_text">Welcome to {{company_name}}, {{customer_name}}! 🎉

We're excited to have you on board. Your account is now active and ready to use.

If you have any questions, feel free to reach out to us.

Best regards,
{{company_name}} Team</field>
        </record>

        <!-- Order Confirmation Template -->
        <record id="template_order_confirmation" model="whatsapp.template">
            <field name="name">Order Confirmation</field>
            <field name="template_type">text</field>
            <field name="category">order</field>
            <field name="description">Confirm customer orders</field>
            <field name="message_text">Hello {{customer_name}}, 

Your order {{order_number}} has been confirmed! ✅

📋 Order Details:
• Order Date: {{order_date}}
• Total Amount: {{currency}} {{total_amount}}
• Expected Delivery: {{delivery_date}}

Thank you for choosing {{company_name}}!

Track your order: {{order_url}}</field>
        </record>

        <!-- Payment Reminder Template -->
        <record id="template_payment_reminder" model="whatsapp.template">
            <field name="name">Payment Reminder</field>
            <field name="template_type">text</field>
            <field name="category">payment</field>
            <field name="description">Remind customers about pending payments</field>
            <field name="message_text">Dear {{customer_name}}, 💳

This is a friendly reminder that payment for invoice {{invoice_number}} is due.

📄 Invoice Details:
• Amount: {{currency}} {{amount_due}}
• Due Date: {{due_date}}
• Days Overdue: {{days_overdue}}

Please make the payment at your earliest convenience.

For any questions, contact us at {{contact_number}}.

Thank you!
{{company_name}}</field>
        </record>

        <!-- Appointment Reminder Template -->
        <record id="template_appointment_reminder" model="whatsapp.template">
            <field name="name">Appointment Reminder</field>
            <field name="template_type">text</field>
            <field name="category">appointment</field>
            <field name="description">Remind customers about upcoming appointments</field>
            <field name="message_text">Hi {{customer_name}}, 📅

This is a reminder about your upcoming appointment:

🕐 Date & Time: {{appointment_date}} at {{appointment_time}}
📍 Location: {{appointment_location}}
👨‍⚕️ With: {{service_provider}}

Please arrive 10 minutes early. If you need to reschedule, please call us at {{contact_number}}.

See you soon!
{{company_name}}</field>
        </record>

        <!-- Delivery Notification Template -->
        <record id="template_delivery_notification" model="whatsapp.template">
            <field name="name">Delivery Notification</field>
            <field name="template_type">text</field>
            <field name="category">notification</field>
            <field name="description">Notify customers about delivery status</field>
            <field name="message_text">Great news, {{customer_name}}! 🚚

Your order {{order_number}} is out for delivery and should arrive today.

📦 Delivery Details:
• Tracking Number: {{tracking_number}}
• Expected Time: {{delivery_time}}
• Delivery Address: {{delivery_address}}

Please ensure someone is available to receive the package.

{{company_name}}</field>
        </record>

        <!-- Customer Support Template -->
        <record id="template_support_response" model="whatsapp.template">
            <field name="name">Support Response</field>
            <field name="template_type">text</field>
            <field name="category">support</field>
            <field name="description">Respond to customer support requests</field>
            <field name="message_text">Hello {{customer_name}}, 🛠️

Thank you for contacting {{company_name}} support.

We have received your request regarding: {{issue_subject}}

📋 Ticket Details:
• Ticket Number: {{ticket_number}}
• Priority: {{priority}}
• Assigned to: {{assigned_agent}}

We will get back to you within {{response_time}} hours.

For urgent matters, call us at {{emergency_number}}.

Best regards,
{{company_name}} Support Team</field>
        </record>

        <!-- Marketing Promotion Template -->
        <record id="template_marketing_promotion" model="whatsapp.template">
            <field name="name">Marketing Promotion</field>
            <field name="template_type">text</field>
            <field name="category">marketing</field>
            <field name="description">Send promotional offers to customers</field>
            <field name="message_text">🎉 Special Offer for {{customer_name}}! 

{{promotion_title}}

💰 Get {{discount_percentage}}% OFF on {{product_category}}
⏰ Valid until: {{expiry_date}}
🏷️ Use code: {{promo_code}}

Shop now: {{shop_url}}

Don't miss out on this amazing deal!

{{company_name}}
*Terms and conditions apply</field>
        </record>

        <!-- Birthday Wishes Template -->
        <record id="template_birthday_wishes" model="whatsapp.template">
            <field name="name">Birthday Wishes</field>
            <field name="template_type">text</field>
            <field name="category">marketing</field>
            <field name="description">Send birthday wishes to customers</field>
            <field name="message_text">🎂 Happy Birthday, {{customer_name}}! 🎉

Wishing you a wonderful day filled with happiness and joy!

As a birthday gift, enjoy {{birthday_discount}}% OFF on your next purchase.

🎁 Use code: {{birthday_code}}
⏰ Valid for: {{validity_days}} days

Celebrate with us: {{shop_url}}

Many happy returns!
{{company_name}} Team</field>
        </record>

        <!-- Survey Request Template -->
        <record id="template_survey_request" model="whatsapp.template">
            <field name="name">Survey Request</field>
            <field name="template_type">text</field>
            <field name="category">other</field>
            <field name="description">Request customer feedback through surveys</field>
            <field name="message_text">Hi {{customer_name}}, 📝

We hope you're satisfied with your recent experience with {{company_name}}.

Your feedback is valuable to us! Please take 2 minutes to complete our survey:

📊 Survey: {{survey_title}}
🔗 Link: {{survey_url}}

As a thank you, you'll receive {{reward_points}} loyalty points upon completion.

Thank you for helping us improve!

{{company_name}} Team</field>
        </record>

        <!-- Event Invitation Template -->
        <record id="template_event_invitation" model="whatsapp.template">
            <field name="name">Event Invitation</field>
            <field name="template_type">text</field>
            <field name="category">marketing</field>
            <field name="description">Invite customers to events</field>
            <field name="message_text">🎪 You're Invited, {{customer_name}}!

{{event_name}}

📅 Date: {{event_date}}
🕐 Time: {{event_time}}
📍 Venue: {{event_venue}}

Join us for an exciting event featuring:
{{event_highlights}}

🎟️ RSVP: {{rsvp_url}}
📞 Questions? Call {{contact_number}}

We can't wait to see you there!

{{company_name}} Events Team</field>
        </record>

        <!-- Template Variables for Order Confirmation -->
        <record id="var_customer_name" model="whatsapp.template.variable">
            <field name="template_id" ref="template_order_confirmation"/>
            <field name="name">customer_name</field>
            <field name="description">Customer's name</field>
            <field name="variable_type">text</field>
            <field name="required">True</field>
            <field name="sequence">10</field>
        </record>

        <record id="var_order_number" model="whatsapp.template.variable">
            <field name="template_id" ref="template_order_confirmation"/>
            <field name="name">order_number</field>
            <field name="description">Order number</field>
            <field name="variable_type">text</field>
            <field name="required">True</field>
            <field name="sequence">20</field>
        </record>

        <record id="var_order_date" model="whatsapp.template.variable">
            <field name="template_id" ref="template_order_confirmation"/>
            <field name="name">order_date</field>
            <field name="description">Order date</field>
            <field name="variable_type">date</field>
            <field name="required">True</field>
            <field name="sequence">30</field>
        </record>

        <record id="var_total_amount" model="whatsapp.template.variable">
            <field name="template_id" ref="template_order_confirmation"/>
            <field name="name">total_amount</field>
            <field name="description">Total order amount</field>
            <field name="variable_type">number</field>
            <field name="required">True</field>
            <field name="sequence">40</field>
        </record>

        <record id="var_currency" model="whatsapp.template.variable">
            <field name="template_id" ref="template_order_confirmation"/>
            <field name="name">currency</field>
            <field name="description">Currency symbol</field>
            <field name="variable_type">text</field>
            <field name="default_value">$</field>
            <field name="sequence">50</field>
        </record>

        <record id="var_company_name" model="whatsapp.template.variable">
            <field name="template_id" ref="template_order_confirmation"/>
            <field name="name">company_name</field>
            <field name="description">Company name</field>
            <field name="variable_type">text</field>
            <field name="default_value">Your Company</field>
            <field name="sequence">60</field>
        </record>

    </data>
</odoo>
