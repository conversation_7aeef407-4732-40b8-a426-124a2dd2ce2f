<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- <PERSON><PERSON> job to check WhatsApp connections -->
        <record id="cron_check_whatsapp_connections" model="ir.cron">
            <field name="name">Check WhatsApp Connections</field>
            <field name="model_id" ref="model_whatsapp_account"/>
            <field name="state">code</field>
            <field name="code">model.cron_check_connections()</field>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Cron job to process scheduled WhatsApp queues -->
        <record id="cron_process_whatsapp_queues" model="ir.cron">
            <field name="name">Process WhatsApp Message Queues</field>
            <field name="model_id" ref="model_whatsapp_queue"/>
            <field name="state">code</field>
            <field name="code">model.process_scheduled_queues()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>
    </data>
</odoo>
