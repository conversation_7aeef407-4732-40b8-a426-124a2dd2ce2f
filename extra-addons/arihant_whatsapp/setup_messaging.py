#!/usr/bin/env python3
"""
Setup script to add the complete messaging functionality after basic module upgrade.

This script will:
1. Add security access rights for new models
2. Load the new view files
3. Add the messaging menus
4. Load demo data

Run this from Odoo shell after upgrading the basic module.
"""

def setup_messaging_system(env):
    """Complete setup of the WhatsApp messaging system"""
    
    print("🚀 Setting up WhatsApp Messaging System...")
    
    # Step 1: Create security access rights
    print("📋 Step 1: Creating security access rights...")
    try:
        created_count = env['ir.model.access'].create_whatsapp_access_rights()
        print(f"✅ Created {created_count} access rights")
    except Exception as e:
        print(f"❌ Error creating access rights: {e}")
        return False
    
    # Step 2: Load view files manually
    print("📋 Step 2: Loading view files...")
    view_files = [
        'views/whatsapp_message_views.xml',
        'views/whatsapp_template_views.xml', 
        'views/whatsapp_contact_views.xml',
        'views/whatsapp_queue_views.xml',
        'views/menu_messaging.xml',
    ]
    
    try:
        from odoo.tools import convert_file
        for view_file in view_files:
            print(f"  Loading {view_file}...")
            convert_file(env, 'arihant_whatsapp', view_file, {}, 'init', False, 'data')
        print("✅ All view files loaded successfully")
    except Exception as e:
        print(f"❌ Error loading view files: {e}")
        return False
    
    # Step 3: Load demo data
    print("📋 Step 3: Loading demo templates...")
    try:
        convert_file(env, 'arihant_whatsapp', 'data/demo_templates.xml', {}, 'init', True, 'demo')
        print("✅ Demo templates loaded successfully")
    except Exception as e:
        print(f"⚠️  Warning: Could not load demo data: {e}")
    
    # Step 4: Commit changes
    print("📋 Step 4: Committing changes...")
    try:
        env.cr.commit()
        print("✅ All changes committed successfully")
    except Exception as e:
        print(f"❌ Error committing changes: {e}")
        return False
    
    print("\n🎉 WhatsApp Messaging System setup completed successfully!")
    print("\n📱 Available features:")
    print("  • Complete message types (text, image, file, location, poll)")
    print("  • Template system with variables")
    print("  • Bulk messaging queues")
    print("  • Contact and group management")
    print("  • Integration mixin for other modules")
    print("\n🔍 Check the WhatsApp menu to access all features!")
    
    return True

def quick_test(env):
    """Quick test of the messaging system"""
    print("\n🧪 Running quick test...")
    
    try:
        # Test model access
        message_model = env['whatsapp.message']
        template_model = env['whatsapp.template']
        contact_model = env['whatsapp.contact']
        queue_model = env['whatsapp.queue']
        
        print("✅ All models accessible")
        
        # Test creating a template
        template = template_model.create({
            'name': 'Test Template',
            'template_type': 'text',
            'message_text': 'Hello {{customer_name}}! This is a test message.',
        })
        print(f"✅ Created test template: {template.name}")
        
        # Test template rendering
        rendered = template.render_template({'customer_name': 'John Doe'})
        print(f"✅ Template rendering works: {rendered['message_text']}")
        
        print("\n🎉 Quick test passed! System is ready for use.")
        return True
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        return False

if __name__ == "__main__":
    print("This script should be run from within Odoo shell.")
    print("\nTo run from Odoo shell:")
    print("1. Start Odoo shell: python3 odoo-bin shell -d your_database")
    print("2. Run: exec(open('/path/to/setup_messaging.py').read())")
    print("3. Run: setup_messaging_system(env)")
    print("4. Optional: quick_test(env)")
    print("\nOr run all at once:")
    print("exec(open('/mnt/extra-addons/arihant_whatsapp/setup_messaging.py').read()); setup_messaging_system(env); quick_test(env)")
