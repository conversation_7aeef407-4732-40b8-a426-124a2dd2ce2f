<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Queue Tree View -->
    <record id="view_whatsapp_queue_tree" model="ir.ui.view">
        <field name="name">whatsapp.queue.tree</field>
        <field name="model">whatsapp.queue</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Queues" decoration-success="state=='completed'" decoration-danger="state=='failed'" decoration-warning="state=='running'">
                <field name="name"/>
                <field name="account_id"/>
                <field name="template_id"/>
                <field name="scheduled_date"/>
                <field name="total_recipients"/>
                <field name="messages_sent"/>
                <field name="messages_failed"/>
                <field name="progress_percentage" widget="progressbar"/>
                <field name="state"/>
                <button name="action_start" type="object" string="Start" icon="fa-play"
                        invisible="state not in ['scheduled', 'paused']"/>
                <button name="action_pause" type="object" string="Pause" icon="fa-pause"
                        invisible="state != 'running'"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Queue Form View -->
    <record id="view_whatsapp_queue_form" model="ir.ui.view">
        <field name="name">whatsapp.queue.form</field>
        <field name="model">whatsapp.queue</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Queue">
                <header>
                    <button name="action_schedule" type="object" string="Schedule" class="btn-primary"
                            invisible="state != 'draft'"/>
                    <button name="action_start" type="object" string="Start" class="btn-success"
                            invisible="state not in ['scheduled', 'paused']"/>
                    <button name="action_pause" type="object" string="Pause" class="btn-warning"
                            invisible="state != 'running'"/>
                    <button name="action_cancel" type="object" string="Cancel" class="btn-danger"
                            invisible="state in ['completed', 'cancelled']"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,scheduled,running,completed"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_messages" type="object" class="oe_stat_button" icon="fa-envelope">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="messages_sent"/></span>
                                <span class="o_stat_text">Sent</span>
                            </div>
                        </button>
                        <button name="action_view_messages" type="object" class="oe_stat_button" icon="fa-exclamation-triangle">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value"><field name="messages_failed"/></span>
                                <span class="o_stat_text">Failed</span>
                            </div>
                        </button>
                    </div>

                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" invisible="active"/>

                    <group>
                        <group>
                            <field name="name"/>
                            <field name="account_id" required="1"/>
                            <field name="template_id" required="1"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="scheduled_date"/>
                            <field name="priority"/>
                            <field name="total_recipients" readonly="1"/>
                            <field name="progress_percentage" widget="progressbar" readonly="1"/>
                        </group>
                    </group>

                    <field name="description" placeholder="Queue description..."/>

                    <notebook>
                        <page string="Batch Settings" name="settings">
                            <group>
                                <group>
                                    <field name="batch_size"/>
                                    <field name="delay_between_messages"/>
                                </group>
                                <group>
                                    <field name="delay_between_batches"/>
                                    <field name="retry_failed"/>
                                    <field name="max_retries"/>
                                </group>
                            </group>
                        </page>

                        <page string="Recipients" name="recipients">
                            <field name="recipient_ids">
                                <tree editable="bottom" decoration-success="state=='sent'" decoration-danger="state=='failed'">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="recipient_phone"/>
                                    <field name="recipient_whatsapp_id"/>
                                    <field name="contact_id"/>
                                    <field name="variables"/>
                                    <field name="state"/>
                                    <field name="sent_date"/>
                                    <field name="error_message"/>
                                    <button name="action_retry" type="object" string="Retry" icon="fa-refresh"
                                            invisible="state != 'failed'"/>
                                </tree>
                            </field>
                            <div class="oe_clear">
                                <button name="action_add_recipients_from_contacts" type="object" string="Add from Contacts" class="btn-link"/>
                            </div>
                        </page>

                        <page string="Execution Log" name="log">
                            <group>
                                <group>
                                    <field name="started_date" readonly="1"/>
                                    <field name="completed_date" readonly="1"/>
                                    <field name="last_batch_date" readonly="1"/>
                                </group>
                                <group>
                                    <field name="current_batch" readonly="1"/>
                                    <field name="error_message" readonly="1" invisible="not error_message"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Queue Search View -->
    <record id="view_whatsapp_queue_search" model="ir.ui.view">
        <field name="name">whatsapp.queue.search</field>
        <field name="model">whatsapp.queue</field>
        <field name="arch" type="xml">
            <search string="WhatsApp Queues">
                <field name="name"/>
                <field name="account_id"/>
                <field name="template_id"/>
                
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Scheduled" name="scheduled" domain="[('state', '=', 'scheduled')]"/>
                <filter string="Running" name="running" domain="[('state', '=', 'running')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                
                <separator/>
                <filter string="Today" name="today" domain="[('scheduled_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('scheduled_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Account" name="group_account" context="{'group_by': 'account_id'}"/>
                    <filter string="Template" name="group_template" context="{'group_by': 'template_id'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'state'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'scheduled_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Queue Action -->
    <record id="action_whatsapp_queue" model="ir.actions.act_window">
        <field name="name">WhatsApp Queues</field>
        <field name="res_model">whatsapp.queue</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_whatsapp_queue_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first WhatsApp queue!
            </p>
            <p>
                Queues allow you to send bulk WhatsApp messages with templates.
                Perfect for marketing campaigns and mass notifications.
            </p>
        </field>
    </record>

    <!-- WhatsApp Queue Recipient Tree View -->
    <record id="view_whatsapp_queue_recipient_tree" model="ir.ui.view">
        <field name="name">whatsapp.queue.recipient.tree</field>
        <field name="model">whatsapp.queue.recipient</field>
        <field name="arch" type="xml">
            <tree string="Queue Recipients" editable="bottom" decoration-success="state=='sent'" decoration-danger="state=='failed'">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="recipient_phone"/>
                <field name="recipient_whatsapp_id"/>
                <field name="contact_id"/>
                <field name="variables"/>
                <field name="state"/>
                <field name="sent_date"/>
                <field name="retry_count"/>
                <field name="error_message"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Queue Recipient Form View -->
    <record id="view_whatsapp_queue_recipient_form" model="ir.ui.view">
        <field name="name">whatsapp.queue.recipient.form</field>
        <field name="model">whatsapp.queue.recipient</field>
        <field name="arch" type="xml">
            <form string="Queue Recipient">
                <header>
                    <button name="action_retry" type="object" string="Retry" class="btn-warning"
                            invisible="state != 'failed'"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="contact_id"/>
                            <field name="recipient_phone" invisible="contact_id"/>
                            <field name="recipient_whatsapp_id" invisible="contact_id"/>
                        </group>
                        <group>
                            <field name="sequence"/>
                            <field name="sent_date" readonly="1"/>
                            <field name="retry_count" readonly="1"/>
                        </group>
                    </group>

                    <group string="Template Variables">
                        <field name="variables" widget="text" placeholder='{"variable_name": "value", "another_var": "another_value"}'/>
                    </group>

                    <group string="Error Information" invisible="not error_message">
                        <field name="error_message" widget="text" readonly="1"/>
                    </group>

                    <group string="Generated Message" invisible="not message_id">
                        <field name="message_id" readonly="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
