<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Template Tree View -->
    <record id="view_whatsapp_template_tree" model="ir.ui.view">
        <field name="name">whatsapp.template.tree</field>
        <field name="model">whatsapp.template</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Templates">
                <field name="name"/>
                <field name="template_type"/>
                <field name="category"/>
                <field name="usage_count"/>
                <field name="last_used"/>
                <field name="active"/>
                <button name="action_create_message" type="object" string="Use Template" icon="fa-paper-plane"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Template Form View -->
    <record id="view_whatsapp_template_form" model="ir.ui.view">
        <field name="name">whatsapp.template.form</field>
        <field name="model">whatsapp.template</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Template">
                <header>
                    <button name="action_create_message" type="object" string="Create Message" class="btn-primary"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_whatsapp_message)d" type="action" class="oe_stat_button" icon="fa-envelope">
                            <field name="usage_count" widget="statinfo" string="Used"/>
                        </button>
                    </div>

                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" invisible="active"/>

                    <group>
                        <group>
                            <field name="name"/>
                            <field name="template_type" widget="radio"/>
                            <field name="category"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="last_used" readonly="1"/>
                            <field name="usage_count" readonly="1"/>
                        </group>
                    </group>

                    <field name="description" placeholder="Template description..."/>

                    <notebook>
                        <page string="Content" name="content">
                            <!-- Text Template -->
                            <group invisible="template_type != 'text'">
                                <field name="message_text" widget="text" nolabel="1" 
                                       placeholder="Enter your message text here. Use {{variable_name}} for dynamic content."/>
                            </group>

                            <!-- Image Template -->
                            <group invisible="template_type != 'image'">
                                <field name="default_attachment" filename="default_attachment_name"/>
                                <field name="default_attachment_name"/>
                                <field name="caption" widget="text" 
                                       placeholder="Image caption. Use {{variable_name}} for dynamic content."/>
                            </group>

                            <!-- File Template -->
                            <group invisible="template_type != 'file'">
                                <field name="default_attachment" filename="default_attachment_name"/>
                                <field name="default_attachment_name"/>
                            </group>

                            <!-- Location Template -->
                            <group invisible="template_type != 'location'">
                                <field name="default_latitude"/>
                                <field name="default_longitude"/>
                                <field name="default_location_name" 
                                       placeholder="Location name. Use {{variable_name}} for dynamic content."/>
                            </group>

                            <!-- Poll Template -->
                            <group invisible="template_type != 'poll'">
                                <field name="poll_question" 
                                       placeholder="Poll question. Use {{variable_name}} for dynamic content."/>
                                <field name="poll_options" widget="text" 
                                       placeholder='Poll options as JSON array, e.g., ["Option 1", "Option 2", "Option 3"]'/>
                                <field name="poll_selectable_count"/>
                            </group>
                        </page>

                        <page string="Variables" name="variables">
                            <field name="variable_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="variable_type"/>
                                    <field name="default_value"/>
                                    <field name="required"/>
                                </tree>
                            </field>
                        </page>

                        <page string="Integration" name="integration">
                            <group>
                                <field name="model_ids" widget="many2many_tags"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Template Search View -->
    <record id="view_whatsapp_template_search" model="ir.ui.view">
        <field name="name">whatsapp.template.search</field>
        <field name="model">whatsapp.template</field>
        <field name="arch" type="xml">
            <search string="WhatsApp Templates">
                <field name="name"/>
                <field name="description"/>
                <field name="message_text"/>
                <field name="category"/>
                
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="archived" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Text" name="text" domain="[('template_type', '=', 'text')]"/>
                <filter string="Image" name="image" domain="[('template_type', '=', 'image')]"/>
                <filter string="File" name="file" domain="[('template_type', '=', 'file')]"/>
                <filter string="Location" name="location" domain="[('template_type', '=', 'location')]"/>
                <filter string="Poll" name="poll" domain="[('template_type', '=', 'poll')]"/>
                
                <separator/>
                <filter string="Notification" name="notification" domain="[('category', '=', 'notification')]"/>
                <filter string="Marketing" name="marketing" domain="[('category', '=', 'marketing')]"/>
                <filter string="Support" name="support" domain="[('category', '=', 'support')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Type" name="group_type" context="{'group_by': 'template_type'}"/>
                    <filter string="Category" name="group_category" context="{'group_by': 'category'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Template Action -->
    <record id="action_whatsapp_template" model="ir.actions.act_window">
        <field name="name">WhatsApp Templates</field>
        <field name="res_model">whatsapp.template</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_whatsapp_template_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first WhatsApp template!
            </p>
            <p>
                Templates allow you to create reusable message formats with dynamic variables.
                Perfect for notifications, marketing campaigns, and customer support.
            </p>
        </field>
    </record>

    <!-- WhatsApp Template Variable Tree View -->
    <record id="view_whatsapp_template_variable_tree" model="ir.ui.view">
        <field name="name">whatsapp.template.variable.tree</field>
        <field name="model">whatsapp.template.variable</field>
        <field name="arch" type="xml">
            <tree string="Template Variables" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="description"/>
                <field name="variable_type"/>
                <field name="default_value"/>
                <field name="required"/>
                <field name="field_name"/>
                <field name="model_id"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Template Variable Form View -->
    <record id="view_whatsapp_template_variable_form" model="ir.ui.view">
        <field name="name">whatsapp.template.variable.form</field>
        <field name="model">whatsapp.template.variable</field>
        <field name="arch" type="xml">
            <form string="Template Variable">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="description"/>
                            <field name="variable_type"/>
                            <field name="default_value"/>
                            <field name="required"/>
                        </group>
                        <group>
                            <field name="sequence"/>
                            <field name="model_id"/>
                            <field name="field_name"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
