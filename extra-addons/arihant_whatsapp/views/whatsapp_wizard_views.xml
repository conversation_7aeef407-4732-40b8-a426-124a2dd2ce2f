<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Sync Wizard -->
    <record id="view_whatsapp_sync_wizard_form" model="ir.ui.view">
        <field name="name">whatsapp.sync.wizard.form</field>
        <field name="model">whatsapp.sync.wizard</field>
        <field name="arch" type="xml">
            <form string="Sync with Existing WhatsApp Instance">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="session_id" placeholder="Enter Session ID (e.g., test123)"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="account_id" readonly="1"/>
                        </group>
                        <group>
                            <button name="action_refresh_status" string="Check Status" 
                                    type="object" class="btn-secondary"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Instance Status">
                            <field name="instance_status" readonly="1" 
                                   widget="text" style="font-family: monospace;"/>
                        </page>
                        <page string="Available Instances">
                            <field name="available_instances" readonly="1" 
                                   widget="text" style="font-family: monospace;"/>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button name="action_sync_instance" string="Sync Instance" 
                            type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- WhatsApp Message Wizard -->
    <record id="view_whatsapp_message_wizard_form" model="ir.ui.view">
        <field name="name">whatsapp.message.wizard.form</field>
        <field name="model">whatsapp.message.wizard</field>
        <field name="arch" type="xml">
            <form string="Send WhatsApp Message">
                <sheet>
                    <div class="oe_title">
                        <h1>Send WhatsApp Message</h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="account_id"/>
                            <field name="recipient_phone" placeholder="************"/>
                            <field name="message_type"/>
                        </group>
                        <group>
                            <field name="use_template"/>
                            <field name="template_id" attrs="{'invisible': [('use_template', '=', False)], 'required': [('use_template', '=', True)]}"/>
                        </group>
                    </group>
                    
                    <group>
                        <field name="message_text" widget="text" 
                               attrs="{'required': [('message_type', '=', 'text')]}"/>
                    </group>
                    
                    <group attrs="{'invisible': [('use_template', '=', False)]}">
                        <field name="template_variables" widget="text" 
                               placeholder='{"customer_name": "John Doe", "order_number": "SO001"}'/>
                    </group>
                    
                    <group attrs="{'invisible': [('message_type', '=', 'text')]}">
                        <group>
                            <field name="attachment" filename="attachment_name"/>
                            <field name="attachment_name"/>
                        </group>
                        <group>
                            <field name="caption" widget="text" 
                                   attrs="{'invisible': [('message_type', '=', 'text')]}"/>
                        </group>
                    </group>
                    
                    <!-- Hidden fields for context -->
                    <group invisible="1">
                        <field name="res_model"/>
                        <field name="res_id"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_send_message" string="Send Message" 
                            type="object" class="btn-primary"/>
                    <button name="action_send_and_close" string="Send &amp; Close" 
                            type="object" class="btn-secondary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_whatsapp_sync_wizard" model="ir.actions.act_window">
        <field name="name">Sync with Existing Instance</field>
        <field name="res_model">whatsapp.sync.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <record id="action_whatsapp_message_wizard" model="ir.actions.act_window">
        <field name="name">Send WhatsApp Message</field>
        <field name="res_model">whatsapp.message.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
