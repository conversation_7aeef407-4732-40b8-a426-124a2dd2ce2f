<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Account Tree View -->
        <record id="view_whatsapp_account_tree" model="ir.ui.view">
            <field name="name">whatsapp.account.tree</field>
            <field name="model">whatsapp.account</field>
            <field name="arch" type="xml">
                <tree decoration-success="status=='connected'" decoration-warning="status=='qr_ready'" decoration-danger="status=='error'">
                    <field name="name"/>
                    <field name="phone_number"/>
                    <field name="whatsapp_name"/>
                    <field name="status" widget="badge" decoration-success="status=='connected'" decoration-warning="status in ['connecting','qr_ready']" decoration-danger="status=='error'"/>
                    <field name="last_connection_attempt"/>
                    <field name="active"/>
                    <field name="is_default"/>
                </tree>
            </field>
        </record>

        <!-- WhatsApp Account Form View -->
        <record id="view_whatsapp_account_form" model="ir.ui.view">
            <field name="name">whatsapp.account.form</field>
            <field name="model">whatsapp.account</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_generate_qr_code" string="Generate QR Code" type="object"
                                class="btn-primary" invisible="status in ['connecting', 'qr_ready', 'connected']"
                                title="Generate WhatsApp Web QR code using mudslide integration"/>
                        <button name="generate_real_whatsapp_qr" string="Generate Real WhatsApp QR" type="object"
                                class="btn-success" invisible="status in ['connecting', 'qr_ready', 'connected']"
                                title="Generate authentic WhatsApp QR using the restored working method"/>
                        <button name="generate_test_qr_code" string="Generate Test QR Code" type="object"
                                class="btn-info" title="Generate a test QR code to verify the display system"/>
                        <button name="test_database_fix" string="Test Database Fix" type="object"
                                class="btn-info" title="Test if database transaction issues are fixed"/>
                        <button name="test_mudslide_installation" string="Test Mudslide" type="object"
                                class="btn-warning" title="Test mudslide installation and configuration"/>
                        <button name="action_refresh_qr" string="Refresh QR" type="object"
                                class="btn-secondary" invisible="status not in ['connecting', 'qr_ready']"/>
                        <button name="action_connect_existing" string="Connect Existing Instance" type="object"
                                class="btn-info" invisible="status == 'connected'"
                                title="Connect to an existing WhatsApp instance from the dashboard"/>
                        <button name="action_quick_sync_test123" string="Quick Connect test123" type="object"
                                class="btn-success" invisible="status == 'connected'"
                                title="Quickly connect to the test123 instance"/>
                        <button name="action_disconnect" string="Disconnect" type="object"
                                class="btn-danger" invisible="status not in ['connecting', 'qr_ready', 'connected']"/>
                        <field name="status" widget="statusbar"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button class="oe_stat_button" type="object" name="action_generate_qr_code"
                                    invisible="status != 'disconnected'" icon="fa-qrcode">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Connect</span>
                                    <span class="o_stat_text">WhatsApp</span>
                                </div>
                            </button>
                        </div>

                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="name"/>
                                <field name="phone_number"/>
                                <field name="active"/>
                                <field name="is_default"/>
                            </group>
                            <group name="connection_info" string="Connection Information">
                                <field name="whatsapp_name" readonly="1"/>
                                <field name="whatsapp_number" readonly="1"/>
                                <field name="last_connection_attempt" readonly="1"/>
                                <field name="connection_attempts" readonly="1"/>
                            </group>
                        </group>

                        <!-- QR Code Display -->
                        <group string="QR Code Authentication" invisible="status not in ['connecting', 'qr_ready']">
                            <div class="text-center" style="padding: 20px;">
                                <h3>Scan QR Code with WhatsApp</h3>
                                <p>Open WhatsApp on your phone → Settings → Linked Devices → Link a Device</p>
                                <div class="qr_code_container" style="margin: 20px auto; max-width: 300px; text-align: center;">
                                    <field name="qr_code_url" widget="url" invisible="qr_code_url == False"
                                           string="QR Code Link" readonly="1"/>
                                    <div invisible="qr_code_url != False"
                                         style="padding: 50px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 8px; text-align: center; width: 300px; margin: 0 auto;">
                                        <i class="fa fa-spinner fa-spin fa-2x" style="color: #6c757d;" title="Loading QR Code"/>
                                        <p style="margin-top: 10px; color: #6c757d;">Generating QR Code...</p>
                                    </div>
                                </div>
                                <p class="text-muted">Click the QR Code Link above to view the QR code in a new tab</p>
                                <div style="margin-top: 15px;">
                                    <button name="action_refresh_qr" string="Refresh QR Code" type="object"
                                            class="btn btn-secondary" invisible="status not in ['qr_ready']"/>
                                </div>
                            </div>
                        </group>

                        <!-- Configuration -->
                        <group string="Configuration">
                            <group>
                                <field name="auto_reconnect"/>
                                <field name="webhook_url"/>
                            </group>
                            <group>
                                <field name="api_token" password="True"/>
                                <field name="session_id" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- WhatsApp Account Action -->
        <record id="action_whatsapp_account" model="ir.actions.act_window">
            <field name="name">WhatsApp Accounts</field>
            <field name="res_model">whatsapp.account</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first WhatsApp account configuration
                </p>
                <p>
                    Configure your WhatsApp Business API accounts to start sending and receiving messages.
                </p>
            </field>
        </record>
    </data>
</odoo>
