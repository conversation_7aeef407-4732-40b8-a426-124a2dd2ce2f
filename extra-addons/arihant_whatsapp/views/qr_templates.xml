<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- QR Code Widget Template -->
        <template id="QRWidget" name="WhatsApp QR Widget">
            <div class="whatsapp_qr_container">
                <div t-if="state.status === 'connecting' or state.status === 'qr_ready'">
                    <h4>Connect WhatsApp Account</h4>
                    <div class="whatsapp_qr_instructions">
                        <h5>How to scan QR Code:</h5>
                        <ol>
                            <li>Open WhatsApp on your phone</li>
                            <li>Go to Settings → Linked Devices</li>
                            <li>Tap "Link a Device"</li>
                            <li>Scan this QR code</li>
                        </ol>
                    </div>
                    
                    <div t-if="qrCodeImageUrl" class="qr_code_display">
                        <img t-att-src="qrCodeImageUrl" class="whatsapp_qr_code" alt="WhatsApp QR Code"/>
                        <p class="text-muted">QR Code refreshes automatically every 30 seconds</p>
                    </div>
                    
                    <div t-else="" class="whatsapp_qr_loading">
                        <i class="fa fa-spinner fa-spin fa-2x whatsapp_loading_spinner"></i>
                        <p>Generating QR Code...</p>
                    </div>
                </div>
                
                <div t-elif="state.status === 'connected'" class="whatsapp_message whatsapp_message_success">
                    <i class="fa fa-check-circle"></i>
                    WhatsApp is connected successfully!
                </div>
                
                <div t-elif="state.status === 'error'" class="whatsapp_message whatsapp_message_error">
                    <i class="fa fa-exclamation-triangle"></i>
                    Connection failed. Please try again.
                </div>
            </div>
        </template>

        <!-- QR Not Available Template -->
        <template id="qr_not_available" name="QR Code Not Available">
            <html>
                <head>
                    <title>QR Code Not Available</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            text-align: center;
                            padding: 50px;
                            background-color: #f8f9fa;
                        }
                        .container {
                            max-width: 400px;
                            margin: 0 auto;
                            background: white;
                            padding: 30px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        .icon {
                            font-size: 48px;
                            color: #dc3545;
                            margin-bottom: 20px;
                        }
                        h1 {
                            color: #343a40;
                            margin-bottom: 15px;
                        }
                        p {
                            color: #6c757d;
                            line-height: 1.5;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="icon">⚠️</div>
                        <h1>QR Code Not Available</h1>
                        <p>The QR code for this WhatsApp account is not available. Please generate a new QR code from the account settings.</p>
                    </div>
                </body>
            </html>
        </template>

        <!-- Connection Status Template -->
        <template id="connection_status" name="WhatsApp Connection Status">
            <div class="whatsapp_connection_info">
                <h5>Connection Information</h5>
                <div class="info_item">
                    <span class="info_label">Status:</span>
                    <span class="info_value">
                        <span t-att-class="'whatsapp_status_badge whatsapp_status_' + status" t-esc="status.title()"/>
                    </span>
                </div>
                <div class="info_item" t-if="whatsapp_name">
                    <span class="info_label">WhatsApp Name:</span>
                    <span class="info_value" t-esc="whatsapp_name"/>
                </div>
                <div class="info_item" t-if="whatsapp_number">
                    <span class="info_label">WhatsApp Number:</span>
                    <span class="info_value" t-esc="whatsapp_number"/>
                </div>
                <div class="info_item" t-if="last_connection_attempt">
                    <span class="info_label">Last Attempt:</span>
                    <span class="info_value" t-esc="last_connection_attempt"/>
                </div>
            </div>
        </template>
    </data>
</odoo>
