<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Message Tree View -->
    <record id="view_whatsapp_message_tree" model="ir.ui.view">
        <field name="name">whatsapp.message.tree</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Messages" decoration-success="state=='sent'" decoration-danger="state=='failed'" decoration-warning="state=='queued'">
                <field name="create_date"/>
                <field name="subject"/>
                <field name="message_type"/>
                <field name="account_id"/>
                <field name="recipient_phone"/>
                <field name="recipient_whatsapp_id"/>
                <field name="state"/>
                <field name="sent_date"/>
                <button name="action_send_message" type="object" string="Send" icon="fa-paper-plane"
                        invisible="state != 'draft'"/>
                <button name="action_retry_send" type="object" string="Retry" icon="fa-refresh"
                        invisible="state != 'failed'"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Message Form View -->
    <record id="view_whatsapp_message_form" model="ir.ui.view">
        <field name="name">whatsapp.message.form</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Message">
                <header>
                    <button name="action_send_message" type="object" string="Send Message"
                            class="btn-primary" invisible="state != 'draft'"/>
                    <button name="action_retry_send" type="object" string="Retry Send"
                            class="btn-warning" invisible="state != 'failed'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,queued,sending,sent"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_related_record" type="object" class="oe_stat_button" icon="fa-external-link"
                                invisible="not res_model or not res_id">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Related Record</span>
                            </div>
                        </button>
                    </div>
                    
                    <group>
                        <group>
                            <field name="account_id" required="1"/>
                            <field name="message_type" widget="radio"/>
                            <field name="template_id" invisible="not is_template_message"/>
                            <field name="is_template_message" invisible="1"/>
                        </group>
                        <group>
                            <field name="recipient_type"/>
                            <field name="contact_id" invisible="recipient_type != 'individual'"/>
                            <field name="recipient_phone" invisible="contact_id"/>
                            <field name="recipient_whatsapp_id" invisible="contact_id"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Content" name="content">
                            <!-- Text Message -->
                            <group invisible="message_type != 'text'">
                                <field name="message_text" widget="text" nolabel="1"/>
                            </group>

                            <!-- Image/File Message -->
                            <group invisible="message_type not in ['image', 'file', 'audio', 'video']">
                                <field name="attachment" filename="attachment_name"/>
                                <field name="attachment_name"/>
                                <field name="caption" invisible="message_type not in ['image', 'video']"/>
                            </group>

                            <!-- Location Message -->
                            <group invisible="message_type != 'location'">
                                <field name="latitude"/>
                                <field name="longitude"/>
                                <field name="location_name"/>
                            </group>

                            <!-- Poll Message -->
                            <group invisible="message_type != 'poll'">
                                <field name="poll_question"/>
                                <field name="poll_options" widget="text"/>
                                <field name="poll_selectable_count"/>
                            </group>
                        </page>

                        <page string="Status &amp; Tracking" name="tracking">
                            <group>
                                <group>
                                    <field name="sent_date" readonly="1"/>
                                    <field name="delivered_date" readonly="1"/>
                                    <field name="read_date" readonly="1"/>
                                </group>
                                <group>
                                    <field name="queue_id" readonly="1"/>
                                    <field name="error_message" readonly="1" invisible="not error_message"/>
                                </group>
                            </group>
                        </page>

                        <page string="Integration" name="integration">
                            <group>
                                <field name="res_model" readonly="1"/>
                                <field name="res_id" readonly="1"/>
                            </group>
                        </page>

                        <page string="Technical" name="technical">
                            <group>
                                <field name="mudslide_command" widget="text" readonly="1"/>
                                <field name="mudslide_response" widget="text" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Message Search View -->
    <record id="view_whatsapp_message_search" model="ir.ui.view">
        <field name="name">whatsapp.message.search</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <search string="WhatsApp Messages">
                <field name="subject"/>
                <field name="message_text"/>
                <field name="recipient_phone"/>
                <field name="recipient_whatsapp_id"/>
                <field name="account_id"/>
                <field name="template_id"/>
                
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Sent" name="sent" domain="[('state', '=', 'sent')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                
                <separator/>
                <filter string="Text Messages" name="text" domain="[('message_type', '=', 'text')]"/>
                <filter string="Images" name="images" domain="[('message_type', '=', 'image')]"/>
                <filter string="Files" name="files" domain="[('message_type', '=', 'file')]"/>
                <filter string="Polls" name="polls" domain="[('message_type', '=', 'poll')]"/>
                
                <separator/>
                <filter string="Today" name="today" domain="[('create_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('create_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Account" name="group_account" context="{'group_by': 'account_id'}"/>
                    <filter string="Message Type" name="group_type" context="{'group_by': 'message_type'}"/>
                    <filter string="Status" name="group_status" context="{'group_by': 'state'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'create_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Message Action -->
    <record id="action_whatsapp_message" model="ir.actions.act_window">
        <field name="name">WhatsApp Messages</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_whatsapp_message_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first WhatsApp message!
            </p>
            <p>
                Send text messages, images, files, locations, and polls through WhatsApp using mudslide.
            </p>
        </field>
    </record>

    <!-- Quick Send Message Action -->
    <record id="action_whatsapp_message_compose" model="ir.actions.act_window">
        <field name="name">Send WhatsApp Message</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{
            'default_message_type': 'text',
            'default_state': 'draft',
            'form_view_initial_mode': 'edit'
        }</field>
    </record>

    <!-- Received Messages Action -->
    <record id="action_whatsapp_received_messages" model="ir.actions.act_window">
        <field name="name">Received Messages</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('state', '=', 'delivered')]</field>
        <field name="context">{'search_default_today': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No received messages yet!
            </p>
            <p>
                Incoming WhatsApp messages will appear here once your webhook is configured.
            </p>
        </field>
    </record>
</odoo>
