<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Contact Tree View -->
    <record id="view_whatsapp_contact_tree" model="ir.ui.view">
        <field name="name">whatsapp.contact.tree</field>
        <field name="model">whatsapp.contact</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Contacts">
                <field name="name"/>
                <field name="contact_type"/>
                <field name="phone_number"/>
                <field name="whatsapp_id"/>
                <field name="message_count"/>
                <field name="last_message_date"/>
                <field name="active"/>
                <button name="action_send_message" type="object" string="Send Message" icon="fa-paper-plane"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Contact Form View -->
    <record id="view_whatsapp_contact_form" model="ir.ui.view">
        <field name="name">whatsapp.contact.form</field>
        <field name="model">whatsapp.contact</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Contact">
                <header>
                    <button name="action_send_message" type="object" string="Send Message" class="btn-primary"/>
                    <button name="action_refresh_group_info" type="object" string="Refresh Group Info"
                            invisible="contact_type != 'group'"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_messages" type="object" class="oe_stat_button" icon="fa-envelope">
                            <field name="message_count" widget="statinfo" string="Messages"/>
                        </button>
                    </div>

                    <widget name="web_ribbon" title="Archived" bg_color="bg-danger" invisible="active"/>

                    <group>
                        <group>
                            <field name="name"/>
                            <field name="contact_type" widget="radio"/>
                            <field name="phone_number" invisible="contact_type == 'group'"/>
                            <field name="whatsapp_id"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="partner_id" invisible="contact_type == 'group'"/>
                            <field name="last_message_date" readonly="1"/>
                        </group>
                    </group>

                    <!-- Group Information -->
                    <group string="Group Information" invisible="contact_type != 'group'">
                        <group>
                            <field name="group_subject"/>
                            <field name="participant_count" readonly="1"/>
                            <field name="is_group_admin"/>
                        </group>
                        <group>
                            <field name="group_description" widget="text"/>
                        </group>
                    </group>

                    <field name="description" placeholder="Contact description..."/>

                    <notebook>
                        <page string="WhatsApp Accounts" name="accounts">
                            <field name="account_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="phone_number"/>
                                    <field name="status"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Contact Search View -->
    <record id="view_whatsapp_contact_search" model="ir.ui.view">
        <field name="name">whatsapp.contact.search</field>
        <field name="model">whatsapp.contact</field>
        <field name="arch" type="xml">
            <search string="WhatsApp Contacts">
                <field name="name"/>
                <field name="phone_number"/>
                <field name="whatsapp_id"/>
                <field name="partner_id"/>
                
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="archived" domain="[('active', '=', False)]"/>
                
                <separator/>
                <filter string="Individuals" name="individuals" domain="[('contact_type', '=', 'individual')]"/>
                <filter string="Groups" name="groups" domain="[('contact_type', '=', 'group')]"/>
                
                <separator/>
                <filter string="With Partner" name="with_partner" domain="[('partner_id', '!=', False)]"/>
                <filter string="Recent Messages" name="recent_messages" 
                        domain="[('last_message_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Type" name="group_type" context="{'group_by': 'contact_type'}"/>
                    <filter string="Partner" name="group_partner" context="{'group_by': 'partner_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Contact Action -->
    <record id="action_whatsapp_contact" model="ir.actions.act_window">
        <field name="name">WhatsApp Contacts</field>
        <field name="res_model">whatsapp.contact</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_whatsapp_contact_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add your first WhatsApp contact!
            </p>
            <p>
                Manage your WhatsApp contacts and groups. You can sync groups from mudslide
                and link contacts to Odoo partners.
            </p>
        </field>
    </record>

    <!-- Sync Groups Action -->
    <record id="action_sync_whatsapp_groups" model="ir.actions.server">
        <field name="name">Sync WhatsApp Groups</field>
        <field name="model_id" ref="model_whatsapp_contact"/>
        <field name="state">code</field>
        <field name="code">
# Get default account
account = env['whatsapp.account'].search([('status', '=', 'connected')], limit=1)
if account:
    action = env['whatsapp.contact'].sync_groups_from_mudslide(account.id)
else:
    raise UserError("No connected WhatsApp account found.")
        </field>
    </record>
</odoo>
