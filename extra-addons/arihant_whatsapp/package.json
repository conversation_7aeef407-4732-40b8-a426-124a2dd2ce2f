{"name": "arihant-whatsapp-service", "version": "1.0.0", "description": "WhatsApp Web Service for Odoo Integration", "main": "whatsapp_service.js", "scripts": {"start": "node whatsapp_service.js", "dev": "nodemon whatsapp_service.js", "install-deps": "npm install"}, "dependencies": {"whatsapp-web.js": "^1.23.0", "qrcode": "^1.5.3", "puppeteer": "^21.0.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "keywords": ["whatsapp", "odoo", "integration", "qr-code", "messaging"], "author": "Arihant AI", "license": "MIT", "engines": {"node": ">=16.0.0"}}