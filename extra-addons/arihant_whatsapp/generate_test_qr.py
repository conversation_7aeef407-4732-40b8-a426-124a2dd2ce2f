#!/usr/bin/env python3
"""
Generate a test QR code and insert it directly into the database
"""

import base64
import qrcode
from io import BytesIO
import psycopg2

def generate_test_qr():
    """Generate a test QR code"""
    
    qr_content = """WhatsApp Web QR Code Test

Account: Main WhatsApp Business
URL: https://arihantai.com/whatsapp/qr/1

This is a test QR code to verify the display system.
For real WhatsApp integration, generate a new QR code in Odoo.

Instructions:
1. This QR code should display properly in your browser
2. If you can see this QR code, the system is working
3. Generate a real QR code using the "Generate QR Code" button in Odoo

System: Arihant AI WhatsApp Integration
Status: Test QR Code - Not for actual WhatsApp scanning
Timestamp: 2025-06-02 11:15:00"""

    qr = qrcode.QRCode(
        version=2,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(qr_content)
    qr.make(fit=True)

    # Create QR code image
    img = qr.make_image(fill_color="black", back_color="white")

    # Convert to base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    qr_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

    return qr_data

def update_database():
    """Update the database with test QR code"""
    
    try:
        # Generate QR code
        qr_data = generate_test_qr()
        print(f"✅ Generated QR code: {len(qr_data)} characters")
        
        # Connect to database
        conn = psycopg2.connect(
            host="localhost",
            database="arihantai.com",
            user="odoo",
            password="odoo"
        )
        
        cur = conn.cursor()
        
        # Check if account exists
        cur.execute("SELECT id, name FROM whatsapp_account WHERE id = 1")
        result = cur.fetchone()
        
        if result:
            print(f"✅ Found account: ID={result[0]}, Name={result[1]}")
            
            # Update QR code
            cur.execute("""
                UPDATE whatsapp_account 
                SET qr_code = %s, 
                    status = 'qr_ready',
                    last_qr_update = NOW()
                WHERE id = 1
            """, (qr_data,))
            
            conn.commit()
            print("✅ QR code updated in database")
            
        else:
            print("❌ Account ID 1 not found, creating new account")
            
            # Create new account
            cur.execute("""
                INSERT INTO whatsapp_account (name, phone_number, qr_code, status, last_qr_update)
                VALUES (%s, %s, %s, %s, NOW())
            """, ("Main WhatsApp Business", "+**********", qr_data, "qr_ready"))
            
            conn.commit()
            print("✅ New account created with QR code")
        
        cur.close()
        conn.close()
        
        print("\n✅ SUCCESS! QR code should now be visible at:")
        print("🔗 https://arihantai.com/whatsapp/qr/1")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

if __name__ == '__main__':
    print("=== Generating Test QR Code ===")
    success = update_database()
    if success:
        print("\n🎉 Test QR code generated successfully!")
        print("Try accessing https://arihantai.com/whatsapp/qr/1 now")
    else:
        print("\n❌ Failed to generate test QR code")
