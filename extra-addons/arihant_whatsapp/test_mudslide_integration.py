#!/usr/bin/env python3
"""
Test script to verify mudslide integration exactly as Odoo module does
"""

import subprocess
import time
import os
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mudslide_integration():
    """Test mudslide integration exactly like Odoo module"""
    
    session_id = "test_integration_check"
    session_dir = f"/tmp/whatsapp_sessions/{session_id}"
    
    try:
        logger.info(f"Starting WhatsApp session {session_id} using mudslide")

        # Create session directory
        os.makedirs(session_dir, exist_ok=True)

        # Check if mudslide is available
        result = subprocess.run(['which', 'mudslide'], capture_output=True, text=True)
        if result.returncode != 0:
            logger.error("mudslide not found. Please install: sudo npm install -g mudslide")
            return False

        logger.info(f"Starting mudslide login for session {session_id}")

        # Start mudslide login process
        cmd = ['mudslide', 'login']
        logger.info(f"Running command: {' '.join(cmd)}")

        # Start the mudslide process
        process = subprocess.Popen(
            cmd,
            cwd=session_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # Wait for QR code generation (timeout after 20 seconds)
        timeout = 20
        start_time = time.time()
        qr_data = None
        qr_text_lines = []
        qr_started = False

        while time.time() - start_time < timeout:
            # Check if process is still running
            if process.poll() is not None:
                break

            # Read output line by line
            try:
                line = process.stdout.readline()
                if line:
                    line_stripped = line.strip()
                    logger.info(f"Mudslide output: {line_stripped}")

                    # Check for QR code start
                    if 'scan the QR code' in line.lower():
                        logger.info("QR code section detected")
                        qr_started = True
                        continue

                    # Look for QR code in terminal output
                    if qr_started and ('█' in line or '▄' in line or '▀' in line):
                        qr_text_lines.append(line.rstrip())
                        logger.info(f"QR line captured: {len(line.rstrip())} chars")

                    # Check for login success or completion
                    if 'Logged in' in line or 'success' in line.lower():
                        logger.info("WhatsApp login successful")
                        break

                    # If we have enough QR lines, break
                    if len(qr_text_lines) > 15:  # QR codes are typically 17-25 lines
                        logger.info(f"Collected {len(qr_text_lines)} QR code lines")
                        break

            except Exception as e:
                logger.error(f"Error reading mudslide output: {e}")
                break

            time.sleep(0.2)

        # Clean up process
        if process.poll() is None:
            process.terminate()
            time.sleep(1)
            if process.poll() is None:
                process.kill()

        # Check stderr for any errors
        try:
            stderr_output = process.stderr.read()
            if stderr_output:
                logger.info(f"Mudslide stderr: {stderr_output}")
        except:
            pass

        # Report results
        logger.info(f"=== MUDSLIDE INTEGRATION TEST RESULTS ===")
        logger.info(f"QR code section detected: {qr_started}")
        logger.info(f"QR text lines captured: {len(qr_text_lines)}")
        logger.info(f"Process exit code: {process.returncode}")
        
        if qr_text_lines:
            logger.info(f"First QR line sample: {qr_text_lines[0][:50]}...")
            logger.info(f"Last QR line sample: {qr_text_lines[-1][:50]}...")
            
            # Save QR lines for inspection
            qr_info = {
                'qr_lines': qr_text_lines,
                'total_lines': len(qr_text_lines),
                'timestamp': time.time(),
                'session_id': session_id,
                'status': 'qr_captured'
            }
            
            qr_file = os.path.join(session_dir, 'qr_test_data.json')
            with open(qr_file, 'w') as f:
                json.dump(qr_info, f, indent=2)
            
            logger.info(f"QR data saved to: {qr_file}")
            logger.info("✅ SUCCESS: QR code lines captured successfully!")
            return True
        else:
            logger.warning("❌ FAILED: No QR code lines captured")
            return False

    except Exception as e:
        logger.error(f"Error in mudslide integration test: {str(e)}")
        return False

if __name__ == '__main__':
    print("Testing mudslide integration...")
    success = test_mudslide_integration()
    if success:
        print("✅ Mudslide integration test PASSED")
    else:
        print("❌ Mudslide integration test FAILED")
