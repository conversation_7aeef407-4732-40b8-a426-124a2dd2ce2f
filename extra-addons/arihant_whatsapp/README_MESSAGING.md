# WhatsApp Messaging System Documentation

## Overview

The Arihant WhatsApp Integration module provides a comprehensive messaging system that supports all mudslide message types and offers scalable architecture for integration with other Odoo modules.

## Features

### Message Types Supported
- **Text Messages** - Simple text messages
- **Image Messages** - Images with optional captions
- **File Messages** - Documents, audio, and video files
- **Location Messages** - GPS coordinates with location names
- **Poll Messages** - Interactive polls with multiple options
- **Contact Messages** - Contact information sharing

### Core Components

#### 1. WhatsApp Accounts (`whatsapp.account`)
- Manage multiple WhatsApp accounts
- QR code authentication using mudslide
- Connection status monitoring
- Message statistics tracking

#### 2. WhatsApp Messages (`whatsapp.message`)
- Individual message records
- Support for all mudslide message types
- Status tracking (draft, sent, delivered, read, failed)
- Integration with other Odoo models

#### 3. WhatsApp Templates (`whatsapp.template`)
- Reusable message templates
- Dynamic variable substitution
- Category-based organization
- Usage statistics

#### 4. WhatsApp Contacts (`whatsapp.contact`)
- Contact and group management
- Integration with Odoo partners
- Group synchronization from mudslide
- Message history tracking

#### 5. WhatsApp Queues (`whatsapp.queue`)
- Bulk messaging system
- Batch processing with rate limiting
- Scheduled message sending
- Progress tracking and retry logic

#### 6. WhatsApp Mixin (`whatsapp.mixin`)
- Easy integration for other modules
- Standard methods for sending messages
- Template variable management
- Recipient handling

## Installation and Setup

### Prerequisites
- Mudslide CLI tool installed and configured
- WhatsApp account for authentication
- Odoo 17.0 or later

### Installation Steps
1. Install the module: `pip install -e /path/to/arihant_whatsapp`
2. Update module list in Odoo
3. Install "Arihant AI WhatsApp Integration" module
4. Configure WhatsApp account and generate QR code
5. Scan QR code with WhatsApp mobile app

## Usage Guide

### Basic Message Sending

#### Send Text Message
```python
# Get default account
account = self.env['whatsapp.account'].get_default_account()

# Send text message
message = account.send_text_message(
    recipient="**********",  # Phone number
    message_text="Hello from Odoo!"
)
```

#### Send Image Message
```python
import base64

# Read image file
with open('/path/to/image.jpg', 'rb') as f:
    image_data = base64.b64encode(f.read())

# Send image
message = account.send_image_message(
    recipient="**********",
    image_data=image_data,
    image_name="image.jpg",
    caption="Check out this image!"
)
```

#### Send File Message
```python
# Send document
message = account.send_file_message(
    recipient="**********",
    file_data=file_data,
    file_name="document.pdf",
    file_type="document"  # or "audio", "video"
)
```

#### Send Location Message
```python
message = account.send_location_message(
    recipient="**********",
    latitude=40.7128,
    longitude=-74.0060,
    location_name="New York City"
)
```

#### Send Poll Message
```python
message = account.send_poll_message(
    recipient="**********",
    question="What's your favorite color?",
    options=["Red", "Blue", "Green", "Yellow"],
    selectable_count=1
)
```

### Template System

#### Create Template
```python
template = self.env['whatsapp.template'].create({
    'name': 'Welcome Message',
    'template_type': 'text',
    'category': 'welcome',
    'message_text': 'Welcome {{customer_name}}! Your account {{account_number}} is ready.',
})
```

#### Send Template Message
```python
variables = {
    'customer_name': 'John Doe',
    'account_number': 'ACC123456'
}

message = account.send_template_message(
    recipient="**********",
    template_id=template.id,
    variables=variables
)
```

### Bulk Messaging with Queues

#### Create Message Queue
```python
# Create queue
queue = self.env['whatsapp.queue'].create({
    'name': 'Monthly Newsletter',
    'account_id': account.id,
    'template_id': template.id,
    'batch_size': 10,
    'delay_between_messages': 2,  # seconds
    'delay_between_batches': 5,   # minutes
})

# Add recipients
recipients_data = [
    {
        'name': 'Customer 1',
        'recipient_phone': '**********',
        'variables': '{"customer_name": "John Doe"}',
    },
    {
        'name': 'Customer 2',
        'recipient_phone': '**********',
        'variables': '{"customer_name": "Jane Smith"}',
    },
]

for recipient_data in recipients_data:
    recipient_data['queue_id'] = queue.id
    self.env['whatsapp.queue.recipient'].create(recipient_data)

# Schedule and start queue
queue.action_schedule()
queue.action_start()
```

## Integration with Other Modules

### Using WhatsApp Mixin

#### Step 1: Inherit from Mixin
```python
class SaleOrder(models.Model):
    _name = 'sale.order'
    _inherit = ['sale.order', 'whatsapp.mixin']
```

#### Step 2: Implement Required Methods
```python
def _get_whatsapp_recipients(self):
    """Return list of recipients for this record"""
    recipients = []
    if self.partner_id.mobile:
        recipients.append(self.partner_id.mobile)
    return recipients

def _get_whatsapp_template_variables(self):
    """Return template variables for this record"""
    return {
        'customer_name': self.partner_id.name,
        'order_number': self.name,
        'total_amount': self.amount_total,
    }
```

#### Step 3: Use Mixin Methods
```python
# Send text message
self.send_whatsapp_text("Your order has been confirmed!")

# Send template message
template = self.env['whatsapp.template'].search([('name', '=', 'Order Confirmation')], limit=1)
self.send_whatsapp_template(template.id)

# Create bulk queue
queue = self.create_whatsapp_queue(template.id, "Order Confirmations")
```

### Adding WhatsApp Buttons to Views

```xml
<record id="view_order_form_whatsapp" model="ir.ui.view">
    <field name="name">sale.order.form.whatsapp</field>
    <field name="model">sale.order</field>
    <field name="inherit_id" ref="sale.view_order_form"/>
    <field name="arch" type="xml">
        <xpath expr="//div[@name='button_box']" position="inside">
            <button name="action_view_whatsapp_messages" type="object" 
                    class="oe_stat_button" icon="fa-whatsapp">
                <field name="whatsapp_message_count" widget="statinfo" string="WhatsApp"/>
            </button>
        </xpath>
        <xpath expr="//header" position="inside">
            <button name="action_send_whatsapp_message" type="object" 
                    string="Send WhatsApp" class="btn-primary"/>
        </xpath>
    </field>
</record>
```

## Mudslide Commands Generated

The system automatically generates appropriate mudslide commands:

- **Text**: `mudslide send <recipient> <message>`
- **Image**: `mudslide send-image --caption <caption> <recipient> <file>`
- **File**: `mudslide send-file --type <type> <recipient> <file>`
- **Location**: `mudslide send-location <recipient> <lat> <lng>`
- **Poll**: `mudslide send-poll <recipient> <question> --item <option1> --item <option2> --selectable <count>`

## Configuration Options

### Account Settings
- **Auto Reconnect**: Automatically reconnect when disconnected
- **Webhook URL**: URL for receiving incoming messages
- **API Token**: Token for API authentication

### Queue Settings
- **Batch Size**: Number of messages per batch
- **Message Delay**: Delay between individual messages
- **Batch Delay**: Delay between batches
- **Max Retries**: Maximum retry attempts for failed messages

## Monitoring and Troubleshooting

### Message Status Tracking
- **Draft**: Message created but not sent
- **Queued**: Message added to queue
- **Sending**: Message being sent
- **Sent**: Message sent successfully
- **Delivered**: Message delivered (if supported)
- **Read**: Message read (if supported)
- **Failed**: Message sending failed

### Error Handling
- Failed messages can be retried manually or automatically
- Error messages are logged for troubleshooting
- Queue processing includes retry logic with exponential backoff

### Logging
- All mudslide commands and responses are logged
- Connection status changes are tracked
- Queue processing progress is monitored

## Best Practices

1. **Rate Limiting**: Use appropriate delays to avoid WhatsApp rate limits
2. **Template Management**: Organize templates by category and purpose
3. **Recipient Validation**: Validate phone numbers before sending
4. **Error Handling**: Implement proper error handling in integrations
5. **Testing**: Test with small batches before large campaigns
6. **Monitoring**: Monitor queue progress and message status regularly

## API Reference

### WhatsApp Account Methods
- `send_text_message(recipient, message_text, **kwargs)`
- `send_image_message(recipient, image_data, image_name, caption=None, **kwargs)`
- `send_file_message(recipient, file_data, file_name, file_type='document', **kwargs)`
- `send_location_message(recipient, latitude, longitude, location_name=None, **kwargs)`
- `send_poll_message(recipient, question, options, selectable_count=1, **kwargs)`
- `send_template_message(recipient, template_id, variables=None, **kwargs)`

### WhatsApp Mixin Methods
- `send_whatsapp_text(message_text, recipients=None, account_id=None)`
- `send_whatsapp_template(template_id, recipients=None, variables=None, account_id=None)`
- `send_whatsapp_image(image_data, image_name, caption=None, recipients=None, account_id=None)`
- `send_whatsapp_file(file_data, file_name, file_type='document', recipients=None, account_id=None)`
- `send_whatsapp_location(latitude, longitude, location_name=None, recipients=None, account_id=None)`
- `send_whatsapp_poll(question, options, selectable_count=1, recipients=None, account_id=None)`

## Support

For issues and questions:
1. Check the Odoo logs for error messages
2. Verify mudslide installation and configuration
3. Test mudslide commands manually
4. Check WhatsApp account connection status
5. Review message queue status and error logs
