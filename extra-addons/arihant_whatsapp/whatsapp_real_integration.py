#!/usr/bin/env python3
"""
Real WhatsApp Integration Script for Odoo
This script provides actual WhatsApp Web QR code generation and session management
"""

import os
import sys
import json
import time
import base64
import subprocess
import logging
from datetime import datetime
import argparse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WhatsAppRealIntegration:
    """Real WhatsApp Web integration using mudslide or alternative methods"""

    def __init__(self, session_id, phone_number=None):
        self.session_id = session_id
        self.phone_number = phone_number
        self.session_dir = f"/tmp/whatsapp_sessions/{session_id}"
        self.ensure_session_dir()

    def ensure_session_dir(self):
        """Ensure session directory exists"""
        os.makedirs(self.session_dir, exist_ok=True)

    def check_mudslide_availability(self):
        """Check if mudslide is available and working"""
        try:
            # Check if npx is available
            result = subprocess.run(['which', 'npx'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("npx not found. Please install Node.js and npm")
                return False

            # Try to run mudslide help
            result = subprocess.run(['npx', 'mudslide', '--help'],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                logger.info("Mudslide is available")
                return True
            else:
                logger.warning(f"Mudslide not working: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.warning("Mudslide check timed out")
            return False
        except Exception as e:
            logger.error(f"Error checking mudslide: {e}")
            return False

    def generate_qr_with_mudslide(self):
        """Generate QR code using mudslide"""
        try:
            logger.info(f"Starting mudslide session {self.session_id}")

            # Command to run mudslide (updated syntax)
            cmd = [
                'mudslide', 'login'
            ]

            logger.info(f"Running: {' '.join(cmd)}")

            # Start mudslide process
            process = subprocess.Popen(
                cmd,
                cwd=self.session_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Monitor output for QR code
            qr_data = None
            timeout = 60
            start_time = time.time()

            while time.time() - start_time < timeout:
                if process.poll() is not None:
                    break

                # Read output
                try:
                    line = process.stdout.readline()
                    if line:
                        logger.info(f"Mudslide: {line.strip()}")

                        # Check for QR code indicators
                        if 'QR Code:' in line or 'Scan the QR code' in line:
                            logger.info("QR code detected in output")

                        # Check for login success
                        if 'Logged in' in line or 'Connected' in line:
                            logger.info("WhatsApp login successful")
                            self.save_session_status('connected')
                            break

                except Exception as e:
                    logger.error(f"Error reading mudslide output: {e}")
                    break

                # Check for QR code files
                qr_files = [
                    os.path.join(self.session_dir, 'qr.png'),
                    os.path.join(self.session_dir, 'qr-code.png'),
                    os.path.join(self.session_dir, f'{self.session_id}_qr.png')
                ]

                for qr_file in qr_files:
                    if os.path.exists(qr_file):
                        try:
                            with open(qr_file, 'rb') as f:
                                qr_data = base64.b64encode(f.read()).decode('utf-8')
                            logger.info(f"QR code image found: {qr_file}")
                            self.save_qr_data(qr_data, 'mudslide')
                            return qr_data
                        except Exception as e:
                            logger.error(f"Error reading QR file {qr_file}: {e}")

                time.sleep(1)

            # Clean up process
            if process.poll() is None:
                process.terminate()

            return qr_data

        except Exception as e:
            logger.error(f"Error with mudslide: {e}")
            return None

    def generate_qr_with_selenium(self):
        """Generate QR code using Selenium (fallback method)"""
        try:
            logger.info("Attempting QR generation with Selenium")

            # Check if selenium is available
            try:
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
            except ImportError:
                logger.error("Selenium not available. Install with: pip3 install selenium")
                return None

            # Setup Chrome options
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")

            # Initialize driver
            try:
                driver = webdriver.Chrome(options=chrome_options)
            except Exception as e:
                logger.error(f"Chrome driver not available: {e}")
                return None

            try:
                # Navigate to WhatsApp Web
                driver.get("https://web.whatsapp.com")

                # Wait for QR code to appear
                wait = WebDriverWait(driver, 30)
                qr_element = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "[data-ref]")))

                # Take screenshot of QR code
                qr_img = qr_element.screenshot_as_base64

                # Save QR code
                self.save_qr_data(qr_img, 'selenium')
                logger.info("QR code generated with Selenium")

                return qr_img

            finally:
                driver.quit()

        except Exception as e:
            logger.error(f"Error with Selenium: {e}")
            return None

    def generate_demo_qr(self):
        """Generate demo QR code with instructions"""
        try:
            import qrcode
            from io import BytesIO

            # Create instructional QR code
            qr_content = f"""WhatsApp Integration Setup Required

Session: {self.session_id}
Phone: {self.phone_number or 'Not specified'}

To get real WhatsApp QR codes:
1. Update Node.js to version 20+
2. Install mudslide: npm install -g mudslide
3. Or use WhatsApp Business API

Current setup generates demo QR codes only.
Contact support for production setup."""

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_content)
            qr.make(fit=True)

            # Create QR code image
            img = qr.make_image(fill_color="black", back_color="white")

            # Convert to base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Save demo QR
            self.save_qr_data(qr_data, 'demo')
            logger.info("Demo QR code generated")

            return qr_data

        except ImportError:
            logger.error("QRCode library not available. Install with: pip3 install qrcode[pil]")
            return None
        except Exception as e:
            logger.error(f"Error generating demo QR: {e}")
            return None

    def save_qr_data(self, qr_data, method):
        """Save QR code data to file"""
        try:
            qr_info = {
                'qr_image': qr_data,
                'timestamp': time.time(),
                'session_id': self.session_id,
                'phone_number': self.phone_number,
                'method': method,
                'status': 'qr_ready'
            }

            qr_file = os.path.join(self.session_dir, 'qr_data.json')
            with open(qr_file, 'w') as f:
                json.dump(qr_info, f, indent=2)

            logger.info(f"QR data saved to {qr_file}")

        except Exception as e:
            logger.error(f"Error saving QR data: {e}")

    def save_session_status(self, status):
        """Save session status"""
        try:
            status_info = {
                'status': status,
                'timestamp': time.time(),
                'session_id': self.session_id
            }

            status_file = os.path.join(self.session_dir, 'status.json')
            with open(status_file, 'w') as f:
                json.dump(status_info, f, indent=2)

            logger.info(f"Session status saved: {status}")

        except Exception as e:
            logger.error(f"Error saving session status: {e}")

    def generate_qr_code(self):
        """Main method to generate QR code using best available method"""
        logger.info(f"Generating QR code for session {self.session_id}")

        # Try mudslide first
        if self.check_mudslide_availability():
            qr_data = self.generate_qr_with_mudslide()
            if qr_data:
                return qr_data

        # Try Selenium as fallback
        qr_data = self.generate_qr_with_selenium()
        if qr_data:
            return qr_data

        # Generate demo QR as last resort
        logger.warning("Using demo QR code - real WhatsApp integration not available")
        return self.generate_demo_qr()

def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='WhatsApp QR Code Generator')
    parser.add_argument('--session', required=True, help='Session ID')
    parser.add_argument('--phone', help='Phone number')
    parser.add_argument('--output', help='Output file for QR data')

    args = parser.parse_args()

    # Create integration instance
    integration = WhatsAppRealIntegration(args.session, args.phone)

    # Generate QR code
    qr_data = integration.generate_qr_code()

    if qr_data:
        print(f"QR code generated successfully for session {args.session}")

        if args.output:
            with open(args.output, 'w') as f:
                json.dump({'qr_data': qr_data, 'session_id': args.session}, f)
            print(f"QR data saved to {args.output}")
    else:
        print("Failed to generate QR code")
        sys.exit(1)

if __name__ == '__main__':
    main()
