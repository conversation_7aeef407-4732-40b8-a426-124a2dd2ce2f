from odoo import http
from odoo.http import request
import base64
import json
import logging

_logger = logging.getLogger(__name__)


class WhatsAppController(http.Controller):

    @http.route('/whatsapp/qr/<int:account_id>', type='http', auth='user', methods=['GET'])
    def get_qr_code(self, account_id, **kwargs):
        """Return QR code image for WhatsApp account"""
        try:
            _logger.info(f"QR code requested for account ID: {account_id}")

            account = request.env['whatsapp.account'].browse(account_id)

            if not account.exists():
                _logger.warning(f"Account {account_id} not found")
                return request.not_found()

            _logger.info(f"Account found: {account.name}, Status: {account.status}")
            _logger.info(f"QR code exists: {bool(account.qr_code)}")

            if not account.qr_code:
                _logger.warning(f"No QR code data for account {account_id}")
                return request.render('arihant_whatsapp.qr_not_available')

            _logger.info(f"QR code length: {len(account.qr_code)} characters")

            # Decode base64 QR code
            qr_data = base64.b64decode(account.qr_code)
            _logger.info(f"Decoded QR data length: {len(qr_data)} bytes")

            return request.make_response(
                qr_data,
                headers=[
                    ('Content-Type', 'image/png'),
                    ('Cache-Control', 'no-cache, no-store, must-revalidate'),
                    ('Pragma', 'no-cache'),
                    ('Expires', '0')
                ]
            )

        except Exception as e:
            _logger.error(f"Error serving QR code for account {account_id}: {str(e)}")
            return request.not_found()

    @http.route('/whatsapp/status/<int:account_id>', type='json', auth='user', methods=['POST'])
    def get_connection_status(self, account_id, **kwargs):
        """Get current connection status of WhatsApp account"""
        try:
            account = request.env['whatsapp.account'].browse(account_id)

            if not account.exists():
                return {'error': 'Account not found'}

            return {
                'status': account.status,
                'qr_code_url': account.qr_code_url,
                'last_update': account.last_qr_update.isoformat() if account.last_qr_update else None,
                'whatsapp_name': account.whatsapp_name,
                'whatsapp_number': account.whatsapp_number
            }

        except Exception as e:
            _logger.error(f"Error getting status for account {account_id}: {str(e)}")
            return {'error': str(e)}

    @http.route('/whatsapp/webhook/<string:session_id>', type='json', auth='none', methods=['POST'], csrf=False)
    def webhook_handler(self, session_id, **kwargs):
        """Handle webhooks from Mudslide"""
        try:
            data = request.jsonrequest

            # Find account by session_id
            account = request.env['whatsapp.account'].sudo().search([
                ('session_id', '=', session_id)
            ], limit=1)

            if not account:
                _logger.warning(f"Webhook received for unknown session: {session_id}")
                return {'status': 'error', 'message': 'Session not found'}

            # Handle different webhook events
            event_type = data.get('type')

            if event_type == 'qr':
                # QR code updated
                qr_code = data.get('qr')
                if qr_code:
                    account.write({
                        'qr_code': qr_code,
                        'last_qr_update': request.env['ir.fields'].Datetime.now(),
                        'status': 'qr_ready'
                    })

            elif event_type == 'ready':
                # WhatsApp connected
                account.write({
                    'status': 'connected',
                    'whatsapp_name': data.get('user', {}).get('name'),
                    'whatsapp_number': data.get('user', {}).get('id'),
                })

            elif event_type == 'disconnected':
                # WhatsApp disconnected
                account.write({
                    'status': 'disconnected',
                    'qr_code': False,
                    'qr_code_url': False
                })

            elif event_type == 'auth_failure':
                # Authentication failed
                account.write({
                    'status': 'error'
                })

            return {'status': 'success'}

        except Exception as e:
            _logger.error(f"Error handling webhook for session {session_id}: {str(e)}")
            return {'status': 'error', 'message': str(e)}
