# 📱 Complete Guide: WhatsApp Integration in Odoo Backend

## 🚀 **Quick Start: Send Your First Message**

### **Step 1: Setup WhatsApp Account**
```python
# In Odoo Python console or custom module
account = self.env['whatsapp.account'].create({
    'name': 'Main WhatsApp Business',
    'phone_number': '**********',  # Your WhatsApp number
    'active': True,
    'is_default': True
})

# Generate QR code and connect
account.action_generate_qr_code()
# Scan QR code with WhatsApp mobile app
```

### **Step 2: Send Your First Message**
```python
# Get default account
account = self.env['whatsapp.account'].get_default_account()

# Send simple text message
message = account.send_text_message(
    recipient='************',  # Customer phone number
    message_text='Hello! Your order has been confirmed.'
)
```

---

## 📤 **SENDING DIFFERENT MESSAGE TYPES**

### **1. Text Messages**
```python
# Basic text message
account.send_text_message(
    recipient='************',
    message_text='Your order #SO001 is ready for delivery!'
)

# Text message linked to a record
account.send_text_message(
    recipient='************',
    message_text='Order confirmation sent',
    res_model='sale.order',
    res_id=order.id
)

# Bulk text messages
recipients = ['************', '************', '************']
for phone in recipients:
    account.send_text_message(
        recipient=phone,
        message_text=f'Special offer for {phone}!'
    )
```

### **2. Image Messages**
```python
# Send image with caption
import base64

# Read image file
with open('/path/to/image.jpg', 'rb') as f:
    image_data = base64.b64encode(f.read()).decode('utf-8')

account.send_image_message(
    recipient='************',
    image_data=image_data,
    image_name='product_catalog.jpg',
    caption='Check out our latest products!'
)

# Send image from Odoo attachment
attachment = self.env['ir.attachment'].browse(attachment_id)
account.send_image_message(
    recipient='************',
    image_data=attachment.datas,
    image_name=attachment.name,
    caption='Product image attached'
)
```

### **3. Document/PDF Messages**
```python
# Send PDF document
pdf_data = base64.b64encode(pdf_content).decode('utf-8')

account.send_file_message(
    recipient='************',
    file_data=pdf_data,
    file_name='invoice_INV001.pdf',
    file_type='document'
)

# Send invoice PDF
invoice = self.env['account.move'].browse(invoice_id)
pdf_content, _ = self.env['ir.actions.report']._render_qweb_pdf(
    'account.account_invoices', invoice.ids
)
pdf_data = base64.b64encode(pdf_content).decode('utf-8')

account.send_file_message(
    recipient=invoice.partner_id.mobile,
    file_data=pdf_data,
    file_name=f'Invoice_{invoice.name}.pdf',
    res_model='account.move',
    res_id=invoice.id
)
```

### **4. Template Messages**
```python
# Create template
template = self.env['whatsapp.template'].create({
    'name': 'Order Confirmation',
    'template_type': 'text',
    'message_text': '''Hello {{customer_name}},

Your order {{order_number}} for {{total_amount}} has been confirmed!

Expected delivery: {{delivery_date}}

Thank you!'''
})

# Send template message
variables = {
    'customer_name': 'John Doe',
    'order_number': 'SO001',
    'total_amount': '₹5,000',
    'delivery_date': '2025-01-15'
}

account.send_template_message(
    recipient='************',
    template_id=template.id,
    variables=variables
)
```

---

## 🔗 **INTEGRATION WITH BUSINESS MODELS**

### **Sale Order Integration**
```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def action_confirm(self):
        # Call original confirm method
        result = super().action_confirm()
        
        # Send WhatsApp confirmation
        if self.partner_id.mobile:
            account = self.env['whatsapp.account'].get_default_account()
            if account:
                message_text = f"""
Dear {self.partner_id.name},

Your order {self.name} has been confirmed!

Order Details:
- Total: {self.currency_id.symbol}{self.amount_total}
- Date: {self.date_order.strftime('%Y-%m-%d')}

Thank you for your business!
                """.strip()
                
                account.send_text_message(
                    recipient=self.partner_id.mobile,
                    message_text=message_text,
                    res_model=self._name,
                    res_id=self.id
                )
        
        return result

    def action_send_whatsapp_delivery_notice(self):
        """Send delivery notification"""
        self.ensure_one()
        
        if not self.partner_id.mobile:
            raise UserError(_("Customer has no mobile number"))
        
        account = self.env['whatsapp.account'].get_default_account()
        message = account.send_text_message(
            recipient=self.partner_id.mobile,
            message_text=f"Hi {self.partner_id.name}! Your order {self.name} is out for delivery today.",
            res_model=self._name,
            res_id=self.id
        )
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Delivery Notice Sent'),
                'message': _('WhatsApp delivery notification sent successfully'),
                'type': 'success',
            }
        }
```

### **Invoice Integration**
```python
class AccountMove(models.Model):
    _inherit = 'account.move'

    def action_post(self):
        # Call original post method
        result = super().action_post()
        
        # Send invoice via WhatsApp for customer invoices
        if self.move_type == 'out_invoice' and self.partner_id.mobile:
            self._send_whatsapp_invoice()
        
        return result

    def _send_whatsapp_invoice(self):
        """Send invoice via WhatsApp"""
        account = self.env['whatsapp.account'].get_default_account()
        if not account:
            return
        
        # Generate PDF
        pdf_content, _ = self.env['ir.actions.report']._render_qweb_pdf(
            'account.account_invoices', self.ids
        )
        pdf_data = base64.b64encode(pdf_content).decode('utf-8')
        
        # Send text message
        account.send_text_message(
            recipient=self.partner_id.mobile,
            message_text=f"Dear {self.partner_id.name}, your invoice {self.name} is ready. Please find it attached.",
            res_model=self._name,
            res_id=self.id
        )
        
        # Send PDF
        account.send_file_message(
            recipient=self.partner_id.mobile,
            file_data=pdf_data,
            file_name=f'Invoice_{self.name}.pdf',
            res_model=self._name,
            res_id=self.id
        )
```

### **Customer Support Integration**
```python
class HelpdeskTicket(models.Model):
    _inherit = 'helpdesk.ticket'

    def action_send_whatsapp_update(self):
        """Send ticket update via WhatsApp"""
        self.ensure_one()
        
        if not self.partner_id.mobile:
            raise UserError(_("Customer has no mobile number"))
        
        account = self.env['whatsapp.account'].get_default_account()
        
        status_messages = {
            'new': 'Your support ticket has been created and assigned to our team.',
            'in_progress': 'Our team is working on your support request.',
            'solved': 'Your support ticket has been resolved. Please let us know if you need further assistance.',
            'closed': 'Your support ticket has been closed. Thank you for contacting us.'
        }
        
        message_text = f"""
Hello {self.partner_id.name},

Ticket Update: {self.name}
Status: {self.stage_id.name}

{status_messages.get(self.stage_id.name.lower(), 'Your ticket status has been updated.')}

Best regards,
Support Team
        """.strip()
        
        account.send_text_message(
            recipient=self.partner_id.mobile,
            message_text=message_text,
            res_model=self._name,
            res_id=self.id
        )
```

---

## 📥 **RECEIVING MESSAGES**

### **Webhook Processing**
```python
# In your webhook controller
@http.route('/whatsapp/webhook/<string:session_id>', type='json', auth='none', methods=['POST'], csrf=False)
def webhook_handler(self, session_id, **kwargs):
    """Handle incoming WhatsApp messages"""
    try:
        data = request.jsonrequest
        
        # Process incoming message
        self.env['whatsapp.backend.guide'].sudo().process_incoming_message(data)
        
        return {'status': 'success'}
    except Exception as e:
        _logger.error(f"Webhook error: {str(e)}")
        return {'status': 'error', 'message': str(e)}
```

### **Auto-Response System**
```python
def process_incoming_message(self, webhook_data):
    """Process incoming messages and send auto-responses"""
    
    from_number = webhook_data.get('from')
    message_text = webhook_data.get('message', {}).get('body', '').lower()
    
    # Find customer by phone number
    partner = self.env['res.partner'].search([
        '|', ('mobile', 'ilike', from_number.replace('@c.us', '')),
        ('phone', 'ilike', from_number.replace('@c.us', ''))
    ], limit=1)
    
    account = self.env['whatsapp.account'].get_default_account()
    
    # Auto-responses based on keywords
    if 'order status' in message_text:
        if partner:
            orders = self.env['sale.order'].search([
                ('partner_id', '=', partner.id),
                ('state', 'in', ['sale', 'done'])
            ], limit=5)
            
            if orders:
                order_list = '\n'.join([f"- {order.name}: {order.state}" for order in orders])
                response = f"Your recent orders:\n{order_list}"
            else:
                response = "No recent orders found."
        else:
            response = "Please provide your order number to check status."
    
    elif 'support' in message_text or 'help' in message_text:
        response = "Our support team will assist you. Please describe your issue in detail."
    
    elif 'hello' in message_text or 'hi' in message_text:
        customer_name = partner.name if partner else "there"
        response = f"Hello {customer_name}! How can we help you today?"
    
    else:
        response = "Thank you for your message. Our team will get back to you soon."
    
    # Send auto-response
    account.send_text_message(
        recipient=from_number,
        message_text=response
    )
```

---

## 🔧 **ADVANCED FEATURES**

### **Bulk Messaging with Queue**
```python
def send_bulk_promotional_messages(self):
    """Send promotional messages to all customers"""
    
    # Get customers with mobile numbers
    customers = self.env['res.partner'].search([
        ('mobile', '!=', False),
        ('is_company', '=', False)
    ])
    
    account = self.env['whatsapp.account'].get_default_account()
    template = self.env['whatsapp.template'].search([
        ('name', '=', 'Promotional Offer')
    ], limit=1)
    
    for customer in customers:
        variables = {
            'customer_name': customer.name,
            'offer_details': '20% off on all products',
            'valid_until': '2025-01-31'
        }
        
        account.send_template_message(
            recipient=customer.mobile,
            template_id=template.id,
            variables=variables,
            res_model='res.partner',
            res_id=customer.id
        )
        
        # Add delay to avoid rate limiting
        time.sleep(1)
```

### **Scheduled Messages**
```python
def schedule_payment_reminders(self):
    """Schedule payment reminder messages"""
    
    # Get overdue invoices
    overdue_invoices = self.env['account.move'].search([
        ('move_type', '=', 'out_invoice'),
        ('state', '=', 'posted'),
        ('invoice_date_due', '<', fields.Date.today()),
        ('amount_residual', '>', 0)
    ])
    
    account = self.env['whatsapp.account'].get_default_account()
    
    for invoice in overdue_invoices:
        if invoice.partner_id.mobile:
            days_overdue = (fields.Date.today() - invoice.invoice_date_due).days
            
            message_text = f"""
Dear {invoice.partner_id.name},

This is a friendly reminder that your invoice {invoice.name} 
for {invoice.currency_id.symbol}{invoice.amount_total} 
is {days_overdue} days overdue.

Please arrange payment at your earliest convenience.

Thank you!
            """.strip()
            
            account.send_text_message(
                recipient=invoice.partner_id.mobile,
                message_text=message_text,
                res_model='account.move',
                res_id=invoice.id
            )
```

---

## 🎯 **BEST PRACTICES**

### **1. Error Handling**
```python
def safe_send_whatsapp(self, recipient, message):
    """Send WhatsApp message with proper error handling"""
    try:
        account = self.env['whatsapp.account'].get_default_account()
        if not account:
            _logger.warning("No WhatsApp account configured")
            return False
        
        if not recipient:
            _logger.warning("No recipient provided")
            return False
        
        message = account.send_text_message(recipient, message)
        return message
        
    except Exception as e:
        _logger.error(f"Failed to send WhatsApp message: {str(e)}")
        return False
```

### **2. Phone Number Validation**
```python
def validate_phone_number(self, phone):
    """Validate and format phone number"""
    if not phone:
        return None
    
    # Remove all non-digit characters
    clean_phone = ''.join(filter(str.isdigit, phone))
    
    # Add country code if missing (assuming India)
    if len(clean_phone) == 10:
        clean_phone = '91' + clean_phone
    
    # Validate length
    if len(clean_phone) < 10:
        return None
    
    return clean_phone
```

### **3. Rate Limiting**
```python
def send_with_rate_limit(self, messages, delay=2):
    """Send messages with rate limiting"""
    account = self.env['whatsapp.account'].get_default_account()
    
    for i, (recipient, message_text) in enumerate(messages):
        try:
            account.send_text_message(recipient, message_text)
            
            # Add delay between messages
            if i < len(messages) - 1:
                time.sleep(delay)
                
        except Exception as e:
            _logger.error(f"Failed to send message to {recipient}: {str(e)}")
            continue
```

---

## 📊 **MONITORING & ANALYTICS**

### **Message Statistics**
```python
def get_whatsapp_statistics(self):
    """Get WhatsApp messaging statistics"""
    
    today = fields.Date.today()
    week_ago = today - timedelta(days=7)
    
    stats = {
        'total_messages': self.env['whatsapp.message'].search_count([]),
        'messages_today': self.env['whatsapp.message'].search_count([
            ('create_date', '>=', today)
        ]),
        'messages_this_week': self.env['whatsapp.message'].search_count([
            ('create_date', '>=', week_ago)
        ]),
        'success_rate': self._calculate_success_rate(),
        'top_recipients': self._get_top_recipients()
    }
    
    return stats

def _calculate_success_rate(self):
    """Calculate message success rate"""
    total = self.env['whatsapp.message'].search_count([])
    if total == 0:
        return 0
    
    successful = self.env['whatsapp.message'].search_count([
        ('state', '=', 'sent')
    ])
    
    return (successful / total) * 100
```

---

## 🚀 **Ready to Use!**

Your Odoo WhatsApp integration is now complete with:
- ✅ Text, image, and document messaging
- ✅ Template-based messaging
- ✅ Business model integration
- ✅ Auto-response system
- ✅ Bulk messaging capabilities
- ✅ Error handling and monitoring

Start sending WhatsApp messages from your Odoo backend today! 📱✨
