#!/usr/bin/env python3
"""
Test QR code generation and verify the output
"""

import base64
import qrcode
from io import BytesIO
import os

def test_qr_generation():
    """Test QR code generation and base64 encoding"""
    
    print("Testing QR code generation...")
    
    try:
        # Generate a test QR code
        qr_content = """WhatsApp Integration Test
        
This is a test QR code to verify the generation process.
If you can see this QR code properly, the system is working.

Test URL: https://arihantai.com/whatsapp/qr/1
Timestamp: 2025-06-02 11:00:00"""

        qr = qrcode.QRCode(
            version=2,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(qr_content)
        qr.make(fit=True)

        # Create QR code image
        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

        print(f"✅ QR code generated successfully")
        print(f"✅ Base64 length: {len(qr_data)} characters")
        print(f"✅ First 100 chars: {qr_data[:100]}...")

        # Save as file for testing
        test_file = "/tmp/test_qr.png"
        with open(test_file, 'wb') as f:
            f.write(base64.b64decode(qr_data))
        
        print(f"✅ Test QR saved to: {test_file}")
        
        # Test base64 decoding
        try:
            decoded_data = base64.b64decode(qr_data)
            print(f"✅ Base64 decoding successful: {len(decoded_data)} bytes")
        except Exception as e:
            print(f"❌ Base64 decoding failed: {e}")
            return False
        
        return qr_data
        
    except Exception as e:
        print(f"❌ QR generation failed: {e}")
        return None

def test_controller_simulation():
    """Simulate what the controller does"""
    
    print("\nTesting controller simulation...")
    
    # Generate test QR
    qr_data = test_qr_generation()
    if not qr_data:
        return False
    
    try:
        # Simulate controller logic
        decoded_qr = base64.b64decode(qr_data)
        
        # Save as what controller would return
        controller_test_file = "/tmp/controller_test_qr.png"
        with open(controller_test_file, 'wb') as f:
            f.write(decoded_qr)
        
        print(f"✅ Controller simulation successful")
        print(f"✅ Output file: {controller_test_file}")
        print(f"✅ File size: {len(decoded_qr)} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Controller simulation failed: {e}")
        return False

if __name__ == '__main__':
    print("=== QR Code Generation Test ===")
    
    success = test_qr_generation()
    if success:
        print("\n=== Controller Simulation Test ===")
        controller_success = test_controller_simulation()
        
        if controller_success:
            print("\n✅ ALL TESTS PASSED")
            print("QR code generation and controller logic are working properly.")
            print("\nTo test the actual URL:")
            print("1. Check if account ID 1 exists in database")
            print("2. Check if account has qr_code data")
            print("3. Check Odoo logs for any errors")
        else:
            print("\n❌ CONTROLLER TEST FAILED")
    else:
        print("\n❌ QR GENERATION TEST FAILED")
