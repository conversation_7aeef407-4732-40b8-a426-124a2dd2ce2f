#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WhatsApp Backend Integration Test Script

This script tests the WhatsApp integration from Odoo backend.
Run this script to verify that your WhatsApp integration is working correctly.

Usage:
1. Make sure your Odoo server is running
2. Make sure WhatsApp service is running on port 3001
3. Run: python3 test_whatsapp_backend.py
"""

import xmlrpc.client
import base64
import json
import time
import sys
import os

# Odoo connection settings
ODOO_URL = 'http://localhost:8069'
ODOO_DB = 'your_database_name'  # Change this to your database name
ODOO_USERNAME = 'admin'
ODOO_PASSWORD = 'admin'

# Test settings
TEST_PHONE_NUMBER = '************'  # Change this to your test phone number
WHATSAPP_ACCOUNT_PHONE = '**********'  # Your WhatsApp business number

class WhatsAppBackendTester:
    """Test WhatsApp integration from Odoo backend"""
    
    def __init__(self):
        self.url = ODOO_URL
        self.db = ODOO_DB
        self.username = ODOO_USERNAME
        self.password = ODOO_PASSWORD
        
        # Connect to Odoo
        self.common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
        self.models = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/object')
        
        # Authenticate
        try:
            self.uid = self.common.authenticate(self.db, self.username, self.password, {})
            if not self.uid:
                raise Exception("Authentication failed")
            print(f"✅ Connected to Odoo as user ID: {self.uid}")
        except Exception as e:
            print(f"❌ Failed to connect to Odoo: {str(e)}")
            sys.exit(1)
    
    def test_whatsapp_account_setup(self):
        """Test WhatsApp account setup"""
        print("\n🔍 Testing WhatsApp Account Setup...")
        
        try:
            # Check if WhatsApp account exists
            account_ids = self.models.execute_kw(
                self.db, self.uid, self.password,
                'whatsapp.account', 'search',
                [[('phone_number', '=', WHATSAPP_ACCOUNT_PHONE)]]
            )
            
            if account_ids:
                account = self.models.execute_kw(
                    self.db, self.uid, self.password,
                    'whatsapp.account', 'read',
                    [account_ids[0]], {'fields': ['name', 'phone_number', 'status']}
                )[0]
                
                print(f"✅ WhatsApp Account Found:")
                print(f"   Name: {account['name']}")
                print(f"   Phone: {account['phone_number']}")
                print(f"   Status: {account['status']}")
                
                return account_ids[0]
            else:
                print("❌ No WhatsApp account found. Creating one...")
                
                # Create WhatsApp account
                account_id = self.models.execute_kw(
                    self.db, self.uid, self.password,
                    'whatsapp.account', 'create',
                    [{
                        'name': 'Test WhatsApp Account',
                        'phone_number': WHATSAPP_ACCOUNT_PHONE,
                        'active': True,
                        'is_default': True
                    }]
                )
                
                print(f"✅ WhatsApp Account Created with ID: {account_id}")
                return account_id
                
        except Exception as e:
            print(f"❌ Error testing WhatsApp account: {str(e)}")
            return None
    
    def test_send_text_message(self, account_id):
        """Test sending text message"""
        print("\n📱 Testing Text Message Sending...")
        
        try:
            # Call the send_text_message_api method
            result = self.models.execute_kw(
                self.db, self.uid, self.password,
                'whatsapp.account', 'send_text_message_api',
                [account_id, TEST_PHONE_NUMBER, 'Hello from Odoo backend! This is a test message.']
            )
            
            if result:
                print(f"✅ Text message sent successfully!")
                print(f"   Message ID: {result}")
                return True
            else:
                print("❌ Failed to send text message")
                return False
                
        except Exception as e:
            print(f"❌ Error sending text message: {str(e)}")
            return False
    
    def test_send_template_message(self, account_id):
        """Test sending template message"""
        print("\n📋 Testing Template Message...")
        
        try:
            # First, create a test template
            template_id = self.models.execute_kw(
                self.db, self.uid, self.password,
                'whatsapp.template', 'create',
                [{
                    'name': 'Test Template',
                    'template_type': 'text',
                    'category': 'test',
                    'message_text': 'Hello {{customer_name}}! Your order {{order_number}} is ready. Total: {{total_amount}}',
                    'active': True
                }]
            )
            
            print(f"✅ Template created with ID: {template_id}")
            
            # Send template message
            variables = {
                'customer_name': 'John Doe',
                'order_number': 'SO001',
                'total_amount': '₹5,000'
            }
            
            result = self.models.execute_kw(
                self.db, self.uid, self.password,
                'whatsapp.account', 'send_template_message',
                [account_id, TEST_PHONE_NUMBER, template_id, variables]
            )
            
            if result:
                print(f"✅ Template message sent successfully!")
                return True
            else:
                print("❌ Failed to send template message")
                return False
                
        except Exception as e:
            print(f"❌ Error sending template message: {str(e)}")
            return False
    
    def test_message_history(self):
        """Test message history retrieval"""
        print("\n📊 Testing Message History...")
        
        try:
            # Get recent messages
            message_ids = self.models.execute_kw(
                self.db, self.uid, self.password,
                'whatsapp.message', 'search',
                [[]], {'limit': 5, 'order': 'create_date desc'}
            )
            
            if message_ids:
                messages = self.models.execute_kw(
                    self.db, self.uid, self.password,
                    'whatsapp.message', 'read',
                    [message_ids], {'fields': ['message_type', 'recipient_phone', 'state', 'create_date']}
                )
                
                print(f"✅ Found {len(messages)} recent messages:")
                for msg in messages:
                    print(f"   - {msg['message_type']} to {msg['recipient_phone']} ({msg['state']})")
                
                return True
            else:
                print("ℹ️  No messages found in history")
                return True
                
        except Exception as e:
            print(f"❌ Error retrieving message history: {str(e)}")
            return False
    
    def test_whatsapp_service_connection(self):
        """Test connection to WhatsApp service"""
        print("\n🔗 Testing WhatsApp Service Connection...")
        
        try:
            import requests
            
            # Test health endpoint
            response = requests.get('http://**************:3001/health', timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ WhatsApp Service is running:")
                print(f"   Status: {data.get('status', 'Unknown')}")
                print(f"   Uptime: {data.get('uptime', 'Unknown')}")
                return True
            else:
                print(f"❌ WhatsApp Service returned status: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Cannot connect to WhatsApp Service: {str(e)}")
            print("   Make sure the service is running on port 3001")
            return False
    
    def test_api_integration(self):
        """Test direct API integration"""
        print("\n🔌 Testing Direct API Integration...")
        
        try:
            import requests
            
            # Test sending message via API
            api_url = f"http://**************:3001/api/instances/{WHATSAPP_ACCOUNT_PHONE}/send"
            
            payload = {
                'to': f'91{TEST_PHONE_NUMBER.replace("91", "")}@c.us',
                'message': 'Direct API test message from Odoo backend test script',
                'type': 'text'
            }
            
            response = requests.post(api_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ Direct API message sent successfully!")
                    return True
                else:
                    print(f"❌ API returned error: {result.get('error')}")
                    return False
            else:
                print(f"❌ API request failed with status: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error testing direct API: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting WhatsApp Backend Integration Tests...")
        print(f"   Odoo URL: {self.url}")
        print(f"   Database: {self.db}")
        print(f"   Test Phone: {TEST_PHONE_NUMBER}")
        print(f"   WhatsApp Account: {WHATSAPP_ACCOUNT_PHONE}")
        
        results = []
        
        # Test 1: WhatsApp Service Connection
        results.append(self.test_whatsapp_service_connection())
        
        # Test 2: WhatsApp Account Setup
        account_id = self.test_whatsapp_account_setup()
        results.append(account_id is not None)
        
        if account_id:
            # Test 3: Send Text Message
            results.append(self.test_send_text_message(account_id))
            
            # Test 4: Send Template Message
            results.append(self.test_send_template_message(account_id))
        
        # Test 5: Message History
        results.append(self.test_message_history())
        
        # Test 6: Direct API Integration
        results.append(self.test_api_integration())
        
        # Summary
        passed = sum(results)
        total = len(results)
        
        print(f"\n📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Your WhatsApp integration is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the errors above.")
        
        return passed == total


def main():
    """Main function"""
    print("WhatsApp Backend Integration Test Script")
    print("=" * 50)
    
    # Check if required settings are configured
    if ODOO_DB == 'your_database_name':
        print("❌ Please configure ODOO_DB in the script")
        sys.exit(1)
    
    if TEST_PHONE_NUMBER == '************':
        print("⚠️  Using default test phone number. Please update TEST_PHONE_NUMBER for your testing.")
    
    # Run tests
    tester = WhatsAppBackendTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Integration test completed successfully!")
        print("\nNext steps:")
        print("1. Check your WhatsApp mobile app for test messages")
        print("2. Try sending messages from Odoo interface")
        print("3. Test with your business workflows")
    else:
        print("\n❌ Integration test failed!")
        print("\nTroubleshooting:")
        print("1. Make sure Odoo server is running")
        print("2. Make sure WhatsApp service is running on port 3001")
        print("3. Check your WhatsApp account is connected")
        print("4. Verify phone numbers are correct")
    
    return 0 if success else 1


if __name__ == '__main__':
    sys.exit(main())
