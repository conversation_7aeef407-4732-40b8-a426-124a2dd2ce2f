/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onMounted, useRef } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * WhatsApp Chat Widget for real-time messaging
 */
class WhatsAppChatWidget extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.chatContainer = useRef("chatContainer");
        
        this.state = useState({
            messages: [],
            newMessage: '',
            loading: false,
            connected: false,
            typing: false
        });
        
        onMounted(() => {
            this.loadMessages();
            this.scrollToBottom();
        });
    }
    
    async loadMessages() {
        if (!this.props.conversationId) return;
        
        this.state.loading = true;
        try {
            const messages = await this.rpc({
                model: 'whatsapp.message',
                method: 'search_read',
                args: [
                    [['conversation_id', '=', this.props.conversationId]],
                    ['content', 'direction', 'date_sent', 'message_type', 'status', 'is_read']
                ],
                kwargs: {
                    order: 'date_sent asc',
                    limit: 100
                }
            });
            
            this.state.messages = messages;
            this.scrollToBottom();
        } catch (error) {
            this.notification.add("Failed to load messages", { type: "danger" });
        } finally {
            this.state.loading = false;
        }
    }
    
    async sendMessage() {
        if (!this.state.newMessage.trim() || !this.props.conversationId) return;
        
        const messageContent = this.state.newMessage.trim();
        this.state.newMessage = '';
        
        try {
            // Add message to UI immediately for better UX
            const tempMessage = {
                id: 'temp_' + Date.now(),
                content: messageContent,
                direction: 'outgoing',
                date_sent: new Date().toISOString(),
                message_type: 'text',
                status: 'pending'
            };
            this.state.messages.push(tempMessage);
            this.scrollToBottom();
            
            // Send message via API
            const result = await this.rpc({
                model: 'whatsapp.send.message',
                method: 'send_message',
                args: [],
                kwargs: {
                    conversation_id: this.props.conversationId,
                    message_content: messageContent,
                    message_type: 'text'
                }
            });
            
            // Remove temp message and reload actual messages
            this.state.messages = this.state.messages.filter(m => m.id !== tempMessage.id);
            await this.loadMessages();
            
        } catch (error) {
            // Remove temp message on error
            this.state.messages = this.state.messages.filter(m => m.id !== tempMessage.id);
            this.notification.add("Failed to send message", { type: "danger" });
        }
    }
    
    onKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.sendMessage();
        }
    }
    
    scrollToBottom() {
        setTimeout(() => {
            if (this.chatContainer.el) {
                this.chatContainer.el.scrollTop = this.chatContainer.el.scrollHeight;
            }
        }, 100);
    }
    
    formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
    
    getMessageClass(message) {
        const baseClass = message.direction === 'incoming' ? 
            'whatsapp_message_incoming' : 'whatsapp_message_outgoing';
        
        if (message.status === 'pending') {
            return baseClass + ' opacity-50';
        }
        
        return baseClass;
    }
    
    getStatusIcon(status) {
        switch (status) {
            case 'sent':
                return 'fa-check';
            case 'delivered':
                return 'fa-check-double';
            case 'read':
                return 'fa-check-double text-primary';
            case 'failed':
                return 'fa-exclamation-triangle text-danger';
            default:
                return 'fa-clock-o';
        }
    }
}

WhatsAppChatWidget.template = 'whatsapp_conversations.ChatWidget';
WhatsAppChatWidget.props = {
    conversationId: { type: Number, optional: true },
    readonly: { type: Boolean, optional: true }
};

registry.category("fields").add("whatsapp_chat_widget", WhatsAppChatWidget);

/**
 * WhatsApp Template Selector Widget
 */
class WhatsAppTemplateSelector extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.state = useState({
            templates: [],
            selectedTemplate: null,
            variables: {},
            previewContent: ''
        });
        
        onMounted(() => {
            this.loadTemplates();
        });
    }
    
    async loadTemplates() {
        try {
            const templates = await this.rpc({
                model: 'whatsapp.template',
                method: 'search_read',
                args: [
                    [['is_active', '=', true]],
                    ['id', 'name', 'content', 'template_type']
                ],
                kwargs: {
                    order: 'name asc'
                }
            });
            
            this.state.templates = templates;
        } catch (error) {
            console.error('Failed to load templates:', error);
        }
    }
    
    onTemplateSelect(templateId) {
        const template = this.state.templates.find(t => t.id === templateId);
        if (template) {
            this.state.selectedTemplate = template;
            this.extractVariables(template.content);
            this.updatePreview();
            
            // Trigger change event for parent component
            if (this.props.onTemplateSelect) {
                this.props.onTemplateSelect(template);
            }
        }
    }
    
    extractVariables(content) {
        const regex = /\{\{([^}]+)\}\}/g;
        const variables = {};
        let match;
        
        while ((match = regex.exec(content)) !== null) {
            const varName = match[1].trim();
            if (!variables[varName]) {
                variables[varName] = '';
            }
        }
        
        this.state.variables = variables;
    }
    
    onVariableChange(varName, value) {
        this.state.variables[varName] = value;
        this.updatePreview();
    }
    
    updatePreview() {
        if (!this.state.selectedTemplate) {
            this.state.previewContent = '';
            return;
        }
        
        let content = this.state.selectedTemplate.content;
        
        // Replace variables with values
        Object.entries(this.state.variables).forEach(([key, value]) => {
            const placeholder = `{{${key}}}`;
            content = content.replace(new RegExp(placeholder, 'g'), value || `[${key}]`);
        });
        
        this.state.previewContent = content;
        
        // Trigger preview update for parent component
        if (this.props.onPreviewUpdate) {
            this.props.onPreviewUpdate(content);
        }
    }
    
    getRenderedContent() {
        return this.state.previewContent;
    }
}

WhatsAppTemplateSelector.template = 'whatsapp_conversations.TemplateSelector';
WhatsAppTemplateSelector.props = {
    onTemplateSelect: { type: Function, optional: true },
    onPreviewUpdate: { type: Function, optional: true }
};

registry.category("fields").add("whatsapp_template_selector", WhatsAppTemplateSelector);

/**
 * WhatsApp Phone Number Formatter Widget
 */
class WhatsAppPhoneFormatter extends Component {
    setup() {
        this.state = useState({
            formattedPhone: this.formatPhone(this.props.value)
        });
    }
    
    formatPhone(phone) {
        if (!phone) return '';
        
        // Remove all non-digit characters except +
        let cleaned = phone.replace(/[^\d+]/g, '');
        
        // Ensure it starts with +
        if (!cleaned.startsWith('+')) {
            cleaned = '+' + cleaned;
        }
        
        // Format based on length (basic formatting)
        if (cleaned.length >= 12) {
            // International format: +XX XXX XXX XXXX
            return cleaned.replace(/(\+\d{2})(\d{3})(\d{3})(\d{4})/, '$1 $2 $3 $4');
        } else if (cleaned.length >= 10) {
            // Shorter format: +XX XXX XXXXXX
            return cleaned.replace(/(\+\d{2})(\d{3})(\d+)/, '$1 $2 $3');
        }
        
        return cleaned;
    }
    
    onPhoneChange(event) {
        const value = event.target.value;
        this.state.formattedPhone = this.formatPhone(value);
        
        if (this.props.onChange) {
            this.props.onChange(this.state.formattedPhone);
        }
    }
    
    validatePhone() {
        const phone = this.state.formattedPhone;
        if (!phone) return false;
        
        // Basic validation: should start with + and have at least 10 digits
        const digitsOnly = phone.replace(/[^\d]/g, '');
        return phone.startsWith('+') && digitsOnly.length >= 10;
    }
}

WhatsAppPhoneFormatter.template = 'whatsapp_conversations.PhoneFormatter';
WhatsAppPhoneFormatter.props = {
    value: { type: String, optional: true },
    onChange: { type: Function, optional: true },
    readonly: { type: Boolean, optional: true }
};

registry.category("fields").add("whatsapp_phone_formatter", WhatsAppPhoneFormatter);
