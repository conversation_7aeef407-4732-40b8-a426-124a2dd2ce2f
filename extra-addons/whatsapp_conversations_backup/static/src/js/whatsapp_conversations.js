/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onMounted } from "@odoo/owl";

/**
 * WhatsApp Conversations JavaScript
 */

class WhatsAppConversationWidget extends Component {
    setup() {
        this.state = useState({
            conversations: [],
            loading: false,
            error: null
        });
        
        onMounted(() => {
            this.loadConversations();
        });
    }
    
    async loadConversations() {
        this.state.loading = true;
        try {
            const result = await this.env.services.rpc({
                model: 'whatsapp.conversation',
                method: 'search_read',
                args: [[], ['id', 'display_name', 'unread_count', 'last_message_date']],
                kwargs: {
                    limit: 10,
                    order: 'last_message_date desc'
                }
            });
            this.state.conversations = result;
        } catch (error) {
            this.state.error = error.message;
        } finally {
            this.state.loading = false;
        }
    }
    
    async markAsRead(conversationId) {
        try {
            await this.env.services.rpc({
                model: 'whatsapp.conversation',
                method: 'action_mark_as_read',
                args: [conversationId]
            });
            await this.loadConversations();
        } catch (error) {
            console.error('Error marking conversation as read:', error);
        }
    }
    
    openConversation(conversationId) {
        this.env.services.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'whatsapp.conversation',
            res_id: conversationId,
            view_mode: 'form',
            target: 'current'
        });
    }
}

WhatsAppConversationWidget.template = 'whatsapp_conversations.ConversationWidget';

registry.category("fields").add("whatsapp_conversation_widget", WhatsAppConversationWidget);

/**
 * WhatsApp Message Widget
 */
class WhatsAppMessageWidget extends Component {
    setup() {
        this.state = useState({
            messages: [],
            loading: false,
            newMessage: ''
        });
    }
    
    async sendMessage() {
        if (!this.state.newMessage.trim()) return;
        
        try {
            await this.env.services.rpc({
                model: 'whatsapp.send.message',
                method: 'create',
                args: [{
                    conversation_id: this.props.conversationId,
                    message_content: this.state.newMessage,
                    message_type: 'text'
                }]
            });
            
            this.state.newMessage = '';
            await this.loadMessages();
        } catch (error) {
            console.error('Error sending message:', error);
        }
    }
    
    async loadMessages() {
        this.state.loading = true;
        try {
            const result = await this.env.services.rpc({
                model: 'whatsapp.message',
                method: 'search_read',
                args: [
                    [['conversation_id', '=', this.props.conversationId]],
                    ['content', 'direction', 'date_sent', 'message_type', 'status']
                ],
                kwargs: {
                    order: 'date_sent asc'
                }
            });
            this.state.messages = result;
        } catch (error) {
            console.error('Error loading messages:', error);
        } finally {
            this.state.loading = false;
        }
    }
    
    formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
    
    getMessageClass(direction) {
        return direction === 'incoming' ? 'whatsapp_message_incoming' : 'whatsapp_message_outgoing';
    }
}

WhatsAppMessageWidget.template = 'whatsapp_conversations.MessageWidget';

registry.category("fields").add("whatsapp_message_widget", WhatsAppMessageWidget);

/**
 * WhatsApp Instance Status Widget
 */
class WhatsAppInstanceStatus extends Component {
    setup() {
        this.state = useState({
            status: this.props.record.data.status,
            loading: false
        });
    }
    
    async testConnection() {
        this.state.loading = true;
        try {
            await this.env.services.rpc({
                model: 'whatsapp.instance',
                method: 'action_test_connection',
                args: [this.props.record.resId]
            });
            // Reload the record to get updated status
            await this.props.record.load();
            this.state.status = this.props.record.data.status;
        } catch (error) {
            console.error('Error testing connection:', error);
        } finally {
            this.state.loading = false;
        }
    }
    
    getStatusClass() {
        switch (this.state.status) {
            case 'connected':
                return 'whatsapp_status_connected';
            case 'connecting':
                return 'whatsapp_status_connecting';
            case 'disconnected':
            case 'error':
                return 'whatsapp_status_disconnected';
            default:
                return '';
        }
    }
    
    getStatusIcon() {
        switch (this.state.status) {
            case 'connected':
                return 'fa-check-circle';
            case 'connecting':
                return 'fa-spinner fa-spin';
            case 'disconnected':
                return 'fa-times-circle';
            case 'error':
                return 'fa-exclamation-triangle';
            default:
                return 'fa-question-circle';
        }
    }
}

WhatsAppInstanceStatus.template = 'whatsapp_conversations.InstanceStatus';

registry.category("fields").add("whatsapp_instance_status", WhatsAppInstanceStatus);

/**
 * Utility functions
 */
export const WhatsAppUtils = {
    formatPhoneNumber(phone) {
        if (!phone) return '';
        // Basic phone number formatting
        return phone.replace(/[^\d+]/g, '');
    },
    
    validatePhoneNumber(phone) {
        if (!phone) return false;
        // Basic validation - should start with + and have at least 10 digits
        const cleaned = phone.replace(/[^\d+]/g, '');
        return cleaned.startsWith('+') && cleaned.length >= 11;
    },
    
    formatMessageTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        
        return date.toLocaleDateString();
    },
    
    getMessageTypeIcon(messageType) {
        const icons = {
            'text': 'fa-comment',
            'image': 'fa-image',
            'video': 'fa-video',
            'audio': 'fa-volume-up',
            'document': 'fa-file',
            'location': 'fa-map-marker',
            'contact': 'fa-user',
            'sticker': 'fa-smile',
            'voice': 'fa-microphone'
        };
        return icons[messageType] || 'fa-comment';
    }
};

// Export for use in other modules
window.WhatsAppUtils = WhatsAppUtils;
