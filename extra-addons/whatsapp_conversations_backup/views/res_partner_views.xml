<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Partner Form View to Add WhatsApp Features -->
    <record id="view_partner_form_whatsapp" model="ir.ui.view">
        <field name="name">res.partner.form.whatsapp</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <!-- Add WhatsApp button to button box -->
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_send_whatsapp_message" type="object" 
                        class="oe_stat_button" icon="fa-whatsapp"
                        invisible="not has_whatsapp or whatsapp_status == 'blocked'">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_text">Send</span>
                        <span class="o_stat_text">WhatsApp</span>
                    </div>
                </button>
                
                <button name="action_view_whatsapp_conversations" type="object" 
                        class="oe_stat_button" icon="fa-comments"
                        invisible="whatsapp_conversation_count == 0">
                    <field name="whatsapp_conversation_count" widget="statinfo" 
                           string="Conversations"/>
                </button>
                
                <button name="action_view_whatsapp_payments" type="object" 
                        class="oe_stat_button" icon="fa-money"
                        invisible="whatsapp_payment_count == 0">
                    <field name="whatsapp_payment_count" widget="statinfo" 
                           string="Payments"/>
                </button>
            </xpath>
            
            <!-- Add WhatsApp fields to contact information -->
            <xpath expr="//field[@name='mobile']" position="after">
                <field name="whatsapp_number" widget="phone"/>
                <field name="whatsapp_name"/>
                <field name="has_whatsapp" invisible="1"/>
            </xpath>
            
            <!-- Add WhatsApp tab -->
            <xpath expr="//notebook" position="inside">
                <page string="WhatsApp" name="whatsapp" 
                      invisible="not has_whatsapp">
                    <group>
                        <group name="whatsapp_info">
                            <field name="whatsapp_status" widget="badge"
                                   decoration-success="whatsapp_status == 'active'"
                                   decoration-danger="whatsapp_status == 'blocked'"
                                   decoration-warning="whatsapp_status == 'spam'"
                                   decoration-muted="whatsapp_status == 'inactive'"/>
                            <field name="whatsapp_opt_in" widget="boolean_toggle"/>
                            <field name="whatsapp_language"/>
                            <field name="whatsapp_timezone"/>
                        </group>
                        <group name="whatsapp_stats">
                            <field name="whatsapp_message_count"/>
                            <field name="whatsapp_unread_count" 
                                   decoration-danger="whatsapp_unread_count > 0"/>
                            <field name="last_whatsapp_message"/>
                            <field name="whatsapp_total_payments" widget="monetary"/>
                            <field name="whatsapp_pending_payments" widget="monetary"
                                   decoration-warning="whatsapp_pending_payments > 0"/>
                        </group>
                    </group>
                    
                    <group name="whatsapp_actions" string="Quick Actions">
                        <div class="oe_button_box">
                            <button name="action_send_whatsapp_message" type="object" 
                                    string="Send Message" class="btn-primary"
                                    icon="fa-paper-plane"
                                    invisible="not has_whatsapp or whatsapp_status == 'blocked'"/>
                            <button name="action_create_whatsapp_payment" type="object" 
                                    string="Create Payment" class="btn-secondary"
                                    icon="fa-plus"
                                    invisible="not has_whatsapp"/>
                            <button name="action_whatsapp_payment_reminder" type="object" 
                                    string="Payment Reminder" class="btn-warning"
                                    icon="fa-bell"
                                    invisible="whatsapp_pending_payments == 0"/>
                            <button name="toggle_whatsapp_opt_in" type="object" 
                                    string="Toggle Opt-in" class="btn-secondary"
                                    icon="fa-toggle-on"/>
                        </div>
                    </group>
                    
                    <field name="whatsapp_notes" 
                           placeholder="Internal notes about WhatsApp communication with this contact..."/>
                </page>
            </xpath>
        </field>
    </record>

    <!-- Inherit Partner Tree View to Show WhatsApp Status -->
    <record id="view_partner_tree_whatsapp" model="ir.ui.view">
        <field name="name">res.partner.tree.whatsapp</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='phone']" position="after">
                <field name="whatsapp_number" optional="hide"/>
                <field name="has_whatsapp" widget="boolean_toggle" optional="show"/>
                <field name="whatsapp_unread_count" optional="hide"
                       decoration-danger="whatsapp_unread_count > 0"/>
            </xpath>
        </field>
    </record>

    <!-- Partner Search View with WhatsApp Filters -->
    <record id="view_partner_search_whatsapp" model="ir.ui.view">
        <field name="name">res.partner.search.whatsapp</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='supplier']" position="after">
                <separator/>
                <filter string="Has WhatsApp" name="has_whatsapp" 
                        domain="[('has_whatsapp', '=', True)]"/>
                <filter string="WhatsApp Active" name="whatsapp_active" 
                        domain="[('whatsapp_status', '=', 'active')]"/>
                <filter string="WhatsApp Opted In" name="whatsapp_opted_in" 
                        domain="[('whatsapp_opt_in', '=', True)]"/>
                <filter string="Unread WhatsApp Messages" name="whatsapp_unread" 
                        domain="[('whatsapp_unread_count', '>', 0)]"/>
                <filter string="Pending WhatsApp Payments" name="whatsapp_pending_payments" 
                        domain="[('whatsapp_pending_payments', '>', 0)]"/>
            </xpath>
            
            <xpath expr="//group[@name='group_by']" position="inside">
                <filter string="WhatsApp Status" name="group_whatsapp_status" 
                        context="{'group_by': 'whatsapp_status'}"/>
            </xpath>
        </field>
    </record>

</odoo>
