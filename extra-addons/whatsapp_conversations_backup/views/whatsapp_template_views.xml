<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Template Tree View -->
    <record id="view_whatsapp_template_tree" model="ir.ui.view">
        <field name="name">whatsapp.template.tree</field>
        <field name="model">whatsapp.template</field>
        <field name="arch" type="xml">
            <tree string="WhatsApp Templates" 
                  decoration-muted="not is_active">
                <field name="name"/>
                <field name="category_id"/>
                <field name="template_type"/>
                <field name="language"/>
                <field name="is_active" widget="boolean_toggle"/>
                <field name="usage_count"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Template Form View -->
    <record id="view_whatsapp_template_form" model="ir.ui.view">
        <field name="name">whatsapp.template.form</field>
        <field name="model">whatsapp.template</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Template">
                <header>
                    <button name="action_preview_template" type="object"
                            string="Preview" class="btn-primary"
                            icon="fa-eye"/>
                    <button name="action_use_template" type="object"
                            string="Use Template" class="btn-secondary"
                            icon="fa-paper-plane"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" title="Inactive" 
                            bg_color="bg-danger" 
                            invisible="is_active"/>
                    
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Template Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group name="basic_info">
                            <field name="category_id"/>
                            <field name="template_type"/>
                            <field name="language"/>
                            <field name="is_active"/>
                        </group>
                        <group name="usage_info">
                            <field name="usage_count" readonly="1"/>
                            <field name="create_date" readonly="1"/>
                            <field name="last_used" readonly="1"/>
                        </group>
                    </group>
                    
                    <group name="content" string="Template Content">
                        <field name="content" widget="text" nolabel="1" 
                               placeholder="Enter your template content here..."/>
                    </group>
                    
                    <notebook>
                        <page string="Variables" name="variables">
                            <field name="variable_ids" mode="tree">
                                <tree string="Variables" editable="bottom">
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="default_value"/>
                                    <field name="is_required"/>
                                    <field name="sequence"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Preview" name="preview">
                            <group>
                                <field name="content" widget="text" readonly="1" string="Template Content"/>
                                <field name="placeholder_count" readonly="1"/>
                            </group>
                        </page>
                        <page string="Usage Statistics" name="stats">
                            <group>
                                <field name="usage_count" readonly="1"/>
                                <field name="last_used" readonly="1"/>
                                <field name="user_id" readonly="1"/>
                                <field name="company_id" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- WhatsApp Template Search View -->
    <record id="view_whatsapp_template_search" model="ir.ui.view">
        <field name="name">whatsapp.template.search</field>
        <field name="model">whatsapp.template</field>
        <field name="arch" type="xml">
            <search string="Search Templates">
                <field name="name" string="Template Name"/>
                <field name="category_id" string="Category"/>
                <field name="content" string="Content"/>
                
                <filter string="Active" name="active" 
                        domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" 
                        domain="[('is_active', '=', False)]"/>
                
                <separator/>
                <filter string="Welcome" name="welcome"
                        domain="[('template_type', '=', 'welcome')]"/>
                <filter string="Payment Reminder" name="payment_reminder"
                        domain="[('template_type', '=', 'payment_reminder')]"/>
                <filter string="Custom" name="custom"
                        domain="[('template_type', '=', 'custom')]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Category" name="group_category"
                            context="{'group_by': 'category_id'}"/>
                    <filter string="Type" name="group_type"
                            context="{'group_by': 'template_type'}"/>
                    <filter string="Language" name="group_language"
                            context="{'group_by': 'language'}"/>
                    <filter string="User" name="group_user"
                            context="{'group_by': 'user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Template Category Tree View -->
    <record id="view_whatsapp_template_category_tree" model="ir.ui.view">
        <field name="name">whatsapp.template.category.tree</field>
        <field name="model">whatsapp.template.category</field>
        <field name="arch" type="xml">
            <tree string="Template Categories">
                <field name="name"/>
                <field name="description"/>
                <field name="template_count"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Template Category Form View -->
    <record id="view_whatsapp_template_category_form" model="ir.ui.view">
        <field name="name">whatsapp.template.category.form</field>
        <field name="model">whatsapp.template.category</field>
        <field name="arch" type="xml">
            <form string="Template Category">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Category Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <field name="description"/>
                        <field name="template_count" readonly="1"/>
                    </group>
                    
                    <notebook>
                        <page string="Templates" name="templates">
                            <field name="template_ids" mode="tree">
                                <tree string="Templates">
                                    <field name="name"/>
                                    <field name="template_type"/>
                                    <field name="is_active"/>
                                    <field name="usage_count"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_whatsapp_template_tree" model="ir.actions.act_window">
        <field name="name">Message Templates</field>
        <field name="res_model">whatsapp.template</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_whatsapp_template_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No WhatsApp templates found!
            </p>
            <p>
                Create message templates to send standardized messages quickly.
            </p>
        </field>
    </record>

    <record id="action_whatsapp_template_category_tree" model="ir.actions.act_window">
        <field name="name">Template Categories</field>
        <field name="res_model">whatsapp.template.category</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No template categories found!
            </p>
            <p>
                Create categories to organize your message templates.
            </p>
        </field>
    </record>

</odoo>
