# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class WhatsAppPaymentWizard(models.TransientModel):
    _name = 'whatsapp.payment.wizard'
    _description = 'WhatsApp Payment Recording Wizard'

    # Basic Information
    conversation_id = fields.Many2one(
        'whatsapp.conversation',
        string='Conversation',
        required=True
    )
    partner_id = fields.Many2one(
        'res.partner',
        string='Contact',
        required=True
    )
    
    # Payment Details
    amount = fields.Monetary(
        string='Amount',
        required=True,
        currency_field='currency_id'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id,
        required=True
    )
    payment_date = fields.Datetime(
        string='Payment Date',
        default=fields.Datetime.now,
        required=True
    )
    payment_method = fields.Selection([
        ('upi', 'UPI'),
        ('bank_transfer', 'Bank Transfer'),
        ('credit_card', 'Credit Card'),
        ('debit_card', 'Debit Card'),
        ('wallet', 'Digital Wallet'),
        ('cash', 'Cash'),
        ('other', 'Other')
    ], string='Payment Method', default='upi', required=True)
    
    # References
    reference = fields.Char(
        string='Payment Reference',
        help='Internal reference for this payment'
    )
    transaction_id = fields.Char(
        string='Transaction ID',
        help='External transaction ID from payment gateway'
    )
    
    # Description
    description = fields.Text(
        string='Description',
        help='Payment description or notes'
    )
    
    # Verification
    is_verified = fields.Boolean(
        string='Verified',
        default=False,
        help='Whether this payment has been verified'
    )
    payment_gateway = fields.Char(
        string='Payment Gateway',
        help='Payment gateway used (if applicable)'
    )
    verification_data = fields.Text(
        string='Verification Data',
        help='Additional verification details'
    )
    
    # Notes
    notes = fields.Text(
        string='Internal Notes',
        help='Internal notes about this payment'
    )
    
    @api.onchange('conversation_id')
    def _onchange_conversation_id(self):
        if self.conversation_id:
            self.partner_id = self.conversation_id.partner_id
    
    @api.constrains('amount')
    def _check_amount(self):
        for record in self:
            if record.amount <= 0:
                raise ValidationError(_('Payment amount must be greater than zero'))
    
    def action_create_payment(self):
        """Create the payment record"""
        self.ensure_one()
        
        # Validate required fields
        if not self.amount or self.amount <= 0:
            raise UserError(_('Please enter a valid payment amount'))
        
        if not self.conversation_id:
            raise UserError(_('Please select a conversation'))
        
        if not self.partner_id:
            raise UserError(_('Please select a contact'))
        
        # Create payment record
        payment_vals = {
            'conversation_id': self.conversation_id.id,
            'partner_id': self.partner_id.id,
            'amount': self.amount,
            'currency_id': self.currency_id.id,
            'payment_date': self.payment_date,
            'payment_method': self.payment_method,
            'reference': self.reference,
            'transaction_id': self.transaction_id,
            'description': self.description,
            'is_verified': self.is_verified,
            'payment_gateway': self.payment_gateway,
            'verification_data': self.verification_data,
            'notes': self.notes,
            'payment_status': 'confirmed' if self.is_verified else 'pending',
        }
        
        try:
            payment = self.env['whatsapp.payment'].create(payment_vals)
            
            # Log the payment creation
            _logger.info(f"WhatsApp payment created: {payment.id} for {self.partner_id.name}")
            
            # Return action to view the created payment
            return {
                'type': 'ir.actions.act_window',
                'name': _('WhatsApp Payment'),
                'res_model': 'whatsapp.payment',
                'res_id': payment.id,
                'view_mode': 'form',
                'target': 'current',
            }
            
        except Exception as e:
            _logger.error(f"Failed to create WhatsApp payment: {e}")
            raise UserError(_('Failed to create payment record: %s') % str(e))
    
    def action_create_and_notify(self):
        """Create payment and send notification"""
        self.ensure_one()
        
        # Create the payment
        result = self.action_create_payment()
        
        # Send notification message if payment is confirmed
        if self.is_verified:
            try:
                self._send_payment_confirmation()
            except Exception as e:
                _logger.warning(f"Failed to send payment confirmation: {e}")
        
        return result
    
    def _send_payment_confirmation(self):
        """Send payment confirmation message"""
        # Get payment confirmation template
        template = self.env['whatsapp.template'].search([
            ('code', '=', 'payment_confirmed'),
            ('is_active', '=', True)
        ], limit=1)
        
        if template:
            # Prepare template variables
            values = {
                'partner_name': self.partner_id.name,
                'amount': self.amount,
                'currency': self.currency_id.symbol,
                'reference': self.reference or 'N/A',
            }
            
            # Render template
            message_content = template.render_template(values)
            
            # Create and send message
            message_vals = {
                'conversation_id': self.conversation_id.id,
                'direction': 'outgoing',
                'message_type': 'text',
                'content': message_content,
                'user_id': self.env.user.id,
            }
            
            message = self.env['whatsapp.message'].create(message_vals)
            message._send_to_whatsapp()
        
        else:
            # Send simple confirmation message
            message_content = f"✅ Payment Confirmed\n\nDear {self.partner_id.name}, we have received your payment of {self.currency_id.symbol}{self.amount}.\nThank you!"
            
            message_vals = {
                'conversation_id': self.conversation_id.id,
                'direction': 'outgoing',
                'message_type': 'text',
                'content': message_content,
                'user_id': self.env.user.id,
            }
            
            message = self.env['whatsapp.message'].create(message_vals)
            message._send_to_whatsapp()
