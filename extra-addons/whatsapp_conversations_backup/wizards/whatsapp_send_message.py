# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)


class WhatsAppSendMessage(models.TransientModel):
    _name = 'whatsapp.send.message'
    _description = 'Send WhatsApp Message Wizard'

    # Recipient Information
    partner_id = fields.Many2one(
        'res.partner',
        string='Contact',
        help='Contact to send message to'
    )
    phone_number = fields.Char(
        string='Phone Number',
        required=True,
        help='WhatsApp phone number'
    )
    conversation_id = fields.Many2one(
        'whatsapp.conversation',
        string='Conversation',
        help='Existing conversation'
    )
    
    # Message Content
    message_content = fields.Text(
        string='Message',
        required=True,
        help='Message content to send'
    )
    message_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('document', 'Document')
    ], string='Message Type', default='text', required=True)
    
    # Template Support
    template_id = fields.Many2one(
        'whatsapp.template',
        string='Use Template',
        help='Select a message template'
    )
    template_variables = fields.Text(
        string='Template Variables',
        help='JSON string of template variables'
    )
    
    # Media Attachment
    attachment_ids = fields.Many2many(
        'ir.attachment',
        string='Attachments',
        help='Files to send with the message'
    )
    
    # Instance Selection
    instance_id = fields.Many2one(
        'whatsapp.instance',
        string='WhatsApp Instance',
        required=True,
        default=lambda self: self.env['whatsapp.instance'].get_default_instance()
    )
    
    # Reply Information
    quoted_message_id = fields.Many2one(
        'whatsapp.message',
        string='Reply to Message',
        help='Message being replied to'
    )
    
    # Scheduling
    schedule_send = fields.Boolean(
        string='Schedule Message',
        default=False,
        help='Schedule message for later sending'
    )
    scheduled_date = fields.Datetime(
        string='Scheduled Date',
        help='When to send the message'
    )
    
    # Options
    create_payment_record = fields.Boolean(
        string='Create Payment Record',
        default=False,
        help='Create a payment record along with this message'
    )
    payment_amount = fields.Monetary(
        string='Payment Amount',
        currency_field='currency_id'
    )
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    @api.onchange('partner_id')
    def _onchange_partner_id(self):
        if self.partner_id:
            self.phone_number = self.partner_id.get_whatsapp_number()
            
            # Try to find existing conversation
            if self.phone_number and self.instance_id:
                conversation = self.env['whatsapp.conversation'].search([
                    ('partner_id', '=', self.partner_id.id),
                    ('phone_number', '=', self.phone_number),
                    ('instance_id', '=', self.instance_id.id)
                ], limit=1)
                if conversation:
                    self.conversation_id = conversation
    
    @api.onchange('template_id')
    def _onchange_template_id(self):
        if self.template_id:
            # Render template with default values
            default_values = self._get_template_default_values()
            self.message_content = self.template_id.render_template(default_values)
    
    @api.onchange('schedule_send')
    def _onchange_schedule_send(self):
        if not self.schedule_send:
            self.scheduled_date = False
    
    @api.constrains('scheduled_date')
    def _check_scheduled_date(self):
        for record in self:
            if record.schedule_send and record.scheduled_date:
                if record.scheduled_date <= fields.Datetime.now():
                    raise ValidationError(_('Scheduled date must be in the future'))
    
    def _get_template_default_values(self):
        """Get default values for template rendering"""
        values = {}
        
        if self.partner_id:
            values.update({
                'partner_name': self.partner_id.name,
                'partner_phone': self.partner_id.phone or self.partner_id.mobile,
                'partner_email': self.partner_id.email,
            })
        
        values.update({
            'company_name': self.env.company.name,
            'user_name': self.env.user.name,
            'current_date': fields.Date.today().strftime('%Y-%m-%d'),
            'current_time': fields.Datetime.now().strftime('%H:%M'),
        })
        
        if self.payment_amount:
            values.update({
                'amount': self.payment_amount,
                'currency': self.currency_id.symbol,
            })
        
        return values
    
    def action_send_message(self):
        """Send the WhatsApp message"""
        self.ensure_one()
        
        if not self.instance_id:
            raise UserError(_('Please select a WhatsApp instance'))
        
        if not self.phone_number:
            raise UserError(_('Phone number is required'))
        
        if not self.message_content.strip():
            raise UserError(_('Message content cannot be empty'))
        
        # Get or create conversation
        conversation = self._get_or_create_conversation()
        
        # Create message record
        message_vals = {
            'conversation_id': conversation.id,
            'direction': 'outgoing',
            'content': self.message_content,
            'message_type': self.message_type,
            'user_id': self.env.user.id,
        }
        
        if self.quoted_message_id:
            message_vals['quoted_message_id'] = self.quoted_message_id.id
        
        message = self.env['whatsapp.message'].create(message_vals)
        
        # Handle attachments
        if self.attachment_ids and self.message_type != 'text':
            self._create_message_attachments(message)
        
        # Send message immediately or schedule
        if self.schedule_send and self.scheduled_date:
            self._schedule_message(message)
        else:
            self._send_message_now(message)
        
        # Create payment record if requested
        if self.create_payment_record and self.payment_amount:
            self._create_payment_record(conversation)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Message sent successfully!'),
                'type': 'success',
            }
        }
    
    def _get_or_create_conversation(self):
        """Get existing conversation or create new one"""
        if self.conversation_id:
            return self.conversation_id
        
        # Create or get conversation
        conversation = self.env['whatsapp.conversation'].create_or_get_conversation(
            phone_number=self.phone_number,
            instance_id=self.instance_id.id,
            partner_id=self.partner_id.id if self.partner_id else None
        )
        
        return conversation
    
    def _create_message_attachments(self, message):
        """Create attachments for the message"""
        for attachment in self.attachment_ids:
            self.env['whatsapp.attachment'].create({
                'message_id': message.id,
                'filename': attachment.name,
                'file_data': attachment.datas,
                'mimetype': attachment.mimetype,
                'file_size': len(attachment.datas) if attachment.datas else 0,
            })
    
    def _send_message_now(self, message):
        """Send message immediately"""
        try:
            message._send_to_whatsapp()
        except Exception as e:
            raise UserError(_('Failed to send message: %s') % str(e))
    
    def _schedule_message(self, message):
        """Schedule message for later sending"""
        # Create scheduled job
        self.env['ir.cron'].create({
            'name': f'Send WhatsApp Message {message.id}',
            'model_id': self.env.ref('whatsapp_conversations.model_whatsapp_message').id,
            'state': 'code',
            'code': f'model.browse({message.id})._send_to_whatsapp()',
            'nextcall': self.scheduled_date,
            'numbercall': 1,
            'active': True,
        })
        
        message.write({'status': 'pending'})
    
    def _create_payment_record(self, conversation):
        """Create payment record"""
        payment_vals = {
            'conversation_id': conversation.id,
            'partner_id': self.partner_id.id if self.partner_id else conversation.partner_id.id,
            'amount': self.payment_amount,
            'currency_id': self.currency_id.id,
            'payment_method': 'other',
            'description': f'Payment mentioned in WhatsApp message',
            'user_id': self.env.user.id,
        }
        
        self.env['whatsapp.payment'].create(payment_vals)
    
    def action_preview_message(self):
        """Preview the message before sending"""
        self.ensure_one()
        
        if self.template_id:
            values = self._get_template_default_values()
            preview_content = self.template_id.render_template(values)
        else:
            preview_content = self.message_content
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Message Preview'),
            'res_model': 'whatsapp.message.preview',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_content': preview_content,
                'default_partner_name': self.partner_id.name if self.partner_id else 'Unknown',
                'default_phone_number': self.phone_number,
            }
        }
