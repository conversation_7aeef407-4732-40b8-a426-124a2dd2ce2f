<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Welcome Message Templates -->
        <record id="template_welcome_new_customer" model="whatsapp.template">
            <field name="name">Welcome New Customer</field>
            <field name="code">welcome_new_customer</field>
            <field name="template_type">welcome</field>
            <field name="category_id" ref="template_category_welcome"/>
            <field name="content">🎉 Welcome to {{company_name}}!

Hello {{partner_name}}, 

Thank you for choosing us! We're excited to have you as our customer.

Our team is here to help you with any questions you may have. Feel free to reach out anytime.

Best regards,
{{company_name}} Team</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <!-- Payment Templates -->
        <record id="template_payment_reminder" model="whatsapp.template">
            <field name="name">Payment Reminder</field>
            <field name="code">payment_reminder</field>
            <field name="template_type">payment_reminder</field>
            <field name="category_id" ref="template_category_payment"/>
            <field name="content">💰 Payment Reminder

Dear {{partner_name}},

This is a friendly reminder that your payment of {{currency}}{{amount}} is due.

📋 Reference: {{reference}}
📅 Due Date: {{due_date}}

Please make the payment at your earliest convenience.

For any queries, feel free to contact us.

Thank you!
{{company_name}}</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <record id="template_payment_confirmation" model="whatsapp.template">
            <field name="name">Payment Confirmation</field>
            <field name="code">payment_confirmation</field>
            <field name="template_type">payment_confirmation</field>
            <field name="category_id" ref="template_category_payment"/>
            <field name="content">✅ Payment Received

Dear {{partner_name}},

We have successfully received your payment of {{currency}}{{amount}}.

📋 Reference: {{reference}}
📅 Payment Date: {{payment_date}}
💳 Method: {{payment_method}}

Thank you for your prompt payment!

{{company_name}}</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <record id="template_payment_overdue" model="whatsapp.template">
            <field name="name">Payment Overdue</field>
            <field name="code">payment_overdue</field>
            <field name="template_type">payment_reminder</field>
            <field name="category_id" ref="template_category_payment"/>
            <field name="content">⚠️ Payment Overdue

Dear {{partner_name}},

Your payment of {{currency}}{{amount}} is now overdue.

📋 Reference: {{reference}}
📅 Due Date: {{due_date}}
⏰ Days Overdue: {{days_overdue}}

Please make the payment immediately to avoid any inconvenience.

Contact us if you need assistance.

{{company_name}}</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <!-- Order Templates -->
        <record id="template_order_confirmation" model="whatsapp.template">
            <field name="name">Order Confirmation</field>
            <field name="code">order_confirmation</field>
            <field name="template_type">order_confirmation</field>
            <field name="category_id" ref="template_category_support"/>
            <field name="content">🛍️ Order Confirmed

Hi {{partner_name}},

Your order has been confirmed!

📦 Order Number: {{order_number}}
💰 Amount: {{currency}}{{amount}}
📅 Order Date: {{order_date}}

We will keep you updated on the delivery status.

Thank you for your business!
{{company_name}}</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <record id="template_delivery_update" model="whatsapp.template">
            <field name="name">Delivery Update</field>
            <field name="code">delivery_update</field>
            <field name="template_type">delivery_update</field>
            <field name="category_id" ref="template_category_support"/>
            <field name="content">🚚 Delivery Update

Hi {{partner_name}},

Your order #{{order_number}} is on its way!

📍 Current Status: {{delivery_status}}
📅 Expected Delivery: {{expected_delivery_date}}
🔗 Tracking: {{tracking_number}}

You can track your order using the tracking number above.

{{company_name}}</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <!-- Support Templates -->
        <record id="template_support_ticket_created" model="whatsapp.template">
            <field name="name">Support Ticket Created</field>
            <field name="code">support_ticket_created</field>
            <field name="template_type">support</field>
            <field name="category_id" ref="template_category_support"/>
            <field name="content">🎫 Support Ticket Created

Hi {{partner_name}},

We have received your support request.

🎫 Ticket Number: {{ticket_number}}
📝 Subject: {{ticket_subject}}
⏰ Created: {{ticket_date}}

Our team will get back to you within 24 hours.

Thank you for contacting us!
{{company_name}} Support</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <record id="template_support_ticket_resolved" model="whatsapp.template">
            <field name="name">Support Ticket Resolved</field>
            <field name="code">support_ticket_resolved</field>
            <field name="template_type">support</field>
            <field name="category_id" ref="template_category_support"/>
            <field name="content">✅ Support Ticket Resolved

Hi {{partner_name}},

Your support ticket has been resolved!

🎫 Ticket Number: {{ticket_number}}
📝 Subject: {{ticket_subject}}
✅ Resolved: {{resolution_date}}

If you need further assistance, please don't hesitate to contact us.

{{company_name}} Support</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <!-- Promotional Templates -->
        <record id="template_promotional_offer" model="whatsapp.template">
            <field name="name">Promotional Offer</field>
            <field name="code">promotional_offer</field>
            <field name="template_type">promotional</field>
            <field name="category_id" ref="template_category_promotional"/>
            <field name="content">🎉 Special Offer Just for You!

Hi {{partner_name}},

We have an exclusive offer for you:

🏷️ {{offer_title}}
💰 {{discount_percentage}}% OFF
⏰ Valid until: {{offer_expiry}}

Use code: {{promo_code}}

Don't miss out on this amazing deal!

Shop now: {{shop_link}}

{{company_name}}</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <record id="template_birthday_wishes" model="whatsapp.template">
            <field name="name">Birthday Wishes</field>
            <field name="code">birthday_wishes</field>
            <field name="template_type">promotional</field>
            <field name="category_id" ref="template_category_promotional"/>
            <field name="content">🎂 Happy Birthday!

Dear {{partner_name}},

Wishing you a very Happy Birthday! 🎉

As a birthday gift, enjoy {{discount_percentage}}% off on your next purchase.

Use code: BIRTHDAY{{current_year}}

Valid for 7 days from today.

Have a wonderful day!
{{company_name}} Team</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

        <!-- General Templates -->
        <record id="template_thank_you" model="whatsapp.template">
            <field name="name">Thank You Message</field>
            <field name="code">thank_you</field>
            <field name="template_type">custom</field>
            <field name="category_id" ref="template_category_support"/>
            <field name="content">🙏 Thank You!

Dear {{partner_name}},

Thank you for choosing {{company_name}}!

Your trust and support mean the world to us. We're committed to providing you with the best service possible.

If you have any feedback or suggestions, we'd love to hear from you.

Best regards,
{{company_name}} Team</field>
            <field name="is_active">True</field>
            <field name="is_public">True</field>
        </record>

    </data>
</odoo>
