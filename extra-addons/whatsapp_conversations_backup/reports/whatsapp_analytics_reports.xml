<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Message Analytics Report Action -->
    <record id="action_whatsapp_message_report" model="ir.actions.act_window">
        <field name="name">Message Analytics</field>
        <field name="res_model">whatsapp.message.report</field>
        <field name="view_mode">graph,pivot,tree</field>
        <field name="search_view_id" ref="view_whatsapp_message_report_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No message data found!
            </p>
            <p>
                Message analytics will appear here once you have WhatsApp messages.
            </p>
        </field>
    </record>

    <!-- WhatsApp Message Report Tree View -->
    <record id="view_whatsapp_message_report_tree" model="ir.ui.view">
        <field name="name">whatsapp.message.report.tree</field>
        <field name="model">whatsapp.message.report</field>
        <field name="arch" type="xml">
            <tree string="Message Analytics" create="false" edit="false">
                <field name="date"/>
                <field name="instance_id"/>
                <field name="message_type"/>
                <field name="direction"/>
                <field name="message_count"/>
                <field name="delivered_count"/>
                <field name="read_count"/>
                <field name="failed_count"/>
                <field name="media_count"/>
            </tree>
        </field>
    </record>

    <!-- WhatsApp Message Report Graph View -->
    <record id="view_whatsapp_message_report_graph" model="ir.ui.view">
        <field name="name">whatsapp.message.report.graph</field>
        <field name="model">whatsapp.message.report</field>
        <field name="arch" type="xml">
            <graph string="Message Analytics" type="line">
                <field name="date" type="row"/>
                <field name="message_count" type="measure"/>
                <field name="delivered_count" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- WhatsApp Message Report Pivot View -->
    <record id="view_whatsapp_message_report_pivot" model="ir.ui.view">
        <field name="name">whatsapp.message.report.pivot</field>
        <field name="model">whatsapp.message.report</field>
        <field name="arch" type="xml">
            <pivot string="Message Analytics">
                <field name="date" type="row"/>
                <field name="direction" type="col"/>
                <field name="message_count" type="measure"/>
                <field name="delivered_count" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- WhatsApp Message Report Search View -->
    <record id="view_whatsapp_message_report_search" model="ir.ui.view">
        <field name="name">whatsapp.message.report.search</field>
        <field name="model">whatsapp.message.report</field>
        <field name="arch" type="xml">
            <search string="Search Message Analytics">
                <field name="instance_id"/>
                <field name="message_type"/>
                <field name="direction"/>
                
                <filter string="Incoming" name="incoming" 
                        domain="[('direction', '=', 'incoming')]"/>
                <filter string="Outgoing" name="outgoing" 
                        domain="[('direction', '=', 'outgoing')]"/>
                
                <separator/>
                <filter string="Today" name="today" 
                        domain="[('date', '=', context_today())]"/>
                <filter string="This Week" name="this_week" 
                        domain="[('date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" 
                        domain="[('date', '>=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Date" name="group_date" 
                            context="{'group_by': 'date'}"/>
                    <filter string="Instance" name="group_instance" 
                            context="{'group_by': 'instance_id'}"/>
                    <filter string="Message Type" name="group_type" 
                            context="{'group_by': 'message_type'}"/>
                    <filter string="Direction" name="group_direction" 
                            context="{'group_by': 'direction'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Settings Action -->
    <record id="action_whatsapp_settings" model="ir.actions.act_window">
        <field name="name">WhatsApp Settings</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module': 'whatsapp_conversations'}</field>
    </record>

    <!-- WhatsApp Sync Messages Action -->
    <record id="action_whatsapp_sync_messages" model="ir.actions.server">
        <field name="name">Sync WhatsApp Messages</field>
        <field name="model_id" ref="model_whatsapp_instance"/>
        <field name="state">code</field>
        <field name="code">
            instances = env['whatsapp.instance'].search([('is_active', '=', True)])
            for instance in instances:
                instance.action_sync_messages()
        </field>
    </record>

    <!-- WhatsApp Send Message Wizard Action -->
    <record id="action_whatsapp_send_message_wizard" model="ir.actions.act_window">
        <field name="name">Send WhatsApp Message</field>
        <field name="res_model">whatsapp.send.message</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_whatsapp_send_message_form"/>
    </record>

</odoo>
