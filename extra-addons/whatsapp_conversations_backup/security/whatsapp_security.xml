<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- WhatsApp Conversations Security Groups -->
    
    <!-- Base WhatsApp User Group -->
    <record id="group_whatsapp_user" model="res.groups">
        <field name="name">WhatsApp User</field>
        <field name="category_id" ref="base.module_category_communication"/>
        <field name="comment">Basic WhatsApp conversation access</field>
    </record>
    
    <!-- WhatsApp Manager Group -->
    <record id="group_whatsapp_manager" model="res.groups">
        <field name="name">WhatsApp Manager</field>
        <field name="category_id" ref="base.module_category_communication"/>
        <field name="implied_ids" eval="[(4, ref('group_whatsapp_user'))]"/>
        <field name="comment">Full WhatsApp conversation management</field>
    </record>
    
    <!-- WhatsApp Payment Manager Group -->
    <record id="group_whatsapp_payment_manager" model="res.groups">
        <field name="name">WhatsApp Payment Manager</field>
        <field name="category_id" ref="base.module_category_accounting"/>
        <field name="implied_ids" eval="[(4, ref('group_whatsapp_manager'))]"/>
        <field name="comment">WhatsApp payment records management</field>
    </record>
    
    <!-- WhatsApp System Administrator Group -->
    <record id="group_whatsapp_admin" model="res.groups">
        <field name="name">WhatsApp System Administrator</field>
        <field name="category_id" ref="base.module_category_administration"/>
        <field name="implied_ids" eval="[(4, ref('group_whatsapp_payment_manager'))]"/>
        <field name="comment">Full WhatsApp system administration</field>
    </record>

    <!-- Record Rules for WhatsApp Conversations -->
    <!-- Note: Model rules will be added after module installation -->

</odoo>
