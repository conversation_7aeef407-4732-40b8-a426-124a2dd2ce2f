<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Demo WhatsApp Instance -->
    <record id="demo_whatsapp_instance" model="whatsapp.instance">
        <field name="name">Demo WhatsApp Instance</field>
        <field name="session_id">demo_instance</field>
        <field name="phone_number">+1234567890</field>
        <field name="api_url">http://localhost:3001</field>
        <field name="status">connected</field>
        <field name="is_active">True</field>
        <field name="is_default">True</field>
        <field name="auto_sync">True</field>
        <field name="sync_interval">5</field>
    </record>

    <!-- Demo Template Categories -->
    <record id="demo_template_category_sales" model="whatsapp.template.category">
        <field name="name">Sales</field>
        <field name="description">Sales related message templates</field>
        <field name="color">1</field>
    </record>

    <record id="demo_template_category_support" model="whatsapp.template.category">
        <field name="name">Support</field>
        <field name="description">Customer support message templates</field>
        <field name="color">2</field>
    </record>

    <!-- Demo Message Templates -->
    <record id="demo_template_welcome" model="whatsapp.template">
        <field name="name">Welcome Message</field>
        <field name="code">welcome_demo</field>
        <field name="category_id" ref="demo_template_category_sales"/>
        <field name="template_type">welcome</field>
        <field name="content">Hello {{partner_name}}! Welcome to {{company_name}}. We are excited to serve you!</field>
        <field name="is_active">True</field>
        <field name="is_public">True</field>
        <field name="language">en_US</field>
    </record>

    <record id="demo_template_payment_reminder" model="whatsapp.template">
        <field name="name">Payment Reminder</field>
        <field name="code">payment_reminder_demo</field>
        <field name="category_id" ref="demo_template_category_sales"/>
        <field name="template_type">payment_reminder</field>
        <field name="content">Dear {{partner_name}}, this is a friendly reminder that your payment of {{currency}}{{amount}} is due. Reference: {{reference}}</field>
        <field name="is_active">True</field>
        <field name="is_public">True</field>
        <field name="language">en_US</field>
    </record>

    <record id="demo_template_support" model="whatsapp.template">
        <field name="name">Support Response</field>
        <field name="code">support_response_demo</field>
        <field name="category_id" ref="demo_template_category_support"/>
        <field name="template_type">support</field>
        <field name="content">Hi {{partner_name}}, thank you for contacting our support team. We have received your inquiry and will respond within 24 hours.</field>
        <field name="is_active">True</field>
        <field name="is_public">True</field>
        <field name="language">en_US</field>
    </record>

    <!-- Demo Partners -->
    <record id="demo_partner_john" model="res.partner">
        <field name="name">John Doe</field>
        <field name="phone">+1234567891</field>
        <field name="mobile">+1234567891</field>
        <field name="email"><EMAIL></field>
        <field name="is_company">False</field>
    </record>

    <record id="demo_partner_jane" model="res.partner">
        <field name="name">Jane Smith</field>
        <field name="phone">+1234567892</field>
        <field name="mobile">+1234567892</field>
        <field name="email"><EMAIL></field>
        <field name="is_company">False</field>
    </record>

    <!-- Demo Conversations -->
    <record id="demo_conversation_john" model="whatsapp.conversation">
        <field name="partner_id" ref="demo_partner_john"/>
        <field name="phone_number">+1234567891</field>
        <field name="instance_id" ref="demo_whatsapp_instance"/>
        <field name="status">active</field>
        <field name="priority">1</field>
        <field name="user_id" ref="base.user_admin"/>
    </record>

    <record id="demo_conversation_jane" model="whatsapp.conversation">
        <field name="partner_id" ref="demo_partner_jane"/>
        <field name="phone_number">+1234567892</field>
        <field name="instance_id" ref="demo_whatsapp_instance"/>
        <field name="status">active</field>
        <field name="priority">2</field>
        <field name="user_id" ref="base.user_admin"/>
    </record>

    <!-- Demo Messages -->
    <record id="demo_message_1" model="whatsapp.message">
        <field name="conversation_id" ref="demo_conversation_john"/>
        <field name="direction">incoming</field>
        <field name="message_type">text</field>
        <field name="content">Hello, I'm interested in your products</field>
        <field name="status">delivered</field>
        <field name="is_read">True</field>
        <field name="date_sent" eval="(DateTime.now() - timedelta(hours=2))"/>
    </record>

    <record id="demo_message_2" model="whatsapp.message">
        <field name="conversation_id" ref="demo_conversation_john"/>
        <field name="direction">outgoing</field>
        <field name="message_type">text</field>
        <field name="content">Hello John! Thank you for your interest. How can we help you today?</field>
        <field name="status">delivered</field>
        <field name="user_id" ref="base.user_admin"/>
        <field name="date_sent" eval="(DateTime.now() - timedelta(hours=1))"/>
    </record>

    <record id="demo_message_3" model="whatsapp.message">
        <field name="conversation_id" ref="demo_conversation_jane"/>
        <field name="direction">incoming</field>
        <field name="message_type">text</field>
        <field name="content">Hi, I need support with my recent order</field>
        <field name="status">delivered</field>
        <field name="is_read">False</field>
        <field name="date_sent" eval="(DateTime.now() - timedelta(minutes=30))"/>
    </record>

    <!-- Demo Payment -->
    <record id="demo_payment_1" model="whatsapp.payment">
        <field name="conversation_id" ref="demo_conversation_john"/>
        <field name="partner_id" ref="demo_partner_john"/>
        <field name="amount">150.00</field>
        <field name="currency_id" ref="base.USD"/>
        <field name="payment_method">upi</field>
        <field name="payment_status">confirmed</field>
        <field name="reference">PAY-001</field>
        <field name="transaction_id">TXN123456789</field>
        <field name="description">Payment for product order</field>
        <field name="is_verified">True</field>
        <field name="payment_date" eval="(DateTime.now() - timedelta(days=1))"/>
    </record>

</odoo>
