# -*- coding: utf-8 -*-

from odoo import models, fields, api, tools
import logging

_logger = logging.getLogger(__name__)


class WhatsAppConversationReport(models.Model):
    _name = 'whatsapp.conversation.report'
    _description = 'WhatsApp Conversation Analytics'
    _auto = False
    _rec_name = 'date'

    # Date and Time
    date = fields.Date(string='Date', readonly=True)
    
    # Relations
    instance_id = fields.Many2one('whatsapp.instance', string='Instance', readonly=True)
    partner_id = fields.Many2one('res.partner', string='Contact', readonly=True)
    user_id = fields.Many2one('res.users', string='Assigned User', readonly=True)
    
    # Conversation Metrics
    conversation_count = fields.Integer(string='Conversations', readonly=True)
    message_count = fields.Integer(string='Total Messages', readonly=True)
    incoming_count = fields.Integer(string='Incoming Messages', readonly=True)
    outgoing_count = fields.Integer(string='Outgoing Messages', readonly=True)
    unread_count = fields.Integer(string='Unread Messages', readonly=True)
    
    # Response Time
    response_time_avg = fields.Float(string='Avg Response Time (hours)', readonly=True)
    
    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    ROW_NUMBER() OVER () AS id,
                    DATE(wc.last_message_date) AS date,
                    wc.instance_id,
                    wc.partner_id,
                    wc.user_id,
                    COUNT(DISTINCT wc.id) AS conversation_count,
                    COUNT(wm.id) AS message_count,
                    COUNT(CASE WHEN wm.direction = 'incoming' THEN 1 END) AS incoming_count,
                    COUNT(CASE WHEN wm.direction = 'outgoing' THEN 1 END) AS outgoing_count,
                    COUNT(CASE WHEN wm.direction = 'incoming' AND NOT wm.is_read THEN 1 END) AS unread_count,
                    AVG(CASE 
                        WHEN wm.direction = 'outgoing' AND wm_prev.direction = 'incoming' 
                        THEN EXTRACT(EPOCH FROM (wm.date_sent - wm_prev.date_sent)) / 3600.0
                    END) AS response_time_avg
                FROM whatsapp_conversation wc
                LEFT JOIN whatsapp_message wm ON wc.id = wm.conversation_id
                LEFT JOIN whatsapp_message wm_prev ON wm.quoted_message_id = wm_prev.id
                WHERE wc.last_message_date IS NOT NULL
                GROUP BY 
                    DATE(wc.last_message_date),
                    wc.instance_id,
                    wc.partner_id,
                    wc.user_id
            )
        """ % self._table)


class WhatsAppPaymentReport(models.Model):
    _name = 'whatsapp.payment.report'
    _description = 'WhatsApp Payment Analytics'
    _auto = False
    _rec_name = 'date'

    # Date and Time
    date = fields.Date(string='Date', readonly=True)
    
    # Relations
    instance_id = fields.Many2one('whatsapp.instance', string='Instance', readonly=True)
    partner_id = fields.Many2one('res.partner', string='Contact', readonly=True)
    currency_id = fields.Many2one('res.currency', string='Currency', readonly=True)
    
    # Payment Details
    payment_method = fields.Selection([
        ('cash', 'Cash'),
        ('bank_transfer', 'Bank Transfer'),
        ('upi', 'UPI'),
        ('card', 'Credit/Debit Card'),
        ('wallet', 'Digital Wallet'),
        ('cheque', 'Cheque'),
        ('other', 'Other')
    ], string='Payment Method', readonly=True)
    
    # Payment Metrics
    payment_count = fields.Integer(string='Payment Count', readonly=True)
    total_amount = fields.Monetary(string='Total Amount', currency_field='currency_id', readonly=True)
    confirmed_count = fields.Integer(string='Confirmed Payments', readonly=True)
    pending_count = fields.Integer(string='Pending Payments', readonly=True)
    failed_count = fields.Integer(string='Failed Payments', readonly=True)
    confirmed_amount = fields.Monetary(string='Confirmed Amount', currency_field='currency_id', readonly=True)
    pending_amount = fields.Monetary(string='Pending Amount', currency_field='currency_id', readonly=True)
    
    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    ROW_NUMBER() OVER () AS id,
                    DATE(wp.payment_date) AS date,
                    wc.instance_id,
                    wp.partner_id,
                    wp.currency_id,
                    wp.payment_method,
                    COUNT(wp.id) AS payment_count,
                    SUM(wp.amount) AS total_amount,
                    COUNT(CASE WHEN wp.payment_status = 'confirmed' THEN 1 END) AS confirmed_count,
                    COUNT(CASE WHEN wp.payment_status = 'pending' THEN 1 END) AS pending_count,
                    COUNT(CASE WHEN wp.payment_status = 'failed' THEN 1 END) AS failed_count,
                    SUM(CASE WHEN wp.payment_status = 'confirmed' THEN wp.amount ELSE 0 END) AS confirmed_amount,
                    SUM(CASE WHEN wp.payment_status = 'pending' THEN wp.amount ELSE 0 END) AS pending_amount
                FROM whatsapp_payment wp
                LEFT JOIN whatsapp_conversation wc ON wp.conversation_id = wc.id
                GROUP BY 
                    DATE(wp.payment_date),
                    wc.instance_id,
                    wp.partner_id,
                    wp.currency_id,
                    wp.payment_method
            )
        """ % self._table)


class WhatsAppMessageReport(models.Model):
    _name = 'whatsapp.message.report'
    _description = 'WhatsApp Message Analytics'
    _auto = False
    _rec_name = 'date'

    # Date and Time
    date = fields.Date(string='Date', readonly=True)
    hour = fields.Integer(string='Hour', readonly=True)
    
    # Relations
    instance_id = fields.Many2one('whatsapp.instance', string='Instance', readonly=True)
    partner_id = fields.Many2one('res.partner', string='Contact', readonly=True)
    user_id = fields.Many2one('res.users', string='User', readonly=True)
    
    # Message Details
    message_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('document', 'Document'),
        ('location', 'Location'),
        ('contact', 'Contact'),
        ('sticker', 'Sticker'),
        ('voice', 'Voice Note'),
        ('system', 'System Message')
    ], string='Message Type', readonly=True)
    
    direction = fields.Selection([
        ('incoming', 'Incoming'),
        ('outgoing', 'Outgoing')
    ], string='Direction', readonly=True)
    
    # Message Metrics
    message_count = fields.Integer(string='Message Count', readonly=True)
    delivered_count = fields.Integer(string='Delivered Messages', readonly=True)
    read_count = fields.Integer(string='Read Messages', readonly=True)
    failed_count = fields.Integer(string='Failed Messages', readonly=True)
    media_count = fields.Integer(string='Media Messages', readonly=True)
    
    # Response Metrics
    avg_response_time = fields.Float(string='Avg Response Time (minutes)', readonly=True)
    
    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    ROW_NUMBER() OVER () AS id,
                    DATE(wm.date_sent) AS date,
                    EXTRACT(HOUR FROM wm.date_sent) AS hour,
                    wc.instance_id,
                    wm.partner_id,
                    wm.user_id,
                    wm.message_type,
                    wm.direction,
                    COUNT(wm.id) AS message_count,
                    COUNT(CASE WHEN wm.status IN ('delivered', 'read') THEN 1 END) AS delivered_count,
                    COUNT(CASE WHEN wm.status = 'read' THEN 1 END) AS read_count,
                    COUNT(CASE WHEN wm.status = 'failed' THEN 1 END) AS failed_count,
                    COUNT(CASE WHEN wm.message_type != 'text' THEN 1 END) AS media_count,
                    AVG(CASE 
                        WHEN wm.direction = 'outgoing' AND wm_prev.direction = 'incoming' 
                        THEN EXTRACT(EPOCH FROM (wm.date_sent - wm_prev.date_sent)) / 60.0
                    END) AS avg_response_time
                FROM whatsapp_message wm
                LEFT JOIN whatsapp_conversation wc ON wm.conversation_id = wc.id
                LEFT JOIN whatsapp_message wm_prev ON wm.quoted_message_id = wm_prev.id
                GROUP BY 
                    DATE(wm.date_sent),
                    EXTRACT(HOUR FROM wm.date_sent),
                    wc.instance_id,
                    wm.partner_id,
                    wm.user_id,
                    wm.message_type,
                    wm.direction
            )
        """ % self._table)
