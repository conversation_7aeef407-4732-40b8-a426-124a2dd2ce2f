# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re
import logging

_logger = logging.getLogger(__name__)


class WhatsAppTemplate(models.Model):
    _name = 'whatsapp.template'
    _description = 'WhatsApp Message Template'
    _order = 'name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(
        string='Template Name',
        required=True,
        help='Name of the message template'
    )
    code = fields.Char(
        string='Template Code',
        required=True,
        help='Unique code for this template'
    )
    description = fields.Text(
        string='Description',
        help='Description of when to use this template'
    )
    
    # Template Content
    content = fields.Text(
        string='Message Content',
        required=True,
        help='Template content with placeholders like {{partner_name}}, {{amount}}, etc.'
    )
    
    # Template Type
    template_type = fields.Selection([
        ('welcome', 'Welcome Message'),
        ('payment_reminder', 'Payment Reminder'),
        ('payment_confirmation', 'Payment Confirmation'),
        ('order_confirmation', 'Order Confirmation'),
        ('delivery_update', 'Delivery Update'),
        ('promotional', 'Promotional'),
        ('support', 'Support'),
        ('custom', 'Custom')
    ], string='Template Type', default='custom', required=True)
    
    # Template Category
    category_id = fields.Many2one(
        'whatsapp.template.category',
        string='Category',
        help='Template category for organization'
    )
    
    # Usage Settings
    is_active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this template is active'
    )
    is_public = fields.Boolean(
        string='Public Template',
        default=False,
        help='Allow all users to use this template'
    )
    
    # Variables and Placeholders
    variable_ids = fields.One2many(
        'whatsapp.template.variable',
        'template_id',
        string='Variables',
        help='Variables used in this template'
    )
    placeholder_count = fields.Integer(
        string='Placeholder Count',
        compute='_compute_placeholder_count',
        store=True,
        help='Number of placeholders in the template'
    )
    
    # Usage Statistics
    usage_count = fields.Integer(
        string='Usage Count',
        default=0,
        help='Number of times this template has been used'
    )
    last_used = fields.Datetime(
        string='Last Used',
        help='When this template was last used'
    )
    
    # User and Company
    user_id = fields.Many2one(
        'res.users',
        string='Created By',
        default=lambda self: self.env.user,
        required=True
    )
    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        required=True
    )
    
    # Language Support
    language = fields.Selection(
        selection='_get_languages',
        string='Language',
        default='en_US',
        help='Language of this template'
    )
    
    @api.model
    def _get_languages(self):
        """Get available languages"""
        return self.env['res.lang'].get_installed()
    
    @api.depends('content')
    def _compute_placeholder_count(self):
        for record in self:
            if record.content:
                # Count placeholders like {{variable_name}}
                placeholders = re.findall(r'\{\{[^}]+\}\}', record.content)
                record.placeholder_count = len(placeholders)
            else:
                record.placeholder_count = 0
    
    @api.constrains('code')
    def _check_unique_code(self):
        for record in self:
            if record.code:
                existing = self.search([
                    ('code', '=', record.code),
                    ('id', '!=', record.id),
                    ('company_id', '=', record.company_id.id)
                ])
                if existing:
                    raise ValidationError(_('Template code must be unique within the company'))
    
    def action_preview_template(self):
        """Preview template with sample data"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Template Preview'),
            'res_model': 'whatsapp.template.preview',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id,
            }
        }
    
    def action_use_template(self):
        """Use this template to send a message"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Send Message'),
            'res_model': 'whatsapp.send.message',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_template_id': self.id,
                'default_message_content': self.content,
            }
        }
    
    def render_template(self, values=None):
        """Render template with provided values"""
        self.ensure_one()
        if not values:
            values = {}
        
        content = self.content
        
        # Replace placeholders with values
        for key, value in values.items():
            placeholder = f"{{{{{key}}}}}"
            content = content.replace(placeholder, str(value))
        
        # Update usage statistics
        self.sudo().write({
            'usage_count': self.usage_count + 1,
            'last_used': fields.Datetime.now()
        })
        
        return content
    
    def get_template_variables(self):
        """Get list of variables used in template"""
        self.ensure_one()
        if not self.content:
            return []
        
        # Extract variables from placeholders
        placeholders = re.findall(r'\{\{([^}]+)\}\}', self.content)
        return list(set(placeholders))
    
    @api.model
    def get_default_variables(self):
        """Get default variables available for templates"""
        return {
            'partner_name': _('Contact Name'),
            'partner_phone': _('Contact Phone'),
            'partner_email': _('Contact Email'),
            'company_name': _('Company Name'),
            'user_name': _('User Name'),
            'current_date': _('Current Date'),
            'current_time': _('Current Time'),
            'amount': _('Amount'),
            'currency': _('Currency'),
            'reference': _('Reference'),
            'order_number': _('Order Number'),
            'invoice_number': _('Invoice Number'),
        }
    
    @api.model
    def create_default_templates(self):
        """Create default message templates"""
        default_templates = [
            {
                'name': 'Welcome Message',
                'code': 'welcome',
                'template_type': 'welcome',
                'content': 'Hello {{partner_name}}! Welcome to {{company_name}}. We are here to help you with any questions you may have.',
            },
            {
                'name': 'Payment Reminder',
                'code': 'payment_reminder',
                'template_type': 'payment_reminder',
                'content': 'Dear {{partner_name}}, this is a reminder that your payment of {{currency}}{{amount}} is due. Reference: {{reference}}',
            },
            {
                'name': 'Payment Confirmation',
                'code': 'payment_confirmed',
                'template_type': 'payment_confirmation',
                'content': '✅ Payment Confirmed\n\nDear {{partner_name}}, we have received your payment of {{currency}}{{amount}}.\nReference: {{reference}}\nThank you!',
            },
            {
                'name': 'Order Confirmation',
                'code': 'order_confirmed',
                'template_type': 'order_confirmation',
                'content': '🛍️ Order Confirmed\n\nHi {{partner_name}}, your order #{{order_number}} has been confirmed.\nAmount: {{currency}}{{amount}}\nWe will update you on the delivery status.',
            },
        ]
        
        for template_data in default_templates:
            existing = self.search([('code', '=', template_data['code'])], limit=1)
            if not existing:
                self.create(template_data)


class WhatsAppTemplateCategory(models.Model):
    _name = 'whatsapp.template.category'
    _description = 'WhatsApp Template Category'
    _order = 'name'

    name = fields.Char(
        string='Category Name',
        required=True
    )
    description = fields.Text(
        string='Description'
    )
    color = fields.Integer(
        string='Color',
        default=0
    )
    template_ids = fields.One2many(
        'whatsapp.template',
        'category_id',
        string='Templates'
    )
    template_count = fields.Integer(
        string='Template Count',
        compute='_compute_template_count'
    )
    
    @api.depends('template_ids')
    def _compute_template_count(self):
        for record in self:
            record.template_count = len(record.template_ids)


class WhatsAppTemplateVariable(models.Model):
    _name = 'whatsapp.template.variable'
    _description = 'WhatsApp Template Variable'
    _order = 'sequence, name'

    name = fields.Char(
        string='Variable Name',
        required=True,
        help='Variable name (without curly braces)'
    )
    description = fields.Char(
        string='Description',
        help='Description of this variable'
    )
    default_value = fields.Char(
        string='Default Value',
        help='Default value for this variable'
    )
    is_required = fields.Boolean(
        string='Required',
        default=False,
        help='Whether this variable is required'
    )
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Order of this variable'
    )
    template_id = fields.Many2one(
        'whatsapp.template',
        string='Template',
        required=True,
        ondelete='cascade'
    )
