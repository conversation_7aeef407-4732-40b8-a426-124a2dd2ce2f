<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_x_client_documents_form" model="ir.ui.view">
        <field name="name">x_client_documents.form</field>
        <field name="model">x_client_documents</field>
        <field name="arch" type="xml">
            <form string="Client Documents">
                <sheet>
                    <group>
                        <group name="client_doc" string="Client Documents">
                            <field name="name"/>

                            <field name="x_client"/>

                            <field name="x_document"/>

                            <field name="x_task_ref"/>

                            <field name="x_document_folder"/>

                        </group>
                        <!--<group name="amount" string="Amount">-->
                        <!--    <field name="amount"/>-->
                        <!--    <field name="unit_amount"/>-->
                        <!--    <field name="product_uom_category_id" invisible="1"/>-->
                        <!--    <field name="product_uom_id" class="oe_inline"/>-->
                        <!--    <field name="currency_id" invisible="1"/>-->
                        <!--</group>-->
                    </group>
                </sheet>
                <div class="oe_chatter">
                        <field name="message_follower_ids" groups="base.group_user"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                </div>
            </form>

        </field>
    </record>

    <record id="view_x_client_documents_tree" model="ir.ui.view">
        <field name="name">x_client_documents.tree</field>
        <field name="model">x_client_documents</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="x_client"/>
                <field name="x_task_ref"/>
                <field name="x_document_folder"/>
                <field name="x_client"/>
                <field name="x_document"/>


            </tree>
        </field>
    </record>

    <menuitem id="menu_x_client_documents" name="Client Documents" action="action_x_client_documents"/>

</odoo>
