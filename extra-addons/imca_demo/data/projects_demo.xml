<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Demo Projects for CA Services -->

        <!-- Tech Innovations Projects -->
        <record id="project_tech_innovations_annual" model="project.project">
            <field name="name">Tech Innovations - Annual Compliance</field>
            <field name="partner_id" ref="imca_demo.partner_tech_innovations_pvt_ltd"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Annual compliance services including audit, ROC filing, and tax returns for Tech Innovations Pvt Ltd</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="allow_timesheets">True</field>
            <field name="allow_billable_time">True</field>
        </record>

        <record id="project_tech_innovations_monthly" model="project.project">
            <field name="name">Tech Innovations - Monthly Services</field>
            <field name="partner_id" ref="imca_demo.partner_tech_innovations_pvt_ltd"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Monthly services including GST returns, bookkeeping, and payroll compliance</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <!-- Green Energy Projects -->
        <record id="project_green_energy_startup" model="project.project">
            <field name="name">Green Energy - Startup Compliance</field>
            <field name="partner_id" ref="imca_demo.partner_green_energy_solutions"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Complete startup compliance package including incorporation, GST registration, and initial tax setup</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
        </record>

        <!-- Retail Mart Projects -->
        <record id="project_retail_mart_gst" model="project.project">
            <field name="name">Retail Mart - GST &amp; Tax Services</field>
            <field name="partner_id" ref="imca_demo.partner_retail_mart_enterprises"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">GST return filing, income tax returns, and business advisory services</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <!-- Manufacturing Corp Projects -->
        <record id="project_manufacturing_comprehensive" model="project.project">
            <field name="name">Manufacturing Corp - Comprehensive Services</field>
            <field name="partner_id" ref="imca_demo.partner_manufacturing_corp"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Complete CA services including audit, tax planning, GST, PF, ESI, and financial advisory</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <!-- Consulting Services Projects -->
        <record id="project_consulting_llp" model="project.project">
            <field name="name">Strategic Consulting - LLP Services</field>
            <field name="partner_id" ref="imca_demo.partner_consulting_services"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">LLP compliance, tax returns, and business advisory services</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <!-- Individual Client Projects -->
        <record id="project_rajesh_sharma_individual" model="project.project">
            <field name="name">Rajesh Sharma - Individual Tax Services</field>
            <field name="partner_id" ref="imca_demo.partner_rajesh_sharma"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Individual income tax return filing and tax planning services</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="project_priya_patel_individual" model="project.project">
            <field name="name">Priya Patel - Individual Tax Services</field>
            <field name="partner_id" ref="imca_demo.partner_priya_patel"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Individual income tax return filing and investment advisory</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="project_amit_kumar_individual" model="project.project">
            <field name="name">Amit Kumar - Individual Tax Services</field>
            <field name="partner_id" ref="imca_demo.partner_amit_kumar"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Individual income tax return filing and financial planning</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="project_sunita_agarwal_individual" model="project.project">
            <field name="name">Sunita Agarwal - Individual Tax Services</field>
            <field name="partner_id" ref="imca_demo.partner_sunita_agarwal"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Individual income tax return filing and business income advisory</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="project_vikram_singh_individual" model="project.project">
            <field name="name">Vikram Singh - Individual Tax Services</field>
            <field name="partner_id" ref="imca_demo.partner_vikram_singh"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Individual income tax return filing and capital gains advisory</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <!-- Partnership Projects -->
        <record id="project_sharma_associates_partnership" model="project.project">
            <field name="name">Sharma Associates - Partnership Services</field>
            <field name="partner_id" ref="imca_demo.partner_sharma_associates"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">Partnership firm tax returns and compliance services</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

        <record id="project_patel_brothers_partnership" model="project.project">
            <field name="name">Patel Brothers - Trading Business Services</field>
            <field name="partner_id" ref="imca_demo.partner_patel_brothers"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy_visibility">portal</field>
            <field name="description">GST returns, income tax filing, and business advisory for trading business</field>
            <field name="date_start" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
        </record>

    </data>
</odoo>
