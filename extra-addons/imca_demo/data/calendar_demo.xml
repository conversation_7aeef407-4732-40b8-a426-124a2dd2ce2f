<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Calendar Events for CA Practice -->
        
        <!-- Client Meetings -->
        <record id="calendar_tech_innovations_audit_meeting" model="calendar.event">
            <field name="name">Audit Planning Meeting - Tech Innovations</field>
            <field name="description">Discuss audit scope, timeline, and documentation requirements for FY 2023-24 audit</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=2, hours=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=2, hours=12)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">2.0</field>
            <field name="location">Tech Innovations Office, Gurgaon</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_tech_innovations_pvt_ltd')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <record id="calendar_green_energy_consultation" model="calendar.event">
            <field name="name">Startup Consultation - Green Energy Solutions</field>
            <field name="description">Initial consultation for startup compliance and GST registration</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=1, hours=14)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=1, hours=15, minutes=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">1.5</field>
            <field name="location">IMCA Office</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_green_energy_solutions')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <record id="calendar_manufacturing_site_visit" model="calendar.event">
            <field name="name">Factory Site Visit - Manufacturing Corp</field>
            <field name="description">Physical verification of inventory and fixed assets</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=3, hours=9)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=3, hours=17)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">8.0</field>
            <field name="location">Manufacturing Corp Factory, Pune</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_manufacturing_corp')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <record id="calendar_retail_mart_review" model="calendar.event">
            <field name="name">Monthly Review - Retail Mart</field>
            <field name="description">Review monthly GST returns and discuss business performance</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=5, hours=11)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=5, hours=12)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">1.0</field>
            <field name="location">Retail Mart Office, Delhi</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_retail_mart_enterprises')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <!-- Individual Client Meetings -->
        <record id="calendar_rajesh_sharma_tax_planning" model="calendar.event">
            <field name="name">Tax Planning Session - Rajesh Sharma</field>
            <field name="description">Discuss investment options and tax saving strategies for FY 2024-25</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=4, hours=16)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=4, hours=17)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">1.0</field>
            <field name="location">IMCA Office</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_rajesh_sharma')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <record id="calendar_priya_patel_investment_review" model="calendar.event">
            <field name="name">Investment Portfolio Review - Priya Patel</field>
            <field name="description">Review capital gains and investment portfolio for tax optimization</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=6, hours=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=6, hours=16)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">1.0</field>
            <field name="location">Video Conference</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_priya_patel')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <!-- Internal Team Meetings -->
        <record id="calendar_team_weekly_meeting" model="calendar.event">
            <field name="name">Weekly Team Meeting</field>
            <field name="description">Weekly team sync-up to discuss client progress and upcoming deadlines</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=1, hours=9)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=1, hours=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">1.0</field>
            <field name="location">IMCA Conference Room</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
            <field name="recurrency">True</field>
            <field name="rrule">FREQ=WEEKLY;BYDAY=MO</field>
        </record>

        <record id="calendar_compliance_review_meeting" model="calendar.event">
            <field name="name">Monthly Compliance Review</field>
            <field name="description">Review all pending compliances and upcoming deadlines</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=7, hours=14)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=7, hours=16)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">2.0</field>
            <field name="location">IMCA Conference Room</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <!-- Training and Development -->
        <record id="calendar_gst_training_session" model="calendar.event">
            <field name="name">GST Updates Training</field>
            <field name="description">Training session on latest GST amendments and compliance requirements</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=8, hours=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=8, hours=12)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">2.0</field>
            <field name="location">IMCA Training Room</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <record id="calendar_audit_methodology_training" model="calendar.event">
            <field name="name">Audit Methodology Workshop</field>
            <field name="description">Workshop on modern audit techniques and risk assessment</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=10, hours=9)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=10, hours=17)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">8.0</field>
            <field name="location">External Training Center</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <!-- Compliance Deadlines -->
        <record id="calendar_gst_filing_deadline" model="calendar.event">
            <field name="name">GST Filing Deadline - November 2024</field>
            <field name="description">Deadline for filing GSTR-1 and GSTR-3B for November 2024</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=15, hours=23, minutes=59)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=15, hours=23, minutes=59)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">0.0</field>
            <field name="allday">True</field>
            <field name="location">Online Filing</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
            <field name="alarm_ids" eval="[(6, 0, [ref('calendar.alarm_notif_1')])]"/>
        </record>

        <record id="calendar_tds_filing_deadline" model="calendar.event">
            <field name="name">TDS Return Filing Deadline - Q2 FY 2024-25</field>
            <field name="description">Deadline for filing TDS returns for Q2 FY 2024-25</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=20, hours=23, minutes=59)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=20, hours=23, minutes=59)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">0.0</field>
            <field name="allday">True</field>
            <field name="location">Online Filing</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
            <field name="alarm_ids" eval="[(6, 0, [ref('calendar.alarm_notif_1')])]"/>
        </record>

        <!-- Professional Development -->
        <record id="calendar_ca_conference" model="calendar.event">
            <field name="name">Annual CA Conference 2024</field>
            <field name="description">National conference on emerging trends in taxation and auditing</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=25, hours=9)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=27, hours=17)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">56.0</field>
            <field name="location">Hotel Grand, Mumbai</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <!-- Client Follow-ups -->
        <record id="calendar_followup_sharma_associates" model="calendar.event">
            <field name="name">Follow-up Call - Sharma Associates</field>
            <field name="description">Follow-up on partnership return filing status</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=3, hours=11)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=3, hours=11, minutes=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">0.5</field>
            <field name="location">Phone Call</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_sharma_associates')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <record id="calendar_followup_patel_brothers" model="calendar.event">
            <field name="name">Document Collection - Patel Brothers</field>
            <field name="description">Collect pending documents for GST return filing</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=2, hours=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=2, hours=16)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">1.0</field>
            <field name="location">Patel Brothers Office, Surat</field>
            <field name="partner_ids" eval="[(6, 0, [ref('partner_patel_brothers')])]"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <!-- Business Development -->
        <record id="calendar_prospect_meeting" model="calendar.event">
            <field name="name">Prospect Meeting - New Manufacturing Client</field>
            <field name="description">Initial meeting with potential new manufacturing client</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=9, hours=14)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=9, hours=15, minutes=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">1.5</field>
            <field name="location">Prospect Office</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

        <!-- Year-end Planning -->
        <record id="calendar_year_end_planning" model="calendar.event">
            <field name="name">Year-end Tax Planning Workshop</field>
            <field name="description">Workshop for clients on year-end tax planning strategies</field>
            <field name="start" eval="(DateTime.now() + timedelta(days=30, hours=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="stop" eval="(DateTime.now() + timedelta(days=30, hours=16)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="duration">6.0</field>
            <field name="location">IMCA Conference Hall</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="privacy">public</field>
        </record>

    </data>
</odoo>
