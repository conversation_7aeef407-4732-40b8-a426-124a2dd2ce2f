<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Expense Categories -->
        <record id="expense_category_travel" model="product.product">
            <field name="name">Travel Expenses</field>
            <field name="can_be_expensed">True</field>
            <field name="type">service</field>
            <field name="list_price">0.0</field>
            <field name="standard_price">0.0</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="expense_category_meals" model="product.product">
            <field name="name">Meals &amp; Entertainment</field>
            <field name="can_be_expensed">True</field>
            <field name="type">service</field>
            <field name="list_price">0.0</field>
            <field name="standard_price">0.0</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="expense_category_office" model="product.product">
            <field name="name">Office Supplies</field>
            <field name="can_be_expensed">True</field>
            <field name="type">service</field>
            <field name="list_price">0.0</field>
            <field name="standard_price">0.0</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="expense_category_communication" model="product.product">
            <field name="name">Communication</field>
            <field name="can_be_expensed">True</field>
            <field name="type">service</field>
            <field name="list_price">0.0</field>
            <field name="standard_price">0.0</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <record id="expense_category_professional" model="product.product">
            <field name="name">Professional Development</field>
            <field name="can_be_expensed">True</field>
            <field name="type">service</field>
            <field name="list_price">0.0</field>
            <field name="standard_price">0.0</field>
            <field name="categ_id" ref="product.product_category_all"/>
        </record>

        <!-- Demo Expenses -->
        
        <!-- Partner Travel Expenses -->
        <record id="expense_partner_client_visit" model="hr.expense">
            <field name="name">Client Visit - Tech Innovations</field>
            <field name="employee_id" ref="employee_ca_partner"/>
            <field name="product_id" ref="expense_category_travel"/>
            <field name="total_amount">2500.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">Travel to client office for audit planning meeting</field>

        </record>

        <record id="expense_partner_conference" model="hr.expense">
            <field name="name">CA Conference Registration</field>
            <field name="employee_id" ref="employee_ca_senior_partner"/>
            <field name="product_id" ref="expense_category_professional"/>
            <field name="total_amount">15000.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="description">Annual CA Conference - New Tax Updates</field>
        </record>

        <!-- Senior Associate Expenses -->
        <record id="expense_senior_travel_manufacturing" model="hr.expense">
            <field name="name">Site Visit - Manufacturing Corp</field>
            <field name="employee_id" ref="employee_ca_senior_associate1"/>
            <field name="product_id" ref="expense_category_travel"/>
            <field name="total_amount">1800.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="description">Factory visit for physical verification</field>

        </record>

        <record id="expense_senior_meals_client" model="hr.expense">
            <field name="name">Client Lunch - Green Energy</field>
            <field name="employee_id" ref="employee_ca_senior_associate2"/>
            <field name="product_id" ref="expense_category_meals"/>
            <field name="total_amount">1200.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Business lunch with Green Energy management</field>

        </record>

        <!-- Associate Expenses -->
        <record id="expense_associate_office_supplies" model="hr.expense">
            <field name="name">Stationery and Printing</field>
            <field name="employee_id" ref="employee_ca_associate1"/>
            <field name="product_id" ref="expense_category_office"/>
            <field name="total_amount">850.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="description">Office supplies for client documentation</field>
        </record>

        <record id="expense_associate_communication" model="hr.expense">
            <field name="name">Mobile Recharge</field>
            <field name="employee_id" ref="employee_ca_associate2"/>
            <field name="product_id" ref="expense_category_communication"/>
            <field name="total_amount">500.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="description">Monthly mobile recharge for client communication</field>
        </record>

        <!-- Junior Staff Expenses -->
        <record id="expense_junior_training" model="hr.expense">
            <field name="name">Online Course - GST Updates</field>
            <field name="employee_id" ref="employee_ca_junior1"/>
            <field name="product_id" ref="expense_category_professional"/>
            <field name="total_amount">2500.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="description">Professional development course on latest GST changes</field>
        </record>

        <record id="expense_junior_travel_local" model="hr.expense">
            <field name="name">Local Travel - Document Collection</field>
            <field name="employee_id" ref="employee_ca_junior2"/>
            <field name="product_id" ref="expense_category_travel"/>
            <field name="total_amount">300.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Travel for collecting client documents</field>

        </record>

        <!-- Office Manager Expenses -->
        <record id="expense_office_manager_supplies" model="hr.expense">
            <field name="name">Office Equipment</field>
            <field name="employee_id" ref="employee_office_manager"/>
            <field name="product_id" ref="expense_category_office"/>
            <field name="total_amount">5000.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="description">Printer cartridges and office equipment</field>
        </record>

        <!-- IT Support Expenses -->
        <record id="expense_it_software" model="hr.expense">
            <field name="name">Software License Renewal</field>
            <field name="employee_id" ref="employee_it_support"/>
            <field name="product_id" ref="expense_category_professional"/>
            <field name="total_amount">8000.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="description">Annual software license for tax preparation software</field>
        </record>

        <!-- Intern Expenses -->
        <record id="expense_intern_transport" model="hr.expense">
            <field name="name">Daily Transport</field>
            <field name="employee_id" ref="employee_intern1"/>
            <field name="product_id" ref="expense_category_travel"/>
            <field name="total_amount">200.00</field>
            <field name="quantity">5</field>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="description">Weekly transport allowance</field>
        </record>

        <!-- Recurring Monthly Expenses -->
        <record id="expense_office_internet" model="hr.expense">
            <field name="name">Office Internet Bill</field>
            <field name="employee_id" ref="employee_office_manager"/>
            <field name="product_id" ref="expense_category_communication"/>
            <field name="total_amount">3500.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="description">Monthly office internet and communication expenses</field>
        </record>

        <record id="expense_office_electricity" model="hr.expense">
            <field name="name">Office Electricity Bill</field>
            <field name="employee_id" ref="employee_office_manager"/>
            <field name="product_id" ref="expense_category_office"/>
            <field name="total_amount">4500.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="description">Monthly office electricity expenses</field>
        </record>

        <!-- Client Entertainment Expenses -->
        <record id="expense_client_dinner" model="hr.expense">
            <field name="name">Client Appreciation Dinner</field>
            <field name="employee_id" ref="employee_ca_senior_partner"/>
            <field name="product_id" ref="expense_category_meals"/>
            <field name="total_amount">8000.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=6)).strftime('%Y-%m-%d')"/>
            <field name="description">Annual client appreciation dinner</field>
        </record>

        <!-- Professional Membership Expenses -->
        <record id="expense_ca_membership" model="hr.expense">
            <field name="name">ICAI Membership Renewal</field>
            <field name="employee_id" ref="employee_ca_partner"/>
            <field name="product_id" ref="expense_category_professional"/>
            <field name="total_amount">2000.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="description">Annual ICAI membership renewal</field>
        </record>

        <!-- Emergency Expenses -->
        <record id="expense_emergency_repair" model="hr.expense">
            <field name="name">Office AC Repair</field>
            <field name="employee_id" ref="employee_office_manager"/>
            <field name="product_id" ref="expense_category_office"/>
            <field name="total_amount">3200.00</field>
            <field name="quantity">1</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="description">Emergency air conditioning repair</field>
        </record>

    </data>
</odoo>
