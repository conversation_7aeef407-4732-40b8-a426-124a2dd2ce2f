<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- CA Services Demo Data -->
        <record id="service_gst_return_filing" model="x_services">
            <field name="name">GST Return Filing</field>
            <field name="x_agreed_fee">5000.00</field>
            <field name="x_planned_hours">8.0</field>
            <field name="x_execute_every">Months</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">GST Return Filing Reminder</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;This is a reminder for your monthly GST return filing due on 20th of this month.&lt;/p&gt;&lt;p&gt;Please provide the required documents.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Monthly GST Return Filing Process:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Collect sales and purchase invoices&lt;/li&gt;&lt;li&gt;Reconcile input and output tax&lt;/li&gt;&lt;li&gt;Prepare GSTR-1 and GSTR-3B&lt;/li&gt;&lt;li&gt;File returns on GST portal&lt;/li&gt;&lt;li&gt;Make tax payment if applicable&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_income_tax_return" model="x_services">
            <field name="name">Income Tax Return Filing</field>
            <field name="x_agreed_fee">3000.00</field>
            <field name="x_planned_hours">12.0</field>
            <field name="x_execute_every">Years</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">Income Tax Return Filing</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;It's time for your annual income tax return filing.&lt;/p&gt;&lt;p&gt;Please provide Form 16, investment proofs, and other relevant documents.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Annual Income Tax Return Filing:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Collect Form 16 and salary slips&lt;/li&gt;&lt;li&gt;Gather investment and deduction proofs&lt;/li&gt;&lt;li&gt;Calculate tax liability&lt;/li&gt;&lt;li&gt;Prepare and file ITR&lt;/li&gt;&lt;li&gt;E-verify the return&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_tds_return_filing" model="x_services">
            <field name="name">TDS Return Filing</field>
            <field name="x_agreed_fee">2500.00</field>
            <field name="x_planned_hours">6.0</field>
            <field name="x_execute_every">Quarter</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=90)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">TDS Return Filing Due</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;Your quarterly TDS return filing is due.&lt;/p&gt;&lt;p&gt;Please provide TDS payment challans and deductee details.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Quarterly TDS Return Filing:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Collect TDS payment challans&lt;/li&gt;&lt;li&gt;Prepare deductee details&lt;/li&gt;&lt;li&gt;File TDS return (24Q/26Q/27Q)&lt;/li&gt;&lt;li&gt;Generate TDS certificates&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_company_audit" model="x_services">
            <field name="name">Statutory Audit</field>
            <field name="x_agreed_fee">25000.00</field>
            <field name="x_planned_hours">80.0</field>
            <field name="x_execute_every">Years</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">Annual Audit Planning</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;We are planning to commence your annual statutory audit.&lt;/p&gt;&lt;p&gt;Please prepare all books of accounts and supporting documents.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Annual Statutory Audit Process:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Review books of accounts&lt;/li&gt;&lt;li&gt;Verify financial transactions&lt;/li&gt;&lt;li&gt;Check compliance with laws&lt;/li&gt;&lt;li&gt;Prepare audit report&lt;/li&gt;&lt;li&gt;File with ROC&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_roc_filing" model="x_services">
            <field name="name">ROC Annual Filing</field>
            <field name="x_agreed_fee">8000.00</field>
            <field name="x_planned_hours">16.0</field>
            <field name="x_execute_every">Years</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">ROC Annual Filing</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;Your annual ROC filing is due.&lt;/p&gt;&lt;p&gt;Please provide audited financial statements and board resolutions.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;ROC Annual Filing:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Prepare Form AOC-4&lt;/li&gt;&lt;li&gt;File Form MGT-7&lt;/li&gt;&lt;li&gt;Submit audited financials&lt;/li&gt;&lt;li&gt;Pay required fees&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_pf_return" model="x_services">
            <field name="name">PF Return Filing</field>
            <field name="x_agreed_fee">1500.00</field>
            <field name="x_planned_hours">4.0</field>
            <field name="x_execute_every">Months</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">PF Return Filing</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;Monthly PF return filing is due by 25th of this month.&lt;/p&gt;&lt;p&gt;Please provide employee salary details and PF contributions.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Monthly PF Return Filing:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Collect employee salary data&lt;/li&gt;&lt;li&gt;Calculate PF contributions&lt;/li&gt;&lt;li&gt;File ECR return&lt;/li&gt;&lt;li&gt;Make PF payment&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_esi_return" model="x_services">
            <field name="name">ESI Return Filing</field>
            <field name="x_agreed_fee">1200.00</field>
            <field name="x_planned_hours">3.0</field>
            <field name="x_execute_every">Months</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">ESI Return Filing</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;Monthly ESI return filing is due.&lt;/p&gt;&lt;p&gt;Please provide employee details and ESI contributions.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Monthly ESI Return Filing:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Collect employee data&lt;/li&gt;&lt;li&gt;Calculate ESI contributions&lt;/li&gt;&lt;li&gt;File monthly return&lt;/li&gt;&lt;li&gt;Make ESI payment&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_bookkeeping" model="x_services">
            <field name="name">Monthly Bookkeeping</field>
            <field name="x_agreed_fee">4000.00</field>
            <field name="x_planned_hours">20.0</field>
            <field name="x_execute_every">Months</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">Monthly Bookkeeping</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;Please provide all invoices, receipts, and bank statements for monthly bookkeeping.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Monthly Bookkeeping Services:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Record all transactions&lt;/li&gt;&lt;li&gt;Reconcile bank statements&lt;/li&gt;&lt;li&gt;Prepare trial balance&lt;/li&gt;&lt;li&gt;Generate financial reports&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_financial_planning" model="x_services">
            <field name="name">Financial Planning &amp; Advisory</field>
            <field name="x_agreed_fee">15000.00</field>
            <field name="x_planned_hours">24.0</field>
            <field name="x_execute_every">Quarter</field>
            <field name="x_interval_number">1</field>
            <field name="x_nextcall" eval="(DateTime.now() + timedelta(days=90)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="x_email_subject">Financial Planning Review</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;Time for your quarterly financial planning review.&lt;/p&gt;&lt;p&gt;Let's discuss your business performance and future strategies.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Financial Planning &amp; Advisory:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Review financial performance&lt;/li&gt;&lt;li&gt;Budget planning and forecasting&lt;/li&gt;&lt;li&gt;Investment advisory&lt;/li&gt;&lt;li&gt;Tax planning strategies&lt;/li&gt;&lt;li&gt;Business growth consultation&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

        <record id="service_company_incorporation" model="x_services">
            <field name="name">Company Incorporation</field>
            <field name="x_agreed_fee">12000.00</field>
            <field name="x_planned_hours">16.0</field>
            <field name="x_email_subject">Company Incorporation Process</field>
            <field name="x_email_template">&lt;p&gt;Dear Client,&lt;/p&gt;&lt;p&gt;We will help you incorporate your company.&lt;/p&gt;&lt;p&gt;Please provide required documents and details.&lt;/p&gt;&lt;p&gt;Best Regards,&lt;br/&gt;IMCA Team&lt;/p&gt;</field>
            <field name="x_task_description">&lt;p&gt;Company Incorporation Process:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Name reservation&lt;/li&gt;&lt;li&gt;Prepare incorporation documents&lt;/li&gt;&lt;li&gt;File with ROC&lt;/li&gt;&lt;li&gt;Obtain Certificate of Incorporation&lt;/li&gt;&lt;li&gt;Complete post-incorporation compliances&lt;/li&gt;&lt;/ul&gt;</field>
        </record>

    </data>
</odoo>
