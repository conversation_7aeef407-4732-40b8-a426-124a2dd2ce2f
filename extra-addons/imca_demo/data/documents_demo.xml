<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Client Documents -->
        
        <!-- Tech Innovations Documents -->
        <record id="doc_tech_innovations_incorporation" model="x_client_documents">
            <field name="name">Certificate of Incorporation</field>
            <field name="x_task_ref" ref="task_tech_innovations_roc_filing"/>
            <field name="x_document" eval="b'Sample Certificate of Incorporation Content'"/>
        </record>

        <record id="doc_tech_innovations_gst_certificate" model="x_client_documents">
            <field name="name">GST Registration Certificate</field>
            <field name="x_task_ref" ref="task_tech_innovations_gst_nov"/>
            <field name="x_document" eval="b'Sample GST Registration Certificate Content'"/>
        </record>

        <record id="doc_tech_innovations_pan_card" model="x_client_documents">
            <field name="name">Company PAN Card</field>
            <field name="x_task_ref" ref="task_tech_innovations_audit_2024"/>
            <field name="x_document" eval="b'Sample Company PAN Card Content'"/>
        </record>

        <record id="doc_tech_innovations_bank_statement" model="x_client_documents">
            <field name="name">Bank Statement - October 2024</field>
            <field name="x_task_ref" ref="task_tech_innovations_gst_nov"/>
            <field name="x_document" eval="b'Sample Bank Statement Content for October 2024'"/>
        </record>

        <!-- Green Energy Documents -->
        <record id="doc_green_energy_moa" model="x_client_documents">
            <field name="name">Memorandum of Association</field>
            <field name="x_task_ref" ref="task_green_energy_gst_registration"/>
            <field name="x_document" eval="b'Sample Memorandum of Association Content'"/>
        </record>

        <record id="doc_green_energy_aoa" model="x_client_documents">
            <field name="name">Articles of Association</field>
            <field name="x_task_ref" ref="task_green_energy_gst_registration"/>
            <field name="x_document" eval="b'Sample Articles of Association Content'"/>
        </record>

        <record id="doc_green_energy_director_pan" model="x_client_documents">
            <field name="name">Director PAN Cards</field>
            <field name="x_task_ref" ref="task_green_energy_gst_registration"/>
            <field name="x_document" eval="b'Sample Director PAN Cards Content'"/>
        </record>

        <!-- Retail Mart Documents -->
        <record id="doc_retail_mart_shop_license" model="x_client_documents">
            <field name="name">Shop and Establishment License</field>
            <field name="x_task_ref" ref="task_retail_mart_gst_oct"/>
            <field name="x_document" eval="b'Sample Shop License Content'"/>
        </record>

        <record id="doc_retail_mart_sales_invoices" model="x_client_documents">
            <field name="name">Sales Invoices - October 2024</field>
            <field name="x_task_ref" ref="task_retail_mart_gst_oct"/>
            <field name="x_document" eval="b'Sample Sales Invoices Content for October 2024'"/>
        </record>

        <record id="doc_retail_mart_purchase_invoices" model="x_client_documents">
            <field name="name">Purchase Invoices - October 2024</field>
            <field name="x_task_ref" ref="task_retail_mart_gst_oct"/>
            <field name="x_document" eval="b'Sample Purchase Invoices Content for October 2024'"/>
        </record>

        <!-- Manufacturing Corp Documents -->
        <record id="doc_manufacturing_factory_license" model="x_client_documents">
            <field name="name">Factory License</field>
            <field name="x_task_ref" ref="task_manufacturing_pf_oct"/>
            <field name="x_document" eval="b'Sample Factory License Content'"/>
        </record>

        <record id="doc_manufacturing_employee_list" model="x_client_documents">
            <field name="name">Employee List - October 2024</field>
            <field name="x_task_ref" ref="task_manufacturing_pf_oct"/>
            <field name="x_document" eval="b'Sample Employee List Content for October 2024'"/>
        </record>

        <record id="doc_manufacturing_salary_register" model="x_client_documents">
            <field name="name">Salary Register - October 2024</field>
            <field name="x_task_ref" ref="task_manufacturing_esi_oct"/>
            <field name="x_document" eval="b'Sample Salary Register Content for October 2024'"/>
        </record>

        <record id="doc_manufacturing_tds_certificates" model="x_client_documents">
            <field name="name">TDS Certificates Q2 FY 2024-25</field>
            <field name="x_task_ref" ref="task_manufacturing_tds_q2"/>
            <field name="x_document" eval="b'Sample TDS Certificates Content for Q2'"/>
        </record>

        <!-- Individual Client Documents -->
        <record id="doc_rajesh_sharma_form16" model="x_client_documents">
            <field name="name">Form 16 FY 2023-24</field>
            <field name="x_task_ref" ref="task_rajesh_sharma_itr"/>
            <field name="x_document" eval="b'Sample Form 16 Content for FY 2023-24'"/>
        </record>

        <record id="doc_rajesh_sharma_investment_proofs" model="x_client_documents">
            <field name="name">Investment Proofs FY 2023-24</field>
            <field name="x_task_ref" ref="task_rajesh_sharma_itr"/>
            <field name="x_document" eval="b'Sample Investment Proofs Content'"/>
        </record>

        <record id="doc_priya_patel_capital_gains" model="x_client_documents">
            <field name="name">Capital Gains Statement FY 2023-24</field>
            <field name="x_task_ref" ref="task_priya_patel_itr"/>
            <field name="x_document" eval="b'Sample Capital Gains Statement Content'"/>
        </record>

        <record id="doc_priya_patel_bank_statement" model="x_client_documents">
            <field name="name">Bank Statement FY 2023-24</field>
            <field name="x_task_ref" ref="task_priya_patel_itr"/>
            <field name="x_document" eval="b'Sample Bank Statement Content for FY 2023-24'"/>
        </record>

        <record id="doc_amit_kumar_salary_slips" model="x_client_documents">
            <field name="name">Salary Slips FY 2023-24</field>
            <field name="x_task_ref" ref="task_amit_kumar_tax_planning"/>
            <field name="x_document" eval="b'Sample Salary Slips Content for FY 2023-24'"/>
        </record>

        <!-- Partnership Documents -->
        <record id="doc_sharma_associates_partnership_deed" model="x_client_documents">
            <field name="name">Partnership Deed</field>
            <field name="x_task_ref" ref="task_sharma_associates_partnership_return"/>
            <field name="x_document" eval="b'Sample Partnership Deed Content'"/>
        </record>

        <record id="doc_sharma_associates_profit_loss" model="x_client_documents">
            <field name="name">Profit &amp; Loss Account FY 2023-24</field>
            <field name="x_task_ref" ref="task_sharma_associates_partnership_return"/>
            <field name="x_document" eval="b'Sample P&amp;L Account Content for FY 2023-24'"/>
        </record>

        <record id="doc_patel_brothers_trading_license" model="x_client_documents">
            <field name="name">Trading License</field>
            <field name="x_task_ref" ref="task_patel_brothers_gst_nov"/>
            <field name="x_document" eval="b'Sample Trading License Content'"/>
        </record>

        <record id="doc_patel_brothers_stock_register" model="x_client_documents">
            <field name="name">Stock Register - November 2024</field>
            <field name="x_task_ref" ref="task_patel_brothers_gst_nov"/>
            <field name="x_document" eval="b'Sample Stock Register Content for November 2024'"/>
        </record>

        <!-- Consulting Services Documents -->
        <record id="doc_consulting_llp_agreement" model="x_client_documents">
            <field name="name">LLP Agreement</field>
            <field name="x_task_ref" ref="task_consulting_llp_return"/>
            <field name="x_document" eval="b'Sample LLP Agreement Content'"/>
        </record>

        <record id="doc_consulting_service_contracts" model="x_client_documents">
            <field name="name">Service Contracts</field>
            <field name="x_task_ref" ref="task_consulting_gst_compliance"/>
            <field name="x_document" eval="b'Sample Service Contracts Content'"/>
        </record>

        <record id="doc_consulting_financial_statements" model="x_client_documents">
            <field name="name">Financial Statements FY 2023-24</field>
            <field name="x_task_ref" ref="task_consulting_llp_return"/>
            <field name="x_document" eval="b'Sample Financial Statements Content for FY 2023-24'"/>
        </record>

    </data>
</odoo>
