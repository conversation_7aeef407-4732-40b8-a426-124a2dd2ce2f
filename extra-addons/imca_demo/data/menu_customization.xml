<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Customize existing menu items for better CA practice presentation -->

        <!-- Rename Project menu to "Client Projects" -->
        <record id="project.menu_main_pm" model="ir.ui.menu">
            <field name="name">Client Projects</field>
            <field name="sequence">15</field>
        </record>

        <!-- Rename Contacts to "Clients" -->
        <record id="contacts.menu_contacts" model="ir.ui.menu">
            <field name="name">Clients</field>
            <field name="sequence">5</field>
        </record>

        <!-- Hide unnecessary menus for CA practice -->

        <!-- Customize Accounting menu for CA practice -->
        <record id="account.menu_finance" model="ir.ui.menu">
            <field name="name">Financial Management</field>
            <field name="sequence">20</field>
        </record>

        <!-- Add custom dashboard action -->
        <record id="action_imca_dashboard_kanban" model="ir.actions.act_window">
            <field name="name">IMCA Dashboard</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">kanban</field>
            <field name="domain">[('customer_rank', '>', 0)]</field>
            <field name="context">{'search_default_customer': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Welcome to IMCA - Your CA Practice Management System
                </p>
                <p>
                    Manage your clients, services, compliance, and documents all in one place.
                    Start by adding your first client or exploring the demo data.
                </p>
            </field>
        </record>

        <!-- Create main IMCA dashboard menu -->
        <record id="menu_imca_dashboard_main" model="ir.ui.menu">
            <field name="name">IMCA Dashboard</field>
            <field name="sequence">1</field>
            <field name="action" ref="action_imca_dashboard_kanban"/>
            <field name="web_icon">imca_demo,static/description/icon.png</field>
        </record>

        <!-- Enhance existing module menus with better names -->
        <record id="imca_groups.menu_x_groups" model="ir.ui.menu">
            <field name="name">Client Groups</field>
            <field name="parent_id" ref="contacts.menu_contacts"/>
            <field name="sequence">10</field>
        </record>

        <record id="imca_services.menu_x_services" model="ir.ui.menu">
            <field name="name">Service Portfolio</field>
            <field name="sequence">25</field>
        </record>

        <record id="imca_dsc_management.action_x_dsc" model="ir.actions.act_window">
            <field name="name">DSC Management</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Manage Digital Signature Certificates
                </p>
                <p>
                    Track DSC details, expiry dates, and usage for all your clients.
                    Never miss a DSC renewal with automated alerts.
                </p>
            </field>
        </record>

        <record id="imca_crednetials_manager.action_x_credentials" model="ir.actions.act_window">
            <field name="name">Portal Credentials</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Secure Credential Management
                </p>
                <p>
                    Safely store and manage all client portal credentials including
                    GST, Income Tax, PF, ESI, and other government portals.
                </p>
            </field>
        </record>

        <record id="imca_client_documents.action_x_client_documents" model="ir.actions.act_window">
            <field name="name">Document Manager</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Centralized Document Storage
                </p>
                <p>
                    Upload, organize, and manage all client documents securely.
                    Link documents to specific tasks and projects for easy access.
                </p>
            </field>
        </record>

        <!-- Create a comprehensive CA Services menu -->
        <record id="menu_ca_services_root" model="ir.ui.menu">
            <field name="name">CA Services</field>
            <field name="sequence">30</field>
        </record>

        <record id="menu_ca_services_gst" model="ir.ui.menu">
            <field name="name">GST Services</field>
            <field name="parent_id" ref="menu_ca_services_root"/>
            <field name="sequence">1</field>
            <field name="action" ref="imca_services.action_x_services"/>
        </record>

        <record id="menu_ca_services_income_tax" model="ir.ui.menu">
            <field name="name">Income Tax Services</field>
            <field name="parent_id" ref="menu_ca_services_root"/>
            <field name="sequence">2</field>
            <field name="action" ref="imca_services.action_x_services"/>
        </record>

        <record id="menu_ca_services_audit" model="ir.ui.menu">
            <field name="name">Audit Services</field>
            <field name="parent_id" ref="menu_ca_services_root"/>
            <field name="sequence">3</field>
            <field name="action" ref="imca_services.action_x_services"/>
        </record>

        <record id="menu_ca_services_compliance" model="ir.ui.menu">
            <field name="name">Compliance Services</field>
            <field name="parent_id" ref="menu_ca_services_root"/>
            <field name="sequence">4</field>
            <field name="action" ref="imca_services.action_x_services"/>
        </record>

        <!-- Create Compliance Management menu -->
        <record id="menu_compliance_root" model="ir.ui.menu">
            <field name="name">Compliance Management</field>
            <field name="sequence">35</field>
        </record>

        <record id="menu_compliance_calendar" model="ir.ui.menu">
            <field name="name">Compliance Calendar</field>
            <field name="parent_id" ref="menu_compliance_root"/>
            <field name="sequence">1</field>
            <field name="action" ref="project.open_view_project_all"/>
        </record>

        <record id="menu_compliance_alerts" model="ir.ui.menu">
            <field name="name">Due Date Alerts</field>
            <field name="parent_id" ref="menu_compliance_root"/>
            <field name="sequence">2</field>
            <field name="action" ref="project.action_view_task"/>
        </record>

        <!-- Create Reports menu for CA practice -->
        <record id="menu_ca_reports_root" model="ir.ui.menu">
            <field name="name">Reports &amp; Analytics</field>
            <field name="sequence">90</field>
        </record>

        <record id="menu_ca_reports_client" model="ir.ui.menu">
            <field name="name">Client Reports</field>
            <field name="parent_id" ref="menu_ca_reports_root"/>
            <field name="sequence">1</field>
        </record>

        <record id="menu_ca_reports_service" model="ir.ui.menu">
            <field name="name">Service Reports</field>
            <field name="parent_id" ref="menu_ca_reports_root"/>
            <field name="sequence">2</field>
        </record>

        <record id="menu_ca_reports_compliance" model="ir.ui.menu">
            <field name="name">Compliance Reports</field>
            <field name="parent_id" ref="menu_ca_reports_root"/>
            <field name="sequence">3</field>
        </record>

        <record id="menu_ca_reports_financial" model="ir.ui.menu">
            <field name="name">Financial Reports</field>
            <field name="parent_id" ref="menu_ca_reports_root"/>
            <field name="sequence">4</field>
        </record>

    </data>
</odoo>
