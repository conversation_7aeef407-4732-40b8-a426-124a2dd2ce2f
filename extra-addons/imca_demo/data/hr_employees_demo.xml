<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Demo Employees for CA Practice -->

        <!-- Senior CA Partners -->
        <record id="employee_ca_senior_partner" model="hr.employee">
            <field name="name"><PERSON><PERSON></field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543210</field>
            <field name="job_title">Senior Partner</field>
            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <record id="employee_ca_partner" model="hr.employee">
            <field name="name">CA <PERSON><PERSON></field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543211</field>
            <field name="job_title">Partner</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <!-- Senior Associates -->
        <record id="employee_ca_senior_associate1" model="hr.employee">
            <field name="name">CA Amit <PERSON></field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543212</field>
            <field name="job_title">Senior Associate</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <record id="employee_ca_senior_associate2" model="hr.employee">
            <field name="name">CA Sunita Agarwal</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543213</field>
            <field name="job_title">Senior Associate</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <!-- Associates -->
        <record id="employee_ca_associate1" model="hr.employee">
            <field name="name">CA Vikram Singh</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543214</field>
            <field name="job_title">Associate</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <record id="employee_ca_associate2" model="hr.employee">
            <field name="name">CA Neha Patel</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543215</field>
            <field name="job_title">Associate</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <!-- Junior Associates -->
        <record id="employee_ca_junior1" model="hr.employee">
            <field name="name">Rohit Verma</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543216</field>
            <field name="job_title">Junior Associate</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <record id="employee_ca_junior2" model="hr.employee">
            <field name="name">Pooja Sharma</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543217</field>
            <field name="job_title">Junior Associate</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <!-- Support Staff -->
        <record id="employee_office_manager" model="hr.employee">
            <field name="name">Ravi Kumar</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543218</field>
            <field name="job_title">Office Manager</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <record id="employee_receptionist" model="hr.employee">
            <field name="name">Anjali Gupta</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543219</field>
            <field name="job_title">Receptionist</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <!-- IT Support -->
        <record id="employee_it_support" model="hr.employee">
            <field name="name">Karan Singh</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543220</field>
            <field name="job_title">IT Support Specialist</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <!-- Intern -->
        <record id="employee_intern1" model="hr.employee">
            <field name="name">Arjun Patel</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543221</field>
            <field name="job_title">CA Intern</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

        <record id="employee_intern2" model="hr.employee">
            <field name="name">Kavya Reddy</field>
            <field name="work_email"><EMAIL></field>
            <field name="work_phone">+91-9876543222</field>
            <field name="job_title">CA Intern</field>



            <field name="resource_calendar_id" ref="resource.resource_calendar_std"/>


        </record>

    </data>
</odoo>
