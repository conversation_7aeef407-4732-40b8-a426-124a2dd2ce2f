<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Service Products for CA Practice -->
        
        <record id="service_gst_return_monthly" model="product.product">
            <field name="name">GST Return Filing (Monthly)</field>
            <field name="type">service</field>
            <field name="list_price">5000.00</field>
            <field name="standard_price">3000.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="description">Monthly GST return filing including GSTR-1 and GSTR-3B</field>
            <field name="invoice_policy">order</field>
            <field name="service_tracking">task_global_project</field>
            <field name="project_id" ref="project_tech_innovations_monthly"/>
        </record>

        <record id="service_income_tax_return" model="product.product">
            <field name="name">Income Tax Return Filing</field>
            <field name="type">service</field>
            <field name="list_price">3000.00</field>
            <field name="standard_price">1500.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="description">Annual income tax return preparation and filing</field>
            <field name="invoice_policy">order</field>
            <field name="service_tracking">task_global_project</field>
        </record>

        <record id="service_statutory_audit" model="product.product">
            <field name="name">Statutory Audit</field>
            <field name="type">service</field>
            <field name="list_price">25000.00</field>
            <field name="standard_price">15000.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="description">Annual statutory audit as per Companies Act</field>
            <field name="invoice_policy">order</field>
            <field name="service_tracking">task_global_project</field>
            <field name="project_id" ref="project_tech_innovations_annual"/>
        </record>

        <record id="service_roc_filing" model="product.product">
            <field name="name">ROC Annual Filing</field>
            <field name="type">service</field>
            <field name="list_price">8000.00</field>
            <field name="standard_price">5000.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="description">Annual ROC filing including Form AOC-4 and MGT-7</field>
            <field name="invoice_policy">order</field>
            <field name="service_tracking">task_global_project</field>
        </record>

        <record id="service_tds_return" model="product.product">
            <field name="name">TDS Return Filing (Quarterly)</field>
            <field name="type">service</field>
            <field name="list_price">2500.00</field>
            <field name="standard_price">1500.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="description">Quarterly TDS return filing (24Q/26Q/27Q)</field>
            <field name="invoice_policy">order</field>
            <field name="service_tracking">task_global_project</field>
        </record>

        <record id="service_bookkeeping" model="product.product">
            <field name="name">Monthly Bookkeeping</field>
            <field name="type">service</field>
            <field name="list_price">4000.00</field>
            <field name="standard_price">2500.00</field>
            <field name="categ_id" ref="product.product_category_all"/>
            <field name="description">Monthly bookkeeping and financial statement preparation</field>
            <field name="invoice_policy">order</field>
            <field name="service_tracking">task_global_project</field>
        </record>

        <!-- Demo Sales Orders -->
        
        <!-- Tech Innovations Annual Package -->
        <record id="sale_order_tech_innovations_annual" model="sale.order">
            <field name="name">SO001</field>
            <field name="partner_id" ref="partner_tech_innovations_pvt_ltd"/>
            <field name="date_order" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="validity_date" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="state">sale</field>
            <field name="project_id" ref="project_tech_innovations_annual"/>
        </record>

        <record id="sale_order_line_tech_audit" model="sale.order.line">
            <field name="order_id" ref="sale_order_tech_innovations_annual"/>
            <field name="product_id" ref="service_statutory_audit"/>
            <field name="product_uom_qty">1</field>
            <field name="price_unit">25000.00</field>
            <field name="task_id" ref="task_tech_innovations_audit_2024"/>
        </record>

        <record id="sale_order_line_tech_roc" model="sale.order.line">
            <field name="order_id" ref="sale_order_tech_innovations_annual"/>
            <field name="product_id" ref="service_roc_filing"/>
            <field name="product_uom_qty">1</field>
            <field name="price_unit">8000.00</field>
            <field name="task_id" ref="task_tech_innovations_roc_filing"/>
        </record>

        <!-- Tech Innovations Monthly Package -->
        <record id="sale_order_tech_innovations_monthly" model="sale.order">
            <field name="name">SO002</field>
            <field name="partner_id" ref="partner_tech_innovations_pvt_ltd"/>
            <field name="date_order" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="validity_date" eval="(DateTime.now() + timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="state">sale</field>
            <field name="project_id" ref="project_tech_innovations_monthly"/>
        </record>

        <record id="sale_order_line_tech_gst" model="sale.order.line">
            <field name="order_id" ref="sale_order_tech_innovations_monthly"/>
            <field name="product_id" ref="service_gst_return_monthly"/>
            <field name="product_uom_qty">12</field>
            <field name="price_unit">5000.00</field>
            <field name="task_id" ref="task_tech_innovations_gst_nov"/>
        </record>

        <record id="sale_order_line_tech_bookkeeping" model="sale.order.line">
            <field name="order_id" ref="sale_order_tech_innovations_monthly"/>
            <field name="product_id" ref="service_bookkeeping"/>
            <field name="product_uom_qty">12</field>
            <field name="price_unit">4000.00</field>
        </record>

        <!-- Manufacturing Corp Comprehensive Package -->
        <record id="sale_order_manufacturing_comprehensive" model="sale.order">
            <field name="name">SO003</field>
            <field name="partner_id" ref="partner_manufacturing_corp"/>
            <field name="date_order" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="validity_date" eval="(DateTime.now() + timedelta(days=40)).strftime('%Y-%m-%d')"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="state">sale</field>
            <field name="project_id" ref="project_manufacturing_comprehensive"/>
        </record>

        <record id="sale_order_line_mfg_gst" model="sale.order.line">
            <field name="order_id" ref="sale_order_manufacturing_comprehensive"/>
            <field name="product_id" ref="service_gst_return_monthly"/>
            <field name="product_uom_qty">12</field>
            <field name="price_unit">6000.00</field>
        </record>

        <record id="sale_order_line_mfg_tds" model="sale.order.line">
            <field name="order_id" ref="sale_order_manufacturing_comprehensive"/>
            <field name="product_id" ref="service_tds_return"/>
            <field name="product_uom_qty">4</field>
            <field name="price_unit">3000.00</field>
            <field name="task_id" ref="task_manufacturing_tds_q2"/>
        </record>

        <record id="sale_order_line_mfg_audit" model="sale.order.line">
            <field name="order_id" ref="sale_order_manufacturing_comprehensive"/>
            <field name="product_id" ref="service_statutory_audit"/>
            <field name="product_uom_qty">1</field>
            <field name="price_unit">35000.00</field>
        </record>

        <!-- Individual Client Orders -->
        <record id="sale_order_rajesh_sharma" model="sale.order">
            <field name="name">SO004</field>
            <field name="partner_id" ref="partner_rajesh_sharma"/>
            <field name="date_order" eval="(DateTime.now() - timedelta(days=10)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="validity_date" eval="(DateTime.now() + timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="state">sale</field>
            <field name="project_id" ref="project_rajesh_sharma_individual"/>
        </record>

        <record id="sale_order_line_rajesh_itr" model="sale.order.line">
            <field name="order_id" ref="sale_order_rajesh_sharma"/>
            <field name="product_id" ref="service_income_tax_return"/>
            <field name="product_uom_qty">1</field>
            <field name="price_unit">3000.00</field>
            <field name="task_id" ref="task_rajesh_sharma_itr"/>
        </record>

        <!-- Green Energy Startup Package -->
        <record id="sale_order_green_energy" model="sale.order">
            <field name="name">SO005</field>
            <field name="partner_id" ref="partner_green_energy_solutions"/>
            <field name="date_order" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d %H:%M:%S')"/>
            <field name="validity_date" eval="(DateTime.now() + timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="user_id" ref="base.user_admin"/>
            <field name="team_id" ref="sales_team.team_sales_department"/>
            <field name="state">sale</field>
            <field name="project_id" ref="project_green_energy_startup"/>
        </record>

        <record id="sale_order_line_green_gst_setup" model="sale.order.line">
            <field name="order_id" ref="sale_order_green_energy"/>
            <field name="product_id" ref="service_gst_return_monthly"/>
            <field name="product_uom_qty">6</field>
            <field name="price_unit">4500.00</field>
            <field name="task_id" ref="task_green_energy_first_gst_return"/>
        </record>

        <!-- Demo Invoices -->
        
        <!-- Confirmed Invoice for Tech Innovations -->
        <record id="invoice_tech_innovations_nov" model="account.move">
            <field name="move_type">out_invoice</field>
            <field name="partner_id" ref="partner_tech_innovations_pvt_ltd"/>
            <field name="invoice_date" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="invoice_date_due" eval="(DateTime.now() + timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="ref">INV/2024/001</field>
            <field name="state">posted</field>
        </record>

        <record id="invoice_line_tech_gst_nov" model="account.move.line">
            <field name="move_id" ref="invoice_tech_innovations_nov"/>
            <field name="product_id" ref="service_gst_return_monthly"/>
            <field name="name">GST Return Filing - November 2024</field>
            <field name="quantity">1</field>
            <field name="price_unit">5000.00</field>
            <field name="account_id" ref="account.a_sale"/>
        </record>

        <!-- Draft Invoice for Manufacturing Corp -->
        <record id="invoice_manufacturing_oct" model="account.move">
            <field name="move_type">out_invoice</field>
            <field name="partner_id" ref="partner_manufacturing_corp"/>
            <field name="invoice_date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="invoice_date_due" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="ref">INV/2024/002</field>
            <field name="state">draft</field>
        </record>

        <record id="invoice_line_mfg_gst_oct" model="account.move.line">
            <field name="move_id" ref="invoice_manufacturing_oct"/>
            <field name="product_id" ref="service_gst_return_monthly"/>
            <field name="name">GST Return Filing - October 2024</field>
            <field name="quantity">1</field>
            <field name="price_unit">6000.00</field>
            <field name="account_id" ref="account.a_sale"/>
        </record>

        <record id="invoice_line_mfg_tds_q2" model="account.move.line">
            <field name="move_id" ref="invoice_manufacturing_oct"/>
            <field name="product_id" ref="service_tds_return"/>
            <field name="name">TDS Return Filing - Q2 FY 2024-25</field>
            <field name="quantity">1</field>
            <field name="price_unit">3000.00</field>
            <field name="account_id" ref="account.a_sale"/>
        </record>

        <!-- Paid Invoice for Individual Client -->
        <record id="invoice_rajesh_sharma_itr" model="account.move">
            <field name="move_type">out_invoice</field>
            <field name="partner_id" ref="partner_rajesh_sharma"/>
            <field name="invoice_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="invoice_date_due" eval="(DateTime.now() - timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="ref">INV/2024/003</field>
            <field name="state">posted</field>
            <field name="payment_state">paid</field>
        </record>

        <record id="invoice_line_rajesh_itr" model="account.move.line">
            <field name="move_id" ref="invoice_rajesh_sharma_itr"/>
            <field name="product_id" ref="service_income_tax_return"/>
            <field name="name">Income Tax Return Filing FY 2023-24</field>
            <field name="quantity">1</field>
            <field name="price_unit">3000.00</field>
            <field name="account_id" ref="account.a_sale"/>
        </record>

    </data>
</odoo>
