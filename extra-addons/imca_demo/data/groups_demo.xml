<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Client Groups -->
        <record id="group_corporate_clients" model="x_groups">
            <field name="name">Corporate Clients</field>
        </record>

        <record id="group_individual_clients" model="x_groups">
            <field name="name">Individual Clients</field>
        </record>

        <record id="group_partnership_firms" model="x_groups">
            <field name="name">Partnership Firms</field>
        </record>

        <record id="group_startup_companies" model="x_groups">
            <field name="name">Startup Companies</field>
        </record>

        <record id="group_manufacturing_sector" model="x_groups">
            <field name="name">Manufacturing Sector</field>
        </record>

        <record id="group_service_sector" model="x_groups">
            <field name="name">Service Sector</field>
        </record>

        <record id="group_retail_trading" model="x_groups">
            <field name="name">Retail &amp; Trading</field>
        </record>

        <record id="group_high_value_clients" model="x_groups">
            <field name="name">High Value Clients</field>
        </record>

        <!-- Assign clients to groups -->
        <record id="imca_demo.partner_tech_innovations_pvt_ltd" model="res.partner">
            <field name="x_group_id" ref="group_corporate_clients"/>
        </record>

        <record id="imca_demo.partner_green_energy_solutions" model="res.partner">
            <field name="x_group_id" ref="group_startup_companies"/>
        </record>

        <record id="imca_demo.partner_retail_mart_enterprises" model="res.partner">
            <field name="x_group_id" ref="group_retail_trading"/>
        </record>

        <record id="imca_demo.partner_manufacturing_corp" model="res.partner">
            <field name="x_group_id" ref="group_manufacturing_sector"/>
        </record>

        <record id="imca_demo.partner_consulting_services" model="res.partner">
            <field name="x_group_id" ref="group_service_sector"/>
        </record>

        <record id="imca_demo.partner_rajesh_sharma" model="res.partner">
            <field name="x_group_id" ref="group_individual_clients"/>
        </record>

        <record id="imca_demo.partner_priya_patel" model="res.partner">
            <field name="x_group_id" ref="group_individual_clients"/>
        </record>

        <record id="imca_demo.partner_amit_kumar" model="res.partner">
            <field name="x_group_id" ref="group_individual_clients"/>
        </record>

        <record id="imca_demo.partner_sunita_agarwal" model="res.partner">
            <field name="x_group_id" ref="group_individual_clients"/>
        </record>

        <record id="imca_demo.partner_vikram_singh" model="res.partner">
            <field name="x_group_id" ref="group_individual_clients"/>
        </record>

        <record id="imca_demo.partner_sharma_associates" model="res.partner">
            <field name="x_group_id" ref="group_partnership_firms"/>
        </record>

        <record id="imca_demo.partner_patel_brothers" model="res.partner">
            <field name="x_group_id" ref="group_partnership_firms"/>
        </record>

    </data>
</odoo>
