<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Demo Tasks for CA Services -->

        <!-- Tech Innovations Tasks -->
        <record id="task_tech_innovations_gst_nov" model="project.task">
            <field name="name">GST Return Filing - November 2024</field>
            <field name="project_id" ref="project_tech_innovations_monthly"/>
            <field name="partner_id" ref="imca_demo.partner_tech_innovations_pvt_ltd"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File GSTR-1 and GSTR-3B for November 2024</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_tech_innovations_audit_2024" model="project.task">
            <field name="name">Annual Audit FY 2023-24</field>
            <field name="project_id" ref="project_tech_innovations_annual"/>
            <field name="partner_id" ref="imca_demo.partner_tech_innovations_pvt_ltd"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Conduct statutory audit for FY 2023-24 and prepare audit report</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>
        </record>

        <record id="task_tech_innovations_roc_filing" model="project.task">
            <field name="name">ROC Annual Filing FY 2023-24</field>
            <field name="project_id" ref="project_tech_innovations_annual"/>
            <field name="partner_id" ref="imca_demo.partner_tech_innovations_pvt_ltd"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File Form AOC-4 and MGT-7 with ROC</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>
        </record>

        <!-- Green Energy Tasks -->
        <record id="task_green_energy_gst_registration" model="project.task">
            <field name="name">GST Registration</field>
            <field name="project_id" ref="project_green_energy_startup"/>
            <field name="partner_id" ref="imca_demo.partner_green_energy_solutions"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Complete GST registration process for the company</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=7)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_green_energy_first_gst_return" model="project.task">
            <field name="name">First GST Return Filing</field>
            <field name="project_id" ref="project_green_energy_startup"/>
            <field name="partner_id" ref="imca_demo.partner_green_energy_solutions"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File first GST return after registration</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <!-- Retail Mart Tasks -->
        <record id="task_retail_mart_gst_oct" model="project.task">
            <field name="name">GST Return Filing - October 2024</field>
            <field name="project_id" ref="project_retail_mart_gst"/>
            <field name="partner_id" ref="imca_demo.partner_retail_mart_enterprises"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File GSTR-1 and GSTR-3B for October 2024</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_retail_mart_income_tax" model="project.task">
            <field name="name">Income Tax Return FY 2023-24</field>
            <field name="project_id" ref="project_retail_mart_gst"/>
            <field name="partner_id" ref="imca_demo.partner_retail_mart_enterprises"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Prepare and file income tax return for FY 2023-24</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=90)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <!-- Manufacturing Corp Tasks -->
        <record id="task_manufacturing_pf_oct" model="project.task">
            <field name="name">PF Return Filing - October 2024</field>
            <field name="project_id" ref="project_manufacturing_comprehensive"/>
            <field name="partner_id" ref="imca_demo.partner_manufacturing_corp"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File ECR and make PF payment for October 2024</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=10)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_manufacturing_esi_oct" model="project.task">
            <field name="name">ESI Return Filing - October 2024</field>
            <field name="project_id" ref="project_manufacturing_comprehensive"/>
            <field name="partner_id" ref="imca_demo.partner_manufacturing_corp"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File ESI return and make payment for October 2024</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=8)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_manufacturing_tds_q2" model="project.task">
            <field name="name">TDS Return Q2 FY 2024-25</field>
            <field name="project_id" ref="project_manufacturing_comprehensive"/>
            <field name="partner_id" ref="imca_demo.partner_manufacturing_corp"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File TDS return for Q2 FY 2024-25 (24Q)</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <!-- Individual Client Tasks -->
        <record id="task_rajesh_sharma_itr" model="project.task">
            <field name="name">Income Tax Return FY 2023-24</field>
            <field name="project_id" ref="project_rajesh_sharma_individual"/>
            <field name="partner_id" ref="imca_demo.partner_rajesh_sharma"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Prepare and file ITR-1 for FY 2023-24</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=75)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_priya_patel_itr" model="project.task">
            <field name="name">Income Tax Return FY 2023-24</field>
            <field name="project_id" ref="project_priya_patel_individual"/>
            <field name="partner_id" ref="imca_demo.partner_priya_patel"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Prepare and file ITR-2 for FY 2023-24 (capital gains)</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=80)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_amit_kumar_tax_planning" model="project.task">
            <field name="name">Tax Planning FY 2024-25</field>
            <field name="project_id" ref="project_amit_kumar_individual"/>
            <field name="partner_id" ref="imca_demo.partner_amit_kumar"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Review investment options and tax saving strategies</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <!-- Partnership Tasks -->
        <record id="task_sharma_associates_partnership_return" model="project.task">
            <field name="name">Partnership Return FY 2023-24</field>
            <field name="project_id" ref="project_sharma_associates_partnership"/>
            <field name="partner_id" ref="imca_demo.partner_sharma_associates"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Prepare and file ITR-5 for partnership firm</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=85)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_patel_brothers_gst_nov" model="project.task">
            <field name="name">GST Return Filing - November 2024</field>
            <field name="project_id" ref="project_patel_brothers_partnership"/>
            <field name="partner_id" ref="imca_demo.partner_patel_brothers"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">File GSTR-1 and GSTR-3B for November 2024</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=12)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <!-- Consulting Services Tasks -->
        <record id="task_consulting_llp_return" model="project.task">
            <field name="name">LLP Return FY 2023-24</field>
            <field name="project_id" ref="project_consulting_llp"/>
            <field name="partner_id" ref="imca_demo.partner_consulting_services"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Prepare and file Form 8 and Form 11 for LLP</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=70)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

        <record id="task_consulting_gst_compliance" model="project.task">
            <field name="name">GST Compliance Review</field>
            <field name="project_id" ref="project_consulting_llp"/>
            <field name="partner_id" ref="imca_demo.partner_consulting_services"/>
            <field name="user_ids" eval="[(6, 0, [ref('base.user_admin')])]"/>
            <field name="description">Review GST compliance and suggest improvements</field>
            <field name="date_deadline" eval="(DateTime.now() + timedelta(days=25)).strftime('%Y-%m-%d')"/>
            <field name="priority">0</field>

        </record>

    </data>
</odoo>
