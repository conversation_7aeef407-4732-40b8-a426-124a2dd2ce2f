<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Timesheets for CA Practice -->
        
        <!-- Timesheets for Tech Innovations Project -->
        <record id="timesheet_tech_gst_senior" model="account.analytic.line">
            <field name="name">GST Return Preparation - Tech Innovations</field>
            <field name="project_id" ref="project_tech_innovations_monthly"/>
            <field name="task_id" ref="task_tech_innovations_gst_nov"/>
            <field name="employee_id" ref="employee_ca_senior_associate1"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">4.5</field>
            <field name="amount">-4500.00</field>
        </record>

        <record id="timesheet_tech_gst_associate" model="account.analytic.line">
            <field name="name">GST Data Entry and Reconciliation</field>
            <field name="project_id" ref="project_tech_innovations_monthly"/>
            <field name="task_id" ref="task_tech_innovations_gst_nov"/>
            <field name="employee_id" ref="employee_ca_associate1"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">6.0</field>
            <field name="amount">-4800.00</field>
        </record>

        <record id="timesheet_tech_audit_partner" model="account.analytic.line">
            <field name="name">Audit Planning and Review</field>
            <field name="project_id" ref="project_tech_innovations_annual"/>
            <field name="task_id" ref="task_tech_innovations_audit_2024"/>
            <field name="employee_id" ref="employee_ca_partner"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">8.0</field>
            <field name="amount">-12000.00</field>
        </record>

        <record id="timesheet_tech_audit_senior" model="account.analytic.line">
            <field name="name">Audit Fieldwork - Fixed Assets</field>
            <field name="project_id" ref="project_tech_innovations_annual"/>
            <field name="task_id" ref="task_tech_innovations_audit_2024"/>
            <field name="employee_id" ref="employee_ca_senior_associate1"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">7.5</field>
            <field name="amount">-7500.00</field>
        </record>

        <!-- Timesheets for Manufacturing Corp Project -->
        <record id="timesheet_mfg_pf_associate" model="account.analytic.line">
            <field name="name">PF Return Filing - Manufacturing Corp</field>
            <field name="project_id" ref="project_manufacturing_comprehensive"/>
            <field name="task_id" ref="task_manufacturing_pf_oct"/>
            <field name="employee_id" ref="employee_ca_associate2"/>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="unit_amount">3.0</field>
            <field name="amount">-2400.00</field>
        </record>

        <record id="timesheet_mfg_esi_junior" model="account.analytic.line">
            <field name="name">ESI Return Preparation</field>
            <field name="project_id" ref="project_manufacturing_comprehensive"/>
            <field name="task_id" ref="task_manufacturing_esi_oct"/>
            <field name="employee_id" ref="employee_ca_junior1"/>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="unit_amount">2.5</field>
            <field name="amount">-1250.00</field>
        </record>

        <record id="timesheet_mfg_tds_senior" model="account.analytic.line">
            <field name="name">TDS Return Q2 - Data Analysis</field>
            <field name="project_id" ref="project_manufacturing_comprehensive"/>
            <field name="task_id" ref="task_manufacturing_tds_q2"/>
            <field name="employee_id" ref="employee_ca_senior_associate2"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">5.0</field>
            <field name="amount">-5000.00</field>
        </record>

        <!-- Timesheets for Individual Clients -->
        <record id="timesheet_rajesh_itr_associate" model="account.analytic.line">
            <field name="name">ITR Preparation - Rajesh Sharma</field>
            <field name="project_id" ref="project_rajesh_sharma_individual"/>
            <field name="task_id" ref="task_rajesh_sharma_itr"/>
            <field name="employee_id" ref="employee_ca_associate1"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">2.0</field>
            <field name="amount">-1600.00</field>
        </record>

        <record id="timesheet_priya_itr_senior" model="account.analytic.line">
            <field name="name">Capital Gains Calculation - Priya Patel</field>
            <field name="project_id" ref="project_priya_patel_individual"/>
            <field name="task_id" ref="task_priya_patel_itr"/>
            <field name="employee_id" ref="employee_ca_senior_associate1"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">3.5</field>
            <field name="amount">-3500.00</field>
        </record>

        <record id="timesheet_amit_tax_planning" model="account.analytic.line">
            <field name="name">Tax Planning Consultation - Amit Kumar</field>
            <field name="project_id" ref="project_amit_kumar_individual"/>
            <field name="task_id" ref="task_amit_kumar_tax_planning"/>
            <field name="employee_id" ref="employee_ca_partner"/>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="unit_amount">2.0</field>
            <field name="amount">-3000.00</field>
        </record>

        <!-- Timesheets for Partnership Projects -->
        <record id="timesheet_sharma_partnership_senior" model="account.analytic.line">
            <field name="name">Partnership Return Filing - Sharma Associates</field>
            <field name="project_id" ref="project_sharma_associates_partnership"/>
            <field name="task_id" ref="task_sharma_associates_partnership_return"/>
            <field name="employee_id" ref="employee_ca_senior_associate2"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">4.0</field>
            <field name="amount">-4000.00</field>
        </record>

        <record id="timesheet_patel_gst_associate" model="account.analytic.line">
            <field name="name">GST Return Filing - Patel Brothers</field>
            <field name="project_id" ref="project_patel_brothers_partnership"/>
            <field name="task_id" ref="task_patel_brothers_gst_nov"/>
            <field name="employee_id" ref="employee_ca_associate2"/>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="unit_amount">3.0</field>
            <field name="amount">-2400.00</field>
        </record>

        <!-- Timesheets for Consulting Services -->
        <record id="timesheet_consulting_llp_partner" model="account.analytic.line">
            <field name="name">LLP Return Filing - Strategic Consulting</field>
            <field name="project_id" ref="project_consulting_llp"/>
            <field name="task_id" ref="task_consulting_llp_return"/>
            <field name="employee_id" ref="employee_ca_partner"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">5.0</field>
            <field name="amount">-7500.00</field>
        </record>

        <record id="timesheet_consulting_compliance_senior" model="account.analytic.line">
            <field name="name">GST Compliance Review</field>
            <field name="project_id" ref="project_consulting_llp"/>
            <field name="task_id" ref="task_consulting_gst_compliance"/>
            <field name="employee_id" ref="employee_ca_senior_associate1"/>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="unit_amount">3.5</field>
            <field name="amount">-3500.00</field>
        </record>

        <!-- Administrative Timesheets -->
        <record id="timesheet_admin_training" model="account.analytic.line">
            <field name="name">Team Training - New Tax Updates</field>
            <field name="employee_id" ref="employee_ca_senior_partner"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">4.0</field>
            <field name="amount">-8000.00</field>
        </record>

        <record id="timesheet_admin_client_meeting" model="account.analytic.line">
            <field name="name">Client Acquisition Meeting</field>
            <field name="employee_id" ref="employee_ca_partner"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">2.5</field>
            <field name="amount">-3750.00</field>
        </record>

        <!-- Junior Staff Learning Timesheets -->
        <record id="timesheet_junior_learning1" model="account.analytic.line">
            <field name="name">GST Software Training</field>
            <field name="employee_id" ref="employee_ca_junior1"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">3.0</field>
            <field name="amount">-1500.00</field>
        </record>

        <record id="timesheet_junior_learning2" model="account.analytic.line">
            <field name="name">Audit Procedures Study</field>
            <field name="employee_id" ref="employee_ca_junior2"/>
            <field name="date" eval="(DateTime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="unit_amount">2.5</field>
            <field name="amount">-1250.00</field>
        </record>

        <!-- Intern Timesheets -->
        <record id="timesheet_intern_data_entry" model="account.analytic.line">
            <field name="name">Data Entry Support - Various Clients</field>
            <field name="employee_id" ref="employee_intern1"/>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="unit_amount">6.0</field>
            <field name="amount">-900.00</field>
        </record>

        <record id="timesheet_intern_filing_support" model="account.analytic.line">
            <field name="name">Document Filing and Organization</field>
            <field name="employee_id" ref="employee_intern2"/>
            <field name="date" eval="DateTime.now().strftime('%Y-%m-%d')"/>
            <field name="unit_amount">5.5</field>
            <field name="amount">-825.00</field>
        </record>

    </data>
</odoo>
