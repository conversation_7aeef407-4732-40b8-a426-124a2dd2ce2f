<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- DSC Demo Data -->
        <record id="dsc_tech_innovations_director" model="x_dsc">
            <field name="name">Tech Innovations Director DSC</field>
            <field name="x_client" ref="imca_demo.partner_tech_innovations_pvt_ltd"/>
            <field name="x_manager" ref="imca_demo.partner_tech_innovations_pvt_ltd"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=730)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Office</field>
            <field name="x_notes">DSC for company director - used for ROC filings and income tax returns</field>
        </record>

        <record id="dsc_green_energy_ceo" model="x_dsc">
            <field name="name">Green Energy CEO DSC</field>
            <field name="x_client" ref="imca_demo.partner_green_energy_solutions"/>
            <field name="x_manager" ref="imca_demo.partner_green_energy_solutions"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=365)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Client</field>
            <field name="x_notes">CEO DSC for statutory filings and GST returns</field>
        </record>

        <record id="dsc_retail_mart_proprietor" model="x_dsc">
            <field name="name">Retail Mart Proprietor DSC</field>
            <field name="x_client" ref="imca_demo.partner_retail_mart_enterprises"/>
            <field name="x_manager" ref="imca_demo.partner_retail_mart_enterprises"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=500)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Office</field>
            <field name="x_notes">Proprietor DSC for GST and income tax filings</field>
        </record>

        <record id="dsc_manufacturing_md" model="x_dsc">
            <field name="name">Manufacturing Corp MD DSC</field>
            <field name="x_client" ref="imca_demo.partner_manufacturing_corp"/>
            <field name="x_manager" ref="imca_demo.partner_manufacturing_corp"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=600)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Office</field>
            <field name="x_notes">Managing Director DSC for audit reports and ROC filings</field>
        </record>

        <record id="dsc_consulting_partner" model="x_dsc">
            <field name="name">Consulting Services Partner DSC</field>
            <field name="x_client" ref="imca_demo.partner_consulting_services"/>
            <field name="x_manager" ref="imca_demo.partner_consulting_services"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=450)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Client</field>
            <field name="x_notes">Designated Partner DSC for LLP filings</field>
        </record>

        <record id="dsc_rajesh_sharma_individual" model="x_dsc">
            <field name="name">Rajesh Sharma Individual DSC</field>
            <field name="x_client" ref="imca_demo.partner_rajesh_sharma"/>
            <field name="x_manager" ref="imca_demo.partner_rajesh_sharma"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=300)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Client</field>
            <field name="x_notes">Individual DSC for income tax return filing</field>
        </record>

        <record id="dsc_priya_patel_individual" model="x_dsc">
            <field name="name">Priya Patel Individual DSC</field>
            <field name="x_client" ref="imca_demo.partner_priya_patel"/>
            <field name="x_manager" ref="imca_demo.partner_priya_patel"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=180)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Office</field>
            <field name="x_notes">Individual DSC - expiring soon, renewal required</field>
        </record>

        <record id="dsc_sharma_associates_partner1" model="x_dsc">
            <field name="name">Sharma Associates Partner DSC</field>
            <field name="x_client" ref="imca_demo.partner_sharma_associates"/>
            <field name="x_manager" ref="imca_demo.partner_sharma_associates"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=800)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Office</field>
            <field name="x_notes">Partnership firm partner DSC for tax filings</field>
        </record>

        <record id="dsc_patel_brothers_partner" model="x_dsc">
            <field name="name">Patel Brothers Partner DSC</field>
            <field name="x_client" ref="imca_demo.partner_patel_brothers"/>
            <field name="x_manager" ref="imca_demo.partner_patel_brothers"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=90)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Client</field>
            <field name="x_notes">URGENT: DSC expiring in 3 months - renewal process to be initiated</field>
        </record>

        <record id="dsc_amit_kumar_individual" model="x_dsc">
            <field name="name">Amit Kumar Individual DSC</field>
            <field name="x_client" ref="imca_demo.partner_amit_kumar"/>
            <field name="x_manager" ref="imca_demo.partner_amit_kumar"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=400)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Office</field>
            <field name="x_notes">Individual DSC for professional tax and income tax filings</field>
        </record>

        <record id="dsc_sunita_agarwal_individual" model="x_dsc">
            <field name="name">Sunita Agarwal Individual DSC</field>
            <field name="x_client" ref="imca_demo.partner_sunita_agarwal"/>
            <field name="x_manager" ref="imca_demo.partner_sunita_agarwal"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=250)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Client</field>
            <field name="x_notes">Individual DSC for business income tax returns</field>
        </record>

        <record id="dsc_vikram_singh_individual" model="x_dsc">
            <field name="name">Vikram Singh Individual DSC</field>
            <field name="x_client" ref="imca_demo.partner_vikram_singh"/>
            <field name="x_manager" ref="imca_demo.partner_vikram_singh"/>
            <field name="x_expiry_date" eval="(DateTime.now() + timedelta(days=150)).strftime('%Y-%m-%d')"/>
            <field name="x_location">Office</field>
            <field name="x_notes">Individual DSC - renewal reminder to be sent soon</field>
        </record>

    </data>
</odoo>
