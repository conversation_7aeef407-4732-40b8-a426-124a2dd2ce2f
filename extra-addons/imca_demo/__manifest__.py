{
    'name': 'IMCA Demo Data - CA Backoffice Management',
    'version': '********.0',
    'category': 'Accounting/Accounting',
    'summary': 'Comprehensive Demo Data for IMCA CA Practice Management System',
    'description': '''
        IMCA Demo Data Module
        ====================

        This module provides comprehensive demo data for the IMCA (Integrated Management for Chartered Accountants) system.

        Demo Data Includes:
        * Sample CA Practice Clients (Companies, Individuals, Partnerships)
        * Client Groups and Categories
        * Service Portfolio (Tax, Audit, Compliance, Advisory)
        * DSC (Digital Signature Certificate) Records
        * Client Portal Credentials
        * Document Management Examples
        * Project and Task Templates
        * Compliance Calendar Entries
        * Financial Records and Fee Structures

        Perfect for:
        * Product Demonstrations
        * Training Sessions
        * System Testing
        * Client Presentations

        This demo showcases a complete CA practice management solution without revealing
        that it's built on Odoo - presenting it as a standalone IMCA software solution.
    ''',
    'author': 'IMCA Solutions',
    'website': 'https://imca.arihantai.com',
    'depends': [
        'base',
        'mail',
        'project',
        'project_account',
        'sale_project',
        'project_hr_expense',
        'project_purchase',
        'project_timesheet_holidays',
        'website_form_project',
        'account',
        'sale',
        'contacts',
        'imca_groups',
        'imca_services',
        'imca_dsc_management',
        'imca_crednetials_manager',
        'imca_client_documents'
    ],
    'data': [
        'data/res_partner_demo.xml',
        'data/groups_demo.xml',
        'data/services_demo.xml',
        'data/dsc_demo.xml',
        'data/credentials_demo.xml',
        'data/projects_demo.xml',
        'data/tasks_demo.xml',
        'data/hr_employees_demo.xml',
        'data/timesheets_demo.xml',
        'data/expenses_demo.xml',
        'data/calendar_demo.xml',
        'data/menu_customization.xml',
    ],
    'demo': [
        'data/res_partner_demo.xml',
        'data/groups_demo.xml',
        'data/services_demo.xml',
        'data/dsc_demo.xml',
        'data/credentials_demo.xml',
        'data/projects_demo.xml',
        'data/tasks_demo.xml',
        'data/hr_employees_demo.xml',
        'data/timesheets_demo.xml',
        'data/expenses_demo.xml',
        'data/calendar_demo.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
    'sequence': 100,
}
