#!/usr/bin/env python3
"""
Test script to verify module upgrade readiness
"""

import os
import xml.etree.ElementTree as ET
import ast

def test_module_structure():
    """Test basic module structure"""
    print("🔍 Testing Module Structure:")
    print("=" * 40)
    
    required_files = [
        '__manifest__.py',
        '__init__.py',
        'models/__init__.py',
        'views/whatsapp_menu.xml',
        'security/ir.model.access.csv'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_manifest_file():
    """Test manifest file validity"""
    print("\n📋 Testing Manifest File:")
    print("=" * 40)
    
    try:
        with open('__manifest__.py', 'r') as f:
            manifest_content = f.read()
        
        # Test Python syntax
        ast.parse(manifest_content)
        print("✅ Manifest syntax is valid")
        
        # Execute manifest to check structure
        manifest_dict = {}
        exec(manifest_content, {}, manifest_dict)
        
        required_keys = ['name', 'version', 'depends', 'data']
        for key in required_keys:
            if key in manifest_dict:
                print(f"✅ {key}: {manifest_dict[key] if key != 'data' else f'{len(manifest_dict[key])} files'}")
            else:
                print(f"❌ Missing required key: {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Manifest error: {e}")
        return False

def test_xml_files():
    """Test all XML files for validity"""
    print("\n📄 Testing XML Files:")
    print("=" * 40)
    
    xml_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.xml'):
                xml_files.append(os.path.join(root, file))
    
    valid_files = 0
    for xml_file in xml_files:
        try:
            ET.parse(xml_file)
            print(f"✅ {xml_file}")
            valid_files += 1
        except Exception as e:
            print(f"❌ {xml_file}: {e}")
    
    print(f"\n📊 XML Files: {valid_files}/{len(xml_files)} valid")
    return valid_files == len(xml_files)

def test_python_files():
    """Test all Python files for syntax"""
    print("\n🐍 Testing Python Files:")
    print("=" * 40)
    
    python_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    valid_files = 0
    for py_file in python_files:
        try:
            with open(py_file, 'r') as f:
                ast.parse(f.read())
            print(f"✅ {py_file}")
            valid_files += 1
        except Exception as e:
            print(f"❌ {py_file}: {e}")
    
    print(f"\n📊 Python Files: {valid_files}/{len(python_files)} valid")
    return valid_files == len(python_files)

def test_data_files():
    """Test data file references in manifest"""
    print("\n📁 Testing Data File References:")
    print("=" * 40)
    
    try:
        with open('__manifest__.py', 'r') as f:
            manifest_content = f.read()
        
        manifest_dict = {}
        exec(manifest_content, {}, manifest_dict)
        
        data_files = manifest_dict.get('data', [])
        missing_files = []
        
        for data_file in data_files:
            if os.path.exists(data_file):
                print(f"✅ {data_file}")
            else:
                print(f"❌ {data_file} - MISSING")
                missing_files.append(data_file)
        
        print(f"\n📊 Data Files: {len(data_files) - len(missing_files)}/{len(data_files)} found")
        return len(missing_files) == 0
        
    except Exception as e:
        print(f"❌ Error checking data files: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 WhatsApp Conversations Module Upgrade Test")
    print("=" * 60)
    
    tests = [
        ("Module Structure", test_module_structure),
        ("Manifest File", test_manifest_file),
        ("XML Files", test_xml_files),
        ("Python Files", test_python_files),
        ("Data Files", test_data_files),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED - Module is ready for upgrade!")
        print("\n📝 Next Steps:")
        print("1. Go to Odoo → Apps")
        print("2. Search for 'WhatsApp Conversations'")
        print("3. Click 'Upgrade' button")
        print("4. Wait for upgrade to complete")
        return True
    else:
        print("⚠️  SOME TESTS FAILED - Fix issues before upgrading")
        print("\n📝 Troubleshooting:")
        print("1. Check the failed tests above")
        print("2. Fix any missing files or syntax errors")
        print("3. Run this test again")
        print("4. Only upgrade when all tests pass")
        return False

if __name__ == "__main__":
    os.chdir('/mnt/extra-addons/whatsapp_conversations')
    main()
