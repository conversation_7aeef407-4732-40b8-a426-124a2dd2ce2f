#!/usr/bin/env python3
"""
Fix Odoo Authentication Issues
This script helps diagnose and fix authentication problems with Odoo XMLRPC
"""

import xmlrpc.client
import sys
import getpass

# Configuration
ODOO_URL = "http://localhost:8069"
ODOO_DB = "arihantai.com"

def test_connection():
    """Test basic connection to Odoo"""
    print("🔍 Testing Odoo Connection...")
    try:
        common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
        version = common.version()
        print(f"✅ Connected to Odoo {version['server_version']}")
        return common
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return None

def list_databases():
    """List available databases"""
    print("🔍 Checking database availability...")
    try:
        # In Odoo 17, we'll just try to authenticate to check if DB exists
        common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
        # Try a simple authentication to see if database exists
        try:
            common.authenticate(ODOO_DB, "dummy", "dummy", {})
            print(f"✅ Database '{ODOO_DB}' is accessible")
            return [ODOO_DB]
        except Exception as auth_e:
            if "database" in str(auth_e).lower():
                print(f"❌ Database '{ODOO_DB}' not found")
                return []
            else:
                print(f"✅ Database '{ODOO_DB}' exists (authentication failed as expected)")
                return [ODOO_DB]
    except Exception as e:
        print(f"❌ Failed to check database: {e}")
        return []

def test_authentication(username, password):
    """Test authentication with given credentials"""
    print(f"🔍 Testing authentication for user: {username}")
    try:
        common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
        uid = common.authenticate(ODOO_DB, username, password, {})
        if uid:
            print(f"✅ Authentication successful! User ID: {uid}")
            return uid
        else:
            print("❌ Authentication failed - Invalid credentials")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def list_users():
    """List users in the database (requires admin access)"""
    print("🔍 Attempting to list users...")
    try:
        # Try with common admin credentials
        admin_passwords = ["admin", "password", "123456", ""]
        
        for password in admin_passwords:
            try:
                common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
                uid = common.authenticate(ODOO_DB, "admin", password, {})
                if uid:
                    print(f"✅ Admin access with password: {'[empty]' if not password else '[hidden]'}")
                    
                    models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
                    users = models.execute_kw(ODOO_DB, uid, password,
                        'res.users', 'search_read',
                        [[]],
                        {'fields': ['login', 'name', 'active'], 'limit': 10})
                    
                    print("📋 Available users:")
                    for user in users:
                        status = "✅" if user['active'] else "❌"
                        print(f"   {status} {user['login']} ({user['name']})")
                    
                    return users
            except:
                continue
        
        print("❌ Could not access user list with admin credentials")
        return []
        
    except Exception as e:
        print(f"❌ Error listing users: {e}")
        return []

def reset_user_password(username):
    """Reset user password (requires admin access)"""
    print(f"🔧 Attempting to reset password for: {username}")
    
    # Try to get admin access
    admin_passwords = ["admin", "password", "123456", ""]
    
    for admin_password in admin_passwords:
        try:
            common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
            admin_uid = common.authenticate(ODOO_DB, "admin", admin_password, {})
            if admin_uid:
                print(f"✅ Got admin access")
                
                models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
                
                # Find the user
                user_ids = models.execute_kw(ODOO_DB, admin_uid, admin_password,
                    'res.users', 'search', [[['login', '=', username]]])
                
                if not user_ids:
                    print(f"❌ User {username} not found")
                    return False
                
                # Set new password
                new_password = "admin123"  # Simple password for testing
                models.execute_kw(ODOO_DB, admin_uid, admin_password,
                    'res.users', 'write', [user_ids, {'password': new_password}])
                
                print(f"✅ Password reset for {username} to: {new_password}")
                
                # Test the new password
                test_uid = common.authenticate(ODOO_DB, username, new_password, {})
                if test_uid:
                    print(f"✅ New password works! User ID: {test_uid}")
                    return True
                else:
                    print("❌ New password doesn't work")
                    return False
                    
        except Exception as e:
            continue
    
    print("❌ Could not reset password - no admin access")
    return False

def create_test_user():
    """Create a test user for integration testing"""
    print("🔧 Creating test user for integration...")
    
    # Try to get admin access
    admin_passwords = ["admin", "password", "123456", ""]
    
    for admin_password in admin_passwords:
        try:
            common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
            admin_uid = common.authenticate(ODOO_DB, "admin", admin_password, {})
            if admin_uid:
                print(f"✅ Got admin access")
                
                models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
                
                # Check if test user already exists
                existing = models.execute_kw(ODOO_DB, admin_uid, admin_password,
                    'res.users', 'search', [[['login', '=', 'whatsapp_test']]])
                
                if existing:
                    print("✅ Test user already exists")
                    # Test authentication
                    test_uid = common.authenticate(ODOO_DB, "whatsapp_test", "test123", {})
                    if test_uid:
                        print(f"✅ Test user authentication works! User ID: {test_uid}")
                        return True
                    else:
                        # Reset password
                        models.execute_kw(ODOO_DB, admin_uid, admin_password,
                            'res.users', 'write', [existing, {'password': 'test123'}])
                        print("✅ Test user password reset")
                        return True
                
                # Create new test user
                user_data = {
                    'name': 'WhatsApp Integration Test User',
                    'login': 'whatsapp_test',
                    'password': 'test123',
                    'groups_id': [(6, 0, [1])]  # Base user group
                }
                
                user_id = models.execute_kw(ODOO_DB, admin_uid, admin_password,
                    'res.users', 'create', [user_data])
                
                print(f"✅ Created test user with ID: {user_id}")
                
                # Test the new user
                test_uid = common.authenticate(ODOO_DB, "whatsapp_test", "test123", {})
                if test_uid:
                    print(f"✅ Test user authentication works! User ID: {test_uid}")
                    return True
                else:
                    print("❌ Test user authentication failed")
                    return False
                    
        except Exception as e:
            print(f"Error with admin password: {e}")
            continue
    
    print("❌ Could not create test user - no admin access")
    return False

def main():
    """Main function"""
    print("🚀 Odoo Authentication Fix Tool")
    print("=" * 50)
    
    # Test basic connection
    if not test_connection():
        print("❌ Cannot connect to Odoo. Check if Odoo is running.")
        return 1
    
    # List databases
    databases = list_databases()
    if ODOO_DB not in databases:
        print(f"❌ Database '{ODOO_DB}' not found in available databases")
        return 1
    
    # List users
    users = list_users()
    
    # Try to create/fix test user
    if create_test_user():
        print("\n✅ Test user is ready!")
        print("Use these credentials for integration testing:")
        print("Username: whatsapp_test")
        print("Password: test123")
    
    # Interactive mode
    print("\n" + "=" * 50)
    print("Interactive Authentication Test")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Test authentication with custom credentials")
        print("2. Reset password for existing user")
        print("3. Exit")
        
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == "1":
            username = input("Username: ").strip()
            password = getpass.getpass("Password: ")
            test_authentication(username, password)
            
        elif choice == "2":
            username = input("Username to reset: ").strip()
            reset_user_password(username)
            
        elif choice == "3":
            break
        else:
            print("Invalid choice")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
