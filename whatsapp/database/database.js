const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class DatabaseManager {
    constructor() {
        this.dbPath = path.join(__dirname, 'whatsapp_platform.db');
        this.db = null;
        this.init();
    }

    init() {
        // Create database directory if it doesn't exist
        const dbDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }

        // Initialize database
        this.db = new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('Error opening database:', err);
            } else {
                console.log('✅ Database connected successfully');
                this.createTables();
            }
        });
    }

    createTables() {
        const schemaPath = path.join(__dirname, 'schema.sql');
        if (fs.existsSync(schemaPath)) {
            const schema = fs.readFileSync(schemaPath, 'utf8');
            this.db.exec(schema, (err) => {
                if (err) {
                    console.error('Error creating tables:', err);
                } else {
                    console.log('✅ Database tables created/verified successfully');
                }
            });
        }
    }

    // User Management
    async createUser(userData) {
        const { mobileNumber, password, fullName, email, role = 'user', corporateId = null } = userData;
        const passwordHash = crypto.createHash('md5').update(password).digest('hex');
        
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO users (mobile_number, password_hash, full_name, email, role, corporate_id)
                VALUES (?, ?, ?, ?, ?, ?)
            `;
            this.db.run(sql, [mobileNumber, passwordHash, fullName, email, role, corporateId], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, mobileNumber, fullName, role, status: 'pending' });
                }
            });
        });
    }

    async authenticateUser(mobileNumber, password) {
        const passwordHash = crypto.createHash('md5').update(password).digest('hex');
        
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT u.*, c.name as corporate_name 
                FROM users u 
                LEFT JOIN corporates c ON u.corporate_id = c.id 
                WHERE u.mobile_number = ? AND u.password_hash = ? AND u.status = 'approved'
            `;
            this.db.get(sql, [mobileNumber, passwordHash], (err, row) => {
                if (err) {
                    reject(err);
                } else if (row) {
                    // Update last login
                    this.db.run('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [row.id]);
                    resolve(row);
                } else {
                    resolve(null);
                }
            });
        });
    }

    async approveUser(userId, adminId) {
        return new Promise((resolve, reject) => {
            const sql = `
                UPDATE users 
                SET status = 'approved', approved_by = ?, approved_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            `;
            this.db.run(sql, [adminId, userId], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, changes: this.changes });
                }
            });
        });
    }

    async getPendingUsers() {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT u.*, c.name as corporate_name 
                FROM users u 
                LEFT JOIN corporates c ON u.corporate_id = c.id 
                WHERE u.status = 'pending' 
                ORDER BY u.created_at DESC
            `;
            this.db.all(sql, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // WhatsApp Instance Management
    async createInstance(sessionId, userId, phoneNumber, corporateId = null) {
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO whatsapp_instances (session_id, user_id, phone_number, corporate_id)
                VALUES (?, ?, ?, ?)
            `;
            this.db.run(sql, [sessionId, userId, phoneNumber, corporateId], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ 
                        id: this.lastID, 
                        sessionId, 
                        userId, 
                        phoneNumber, 
                        corporateId,
                        status: 'initializing' 
                    });
                }
            });
        });
    }

    async getUserInstances(userId, corporateId = null) {
        return new Promise((resolve, reject) => {
            let sql, params;
            
            if (corporateId) {
                // Corporate user - get all instances in the corporate
                sql = `
                    SELECT * FROM whatsapp_instances 
                    WHERE corporate_id = ? 
                    ORDER BY created_at DESC
                `;
                params = [corporateId];
            } else {
                // Individual user - get only their instances
                sql = `
                    SELECT * FROM whatsapp_instances 
                    WHERE user_id = ? AND corporate_id IS NULL 
                    ORDER BY created_at DESC
                `;
                params = [userId];
            }
            
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async updateInstanceStatus(sessionId, status, hasQR = null) {
        return new Promise((resolve, reject) => {
            let sql = 'UPDATE whatsapp_instances SET status = ?, updated_at = CURRENT_TIMESTAMP';
            let params = [status];

            if (hasQR !== null) {
                sql += ', has_qr = ?';
                params.push(hasQR);
            }

            sql += ' WHERE session_id = ?';
            params.push(sessionId);

            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, changes: this.changes });
                }
            });
        });
    }

    async getAllInstances() {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT wi.*, u.mobile_number, u.full_name
                FROM whatsapp_instances wi
                LEFT JOIN users u ON wi.user_id = u.id
                ORDER BY wi.created_at DESC
            `;
            this.db.all(sql, [], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows || []);
                }
            });
        });
    }

    async getInstancesByUser(userId) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM whatsapp_instances
                WHERE user_id = ?
                ORDER BY created_at DESC
            `;
            this.db.all(sql, [userId], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows || []);
                }
            });
        });
    }

    async getInstanceByCorporate(corporateId) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM whatsapp_instances
                WHERE corporate_id = ?
                ORDER BY created_at DESC
            `;
            this.db.all(sql, [corporateId], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows || []);
                }
            });
        });
    }

    async deleteInstance(sessionId) {
        return new Promise((resolve, reject) => {
            // Simple approach: just delete the instance, related data will be cleaned up by foreign key constraints
            const sql = 'DELETE FROM whatsapp_instances WHERE session_id = ?';

            this.db.run(sql, [sessionId], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        success: true,
                        changes: this.changes,
                        message: 'Instance deleted successfully'
                    });
                }
            });
        });
    }

    // Message Management with Instance Isolation
    async saveMessage(instanceId, messageData) {
        const { messageId, fromNumber, toNumber, messageText, messageType, isFromMe, status, timestamp } = messageData;
        
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT OR REPLACE INTO whatsapp_messages 
                (instance_id, message_id, from_number, to_number, message_text, message_type, is_from_me, status, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
            this.db.run(sql, [instanceId, messageId, fromNumber, toNumber, messageText, messageType, isFromMe, status, timestamp], function(err) {
                if (err) {
                    reject(err);
                } else {
                    // Update instance message count and last activity
                    const updateSql = `
                        UPDATE whatsapp_instances 
                        SET message_count = message_count + 1, last_activity = CURRENT_TIMESTAMP 
                        WHERE id = ?
                    `;
                    resolve({ id: this.lastID, messageId });
                }
            });
        });
    }

    async getInstanceMessages(instanceId, limit = 50) {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM whatsapp_messages 
                WHERE instance_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            `;
            this.db.all(sql, [instanceId, limit], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // Knowledge Base Management
    async saveKnowledgeFile(instanceId, filePath, fileName, fileType, content) {
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT OR REPLACE INTO knowledge_files 
                (instance_id, file_path, file_name, file_type, content, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `;
            this.db.run(sql, [instanceId, filePath, fileName, fileType, content], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, filePath, fileName });
                }
            });
        });
    }

    async getKnowledgeFiles(instanceId, pathPrefix = '') {
        return new Promise((resolve, reject) => {
            const sql = `
                SELECT * FROM knowledge_files 
                WHERE instance_id = ? AND file_path LIKE ? 
                ORDER BY file_name
            `;
            this.db.all(sql, [instanceId, `${pathPrefix}%`], (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async deleteKnowledgeFile(instanceId, filePath) {
        return new Promise((resolve, reject) => {
            const sql = 'DELETE FROM knowledge_files WHERE instance_id = ? AND file_path = ?';
            this.db.run(sql, [instanceId, filePath], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, changes: this.changes });
                }
            });
        });
    }

    // Chatbot Configuration
    async saveChatbotConfig(instanceId, config) {
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT OR REPLACE INTO chatbot_configs 
                (instance_id, enabled, model, api_key, system_prompt, temperature, max_tokens, use_knowledge_base, auto_reply, response_delay, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `;
            const params = [
                instanceId, config.enabled, config.model, config.apiKey, config.systemPrompt,
                config.temperature, config.maxTokens, config.knowledgeBase, config.autoReply, config.responseDelay
            ];
            
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ success: true, id: this.lastID });
                }
            });
        });
    }

    async getChatbotConfig(instanceId) {
        return new Promise((resolve, reject) => {
            const sql = 'SELECT * FROM chatbot_configs WHERE instance_id = ?';
            this.db.get(sql, [instanceId], (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    // Session Management
    async createSession(userId, tokenHash, expiresAt, ipAddress, userAgent) {
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            `;
            this.db.run(sql, [userId, tokenHash, expiresAt, ipAddress, userAgent], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, tokenHash });
                }
            });
        });
    }

    async validateSession(tokenHash) {
        return new Promise((resolve, reject) => {
            const currentTime = Date.now();
            const sql = `
                SELECT s.*, u.mobile_number, u.full_name, u.role, u.corporate_id
                FROM user_sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.token_hash = ? AND s.expires_at > ? AND u.status = 'approved'
            `;
            this.db.get(sql, [tokenHash, currentTime], (err, row) => {
                if (err) {
                    reject(err);
                } else if (row) {
                    // Update last used
                    this.db.run('UPDATE user_sessions SET last_used = ? WHERE id = ?', [currentTime, row.id]);
                    resolve(row);
                } else {
                    resolve(null);
                }
            });
        });
    }

    // Audit Logging
    async logAction(userId, action, resourceType, resourceId, details, ipAddress) {
        return new Promise((resolve, reject) => {
            const sql = `
                INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details, ip_address)
                VALUES (?, ?, ?, ?, ?, ?)
            `;
            this.db.run(sql, [userId, action, resourceType, resourceId, JSON.stringify(details), ipAddress], function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID });
                }
            });
        });
    }

    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }
}

module.exports = DatabaseManager;
