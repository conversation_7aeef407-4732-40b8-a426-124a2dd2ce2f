-- WhatsApp Automation Platform Database Schema
-- Created for data persistence, user management, and instance isolation

-- Users table for authentication and authorization
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mobile_number VARCHAR(15) UNIQUE NOT NULL, -- Username is mobile number
    password_hash VARCHAR(255) NOT NULL, -- MD5/JWT hash
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'suspended')),
    corporate_id INTEGER NULL, -- For corporate accounts
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    approved_by INTEGER NULL, -- Admin who approved the user
    approved_at TIMESTAMP NULL,
    FOREI<PERSON><PERSON> KEY (approved_by) REFERENCES users(id),
    <PERSON>OR<PERSON><PERSON><PERSON>EY (corporate_id) REFERENCES corporates(id)
);

-- Corporate accounts table
CREATE TABLE IF NOT EXISTS corporates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    admin_user_id INTEGER NOT NULL, -- Corporate admin
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_user_id) REFERENCES users(id)
);

-- WhatsApp instances table
CREATE TABLE IF NOT EXISTS whatsapp_instances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    corporate_id INTEGER NULL, -- NULL for individual instances
    phone_number VARCHAR(15) NOT NULL,
    status TEXT DEFAULT 'initializing' CHECK (status IN ('initializing', 'qr_ready', 'connected', 'disconnected', 'error')),
    has_qr BOOLEAN DEFAULT FALSE,
    qr_code_path VARCHAR(255) NULL,
    message_count INTEGER DEFAULT 0,
    last_activity TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (corporate_id) REFERENCES corporates(id)
);

-- WhatsApp messages table for conversation history
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id INTEGER NOT NULL,
    message_id VARCHAR(100) UNIQUE NOT NULL, -- WhatsApp message ID
    from_number VARCHAR(15) NOT NULL,
    to_number VARCHAR(15) NOT NULL,
    message_text TEXT,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'audio', 'document', 'location', 'contact')),
    media_path VARCHAR(255) NULL, -- For media files
    is_from_me BOOLEAN NOT NULL,
    status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (instance_id) REFERENCES whatsapp_instances(id)
);

-- Contacts table for each instance
CREATE TABLE IF NOT EXISTS contacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id INTEGER NOT NULL,
    phone_number VARCHAR(15) NOT NULL,
    name VARCHAR(100),
    avatar_path VARCHAR(255) NULL,
    is_blocked BOOLEAN DEFAULT FALSE,
    last_message_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instance_id, phone_number),
    FOREIGN KEY (instance_id) REFERENCES whatsapp_instances(id)
);

-- Knowledge base files table
CREATE TABLE IF NOT EXISTS knowledge_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id INTEGER NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER DEFAULT 0,
    content TEXT, -- For text files
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instance_id, file_path),
    FOREIGN KEY (instance_id) REFERENCES whatsapp_instances(id)
);

-- Chatbot configurations table
CREATE TABLE IF NOT EXISTS chatbot_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id INTEGER NOT NULL,
    enabled BOOLEAN DEFAULT FALSE,
    model VARCHAR(100) DEFAULT 'meta-llama/llama-3.1-70b-versatile',
    api_key VARCHAR(255),
    system_prompt TEXT,
    temperature DECIMAL(3,2) DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 500,
    use_knowledge_base BOOLEAN DEFAULT TRUE,
    auto_reply BOOLEAN DEFAULT TRUE,
    response_delay INTEGER DEFAULT 2, -- seconds
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instance_id),
    FOREIGN KEY (instance_id) REFERENCES whatsapp_instances(id)
);

-- Tools configurations table
CREATE TABLE IF NOT EXISTS tool_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id INTEGER NOT NULL,
    tool_name VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT FALSE,
    config_data JSON, -- Tool-specific configuration
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instance_id, tool_name),
    FOREIGN KEY (instance_id) REFERENCES whatsapp_instances(id)
);

-- Scheduled messages table
CREATE TABLE IF NOT EXISTS scheduled_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id INTEGER NOT NULL,
    to_number VARCHAR(15) NOT NULL,
    message_text TEXT NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    FOREIGN KEY (instance_id) REFERENCES whatsapp_instances(id)
);

-- Message forwarding rules table
CREATE TABLE IF NOT EXISTS forwarding_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    instance_id INTEGER NOT NULL,
    from_number VARCHAR(15), -- NULL means all numbers
    to_numbers TEXT NOT NULL, -- JSON array of phone numbers
    prefix VARCHAR(100), -- Message prefix
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (instance_id) REFERENCES whatsapp_instances(id)
);

-- Session tokens for JWT authentication
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Audit log for security and tracking
CREATE TABLE IF NOT EXISTS audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50), -- 'instance', 'message', 'file', etc.
    resource_id INTEGER,
    details JSON,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_mobile ON users(mobile_number);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_instances_user ON whatsapp_instances(user_id);
CREATE INDEX IF NOT EXISTS idx_instances_corporate ON whatsapp_instances(corporate_id);
CREATE INDEX IF NOT EXISTS idx_messages_instance ON whatsapp_messages(instance_id);
CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON whatsapp_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_contacts_instance ON contacts(instance_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_instance ON knowledge_files(instance_id);
CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);
CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_created ON audit_logs(created_at);

-- Insert default admin user (password: admin123 - MD5: 0192023a7bbd73250516f069df18b500)
INSERT OR IGNORE INTO users (mobile_number, password_hash, full_name, role, status, approved_at) 
VALUES ('************', '0192023a7bbd73250516f069df18b500', 'System Administrator', 'admin', 'approved', CURRENT_TIMESTAMP);

-- Create default corporate for demo
INSERT OR IGNORE INTO corporates (name, description, admin_user_id) 
VALUES ('Demo Corporation', 'Default corporate account for demonstration', 1);
