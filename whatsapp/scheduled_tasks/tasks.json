{"+91-8487921219": [{"id": "task_1749200536468_40mt4lw8q", "description": "Interview rescheduled to Monday at 10 AM.", "scheduled_time": null, "phone_number": "+91-8487921219", "context": "Interview schedule change", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-06T09:02:16.468Z"}, {"id": "task_1749201069408_n7vtm699l", "description": "Interview with <PERSON> for Python Developer Internship (rescheduled)", "scheduled_time": null, "phone_number": "+91-8487921219", "context": "Python Developer Internship", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-06T09:11:09.408Z"}], "+91-1234567890": [{"id": "task_1749200536937_zkybwz97u", "description": "Reschedule interview for Monday due to hospitalization on Friday.", "scheduled_time": null, "phone_number": "+91-1234567890", "context": "Interview rescheduling", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-06T09:02:16.937Z"}, {"id": "task_1749200913448_sgu6ewt9a", "description": "Interview with <PERSON> from 2pm to 3pm", "scheduled_time": null, "phone_number": "+91-1234567890", "context": "Interview rescheduling", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-06T09:08:33.448Z"}, {"id": "task_1749200959528_j5tp8zj0r", "description": "Interview with <PERSON> on Wednesday from 11:30 to 12:30pm.", "scheduled_time": null, "phone_number": "+91-1234567890", "context": "Interview Rescheduling", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-06T09:09:19.528Z"}, {"id": "task_1749704028232_e5mi2r0s7", "description": "Interview", "scheduled_time": "2025-06-13T10:00:00.000Z", "phone_number": "<EMAIL>", "original_input": "+91-1234567890", "context": "User asked if they can come for interview tomorrow.", "session_id": "8487921219", "status": "executed", "created_at": "2025-06-12T04:53:48.232Z", "executed_at": "2025-06-13T10:20:31.372Z"}, {"id": "task_1749886107844_dcdn3iluq", "description": "Interview for Python Developer Internship (1 Yr) at Arihant AI", "scheduled_time": null, "phone_number": "<EMAIL>", "original_input": "+91-1234567890", "context": "", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-14T07:28:27.844Z"}], "919429673956": [{"id": "task_1749200683398_tlezzngp7", "description": "Follow up with <PERSON> about interview feedback", "scheduled_time": "2025-06-06T09:06:43.398Z", "phone_number": "919429673956", "context": "<PERSON> had interview rescheduled to 10AM on Friday", "session_id": "8487921219", "status": "executed", "created_at": "2025-06-06T09:04:43.398Z", "executed_at": "2025-06-06T09:06:43.399Z"}], "your phone number": [{"id": "task_1749203322800_3j86m26zb", "description": "Python Developer Internship interview rescheduling", "scheduled_time": null, "phone_number": "your phone number", "context": "Rescheduling interview", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-06T09:48:42.800Z"}], "<EMAIL>": [{"id": "task_1749555245575_09pl4qr1m", "description": "Reminder: Follow up on project proposal", "scheduled_time": "2025-06-10T11:36:05.575Z", "phone_number": "<EMAIL>", "context": "User asked about project timeline yesterday", "session_id": "8487921219", "status": "executed", "created_at": "2025-06-10T11:34:05.575Z", "executed_at": "2025-06-10T11:36:05.576Z"}, {"id": "task_1749555273630_zt5shqn2z", "description": "Daily standup meeting reminder", "scheduled_time": "2025-06-11T09:00:00.000Z", "phone_number": "<EMAIL>", "context": "Team meeting scheduled for 9 AM", "session_id": "8487921219", "status": "scheduled", "created_at": "2025-06-10T11:34:33.630Z"}, {"id": "task_1749555298006_tw437x7jo", "description": "Client presentation preparation", "scheduled_time": "2025-06-10T11:36:00.000Z", "phone_number": "<EMAIL>", "context": "Important client meeting next week", "session_id": "8487921219", "status": "executed", "created_at": "2025-06-10T11:34:58.006Z", "executed_at": "2025-06-10T11:36:00.001Z"}], "Rashmi": [{"id": "task_1749555481784_yy0iv9ubi", "description": "<NAME_EMAIL>", "scheduled_time": "2025-06-10T11:39:01.784Z", "phone_number": "<PERSON><PERSON><PERSON>", "context": "User requested to send email", "session_id": "8487921219", "status": "executed", "created_at": "2025-06-10T11:38:01.784Z", "executed_at": "2025-06-10T11:39:01.784Z"}]}