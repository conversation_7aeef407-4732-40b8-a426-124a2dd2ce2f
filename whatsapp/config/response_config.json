{"audioResponse": {"enabled": true, "mode": "voice_only", "voice": "default", "language": "auto", "provider": "auto", "quality": "medium", "speed": 1, "fallbackProvider": "edge"}, "responseTarget": {"mode": "all", "knownNumbers": [], "whitelist": [], "blacklist": []}, "relevanceFilter": {"enabled": false, "strictMode": false, "allowedTopics": [], "blockedTopics": [], "fallbackMessage": "I can only help with business-related topics."}, "responseBehavior": {"maxResponseLength": 1000, "responseDelay": 100, "typingIndicator": true, "readReceipts": true, "autoReply": true}, "languageSettings": {"autoDetect": true, "defaultLanguage": "en", "supportedLanguages": ["en", "hi", "gu", "es", "fr", "de", "ar"], "translateResponses": false}}