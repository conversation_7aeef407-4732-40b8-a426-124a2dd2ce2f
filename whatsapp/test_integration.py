#!/usr/bin/env python3
"""
WhatsApp-Odoo Integration Test Script
Tests the connection between WhatsApp service and Odoo
"""

import requests
import xmlrpc.client
import json
import sys
from datetime import datetime

# Configuration
WHATSAPP_SERVICE_URL = "http://localhost:3001"
ODOO_URL = "http://localhost:8069"
ODOO_DB = "arihantai.com"
ODOO_USERNAME = "admin"  # Change this to your username
ODOO_PASSWORD = "admin"  # Change this to your password

def test_whatsapp_service():
    """Test WhatsApp service connectivity"""
    print("🔍 Testing WhatsApp Service...")
    try:
        response = requests.get(f"{WHATSAPP_SERVICE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ WhatsApp service is running")
            return True
        else:
            print(f"❌ WhatsApp service returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ WhatsApp service connection failed: {e}")
        return False

def test_odoo_connection():
    """Test Odoo XMLRPC connection"""
    print("🔍 Testing Odoo Connection...")
    try:
        common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
        version = common.version()
        print(f"✅ Odoo version: {version['server_version']}")
        
        # Test authentication
        uid = common.authenticate(ODOO_DB, ODOO_USERNAME, ODOO_PASSWORD, {})
        if uid:
            print(f"✅ Authenticated as user ID: {uid}")
            return uid
        else:
            print("❌ Authentication failed")
            return None
    except Exception as e:
        print(f"❌ Odoo connection failed: {e}")
        return None

def test_whatsapp_module(uid):
    """Test WhatsApp module in Odoo"""
    print("🔍 Testing WhatsApp Module...")
    try:
        models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
        
        # Check if WhatsApp models exist
        instance_count = models.execute_kw(ODOO_DB, uid, ODOO_PASSWORD,
            'whatsapp.instance', 'search_count', [[]])
        print(f"✅ WhatsApp instances in database: {instance_count}")
        
        conversation_count = models.execute_kw(ODOO_DB, uid, ODOO_PASSWORD,
            'whatsapp.conversation', 'search_count', [[]])
        print(f"✅ WhatsApp conversations in database: {conversation_count}")
        
        message_count = models.execute_kw(ODOO_DB, uid, ODOO_PASSWORD,
            'whatsapp.message', 'search_count', [[]])
        print(f"✅ WhatsApp messages in database: {message_count}")
        
        template_count = models.execute_kw(ODOO_DB, uid, ODOO_PASSWORD,
            'whatsapp.template', 'search_count', [[]])
        print(f"✅ WhatsApp templates in database: {template_count}")
        
        return True
    except Exception as e:
        print(f"❌ WhatsApp module test failed: {e}")
        return False

def test_whatsapp_instances():
    """Test WhatsApp instances endpoint"""
    print("🔍 Testing WhatsApp Instances Endpoint...")
    try:
        response = requests.get(f"{WHATSAPP_SERVICE_URL}/instances", timeout=5)
        if response.status_code == 200:
            instances = response.json()
            print(f"✅ Found {len(instances)} WhatsApp instances")
            for instance in instances:
                print(f"   - {instance.get('name', 'Unknown')}: {instance.get('status', 'Unknown')}")
            return True
        else:
            print(f"❌ Instances endpoint returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Instances endpoint test failed: {e}")
        return False

def create_test_instance(uid):
    """Create a test WhatsApp instance in Odoo"""
    print("🔍 Creating Test Instance...")
    try:
        models = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/object')
        
        # Check if test instance already exists
        existing = models.execute_kw(ODOO_DB, uid, ODOO_PASSWORD,
            'whatsapp.instance', 'search', 
            [[['name', '=', 'Test Instance']]])
        
        if existing:
            print("✅ Test instance already exists")
            return existing[0]
        
        # Create new test instance
        instance_data = {
            'name': 'Test Instance',
            'phone_number': '+91XXXXXXXXXX',
            'session_id': f'test_session_{int(datetime.now().timestamp())}',
            'is_active': True,
        }
        
        instance_id = models.execute_kw(ODOO_DB, uid, ODOO_PASSWORD,
            'whatsapp.instance', 'create', [instance_data])
        
        print(f"✅ Created test instance with ID: {instance_id}")
        return instance_id
        
    except Exception as e:
        print(f"❌ Test instance creation failed: {e}")
        return None

def test_mudslide():
    """Test mudslide availability"""
    print("🔍 Testing Mudslide...")
    import os
    mudslide_path = "/mnt/whatsapp/mudslide"
    
    if os.path.exists(mudslide_path):
        print("✅ Mudslide directory exists")
        
        # Check if mudslide executable exists
        mudslide_files = os.listdir(mudslide_path)
        if any('mudslide' in f for f in mudslide_files):
            print("✅ Mudslide executable found")
            return True
        else:
            print("⚠️  Mudslide executable not found in directory")
            return False
    else:
        print("❌ Mudslide directory not found")
        return False

def main():
    """Run all integration tests"""
    print("🚀 WhatsApp-Odoo Integration Test Suite")
    print("=" * 50)
    
    results = []
    
    # Test 1: WhatsApp Service
    results.append(("WhatsApp Service", test_whatsapp_service()))
    
    # Test 2: Odoo Connection
    uid = test_odoo_connection()
    results.append(("Odoo Connection", uid is not None))
    
    if uid:
        # Test 3: WhatsApp Module
        results.append(("WhatsApp Module", test_whatsapp_module(uid)))
        
        # Test 4: Create Test Instance
        test_instance = create_test_instance(uid)
        results.append(("Test Instance Creation", test_instance is not None))
    
    # Test 5: WhatsApp Instances Endpoint
    results.append(("WhatsApp Instances API", test_whatsapp_instances()))
    
    # Test 6: Mudslide
    results.append(("Mudslide Availability", test_mudslide()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
