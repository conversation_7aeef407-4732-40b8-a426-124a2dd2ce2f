#!/usr/bin/env python3
"""
Create and approve a user for WhatsApp dashboard authentication
"""

import requests
import json
import sys

# Configuration
WHATSAPP_SERVICE_URL = "http://localhost:3001"

def register_user(mobile_number, password, full_name, email="<EMAIL>"):
    """Register a new user"""
    print(f"🔍 Registering user: {full_name} ({mobile_number})")
    
    try:
        response = requests.post(f"{WHATSAPP_SERVICE_URL}/auth/register", 
            json={
                "mobileNumber": mobile_number,
                "password": password,
                "fullName": full_name,
                "email": email
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ User registered successfully!")
                print(f"   User ID: {result['user']['id']}")
                print(f"   Status: {result['user']['status']}")
                return result['user']['id']
            else:
                print(f"❌ Registration failed: {result.get('message')}")
                return None
        else:
            print(f"❌ Registration failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None

def test_login(mobile_number, password):
    """Test login with the created user"""
    print(f"🔍 Testing login for: {mobile_number}")
    
    try:
        response = requests.post(f"{WHATSAPP_SERVICE_URL}/auth/login",
            json={
                "mobileNumber": mobile_number,
                "password": password
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Login successful!")
                print(f"   Token: {result['token'][:50]}...")
                print(f"   User: {result['user']['fullName']}")
                print(f"   Role: {result['user']['role']}")
                return result['token']
            else:
                print(f"❌ Login failed: {result.get('message')}")
                return None
        else:
            print(f"❌ Login failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_instances_access(token):
    """Test accessing instances with the token"""
    print(f"🔍 Testing instances access...")
    
    try:
        response = requests.get(f"{WHATSAPP_SERVICE_URL}/instances",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Instances access successful!")
            print(f"   Found {len(result.get('instances', []))} instances")
            return True
        else:
            print(f"❌ Instances access failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Instances access error: {e}")
        return False

def create_admin_user_directly():
    """Create admin user directly in database"""
    print("🔧 Creating admin user directly in database...")
    
    try:
        import sqlite3
        import hashlib
        
        # Connect to database
        db_path = "/mnt/whatsapp/database/whatsapp.db"
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if admin user exists
        cursor.execute("SELECT id FROM users WHERE role = 'admin' LIMIT 1")
        admin_exists = cursor.fetchone()
        
        if admin_exists:
            print("✅ Admin user already exists")
            conn.close()
            return True
        
        # Create admin user
        mobile_number = "9999999999"
        password = "admin123"
        full_name = "WhatsApp Admin"
        email = "<EMAIL>"
        
        # Hash password using MD5 (as per auth system)
        password_hash = hashlib.md5(password.encode()).hexdigest()
        
        cursor.execute("""
            INSERT INTO users (mobile_number, password_hash, full_name, email, role, status, created_at)
            VALUES (?, ?, ?, ?, 'admin', 'approved', datetime('now'))
        """, (mobile_number, password_hash, full_name, email))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"✅ Admin user created successfully!")
        print(f"   User ID: {user_id}")
        print(f"   Mobile: {mobile_number}")
        print(f"   Password: {password}")
        print(f"   Role: admin")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Main function"""
    print("🚀 WhatsApp Dashboard User Creation Tool")
    print("=" * 50)
    
    # First try to create admin user directly in database
    if create_admin_user_directly():
        # Test login with admin user
        token = test_login("9999999999", "admin123")
        if token:
            # Test instances access
            test_instances_access(token)
            
            print("\n" + "=" * 50)
            print("🎉 SUCCESS! Admin user is ready for dashboard login:")
            print("   Mobile Number: 9999999999")
            print("   Password: admin123")
            print("   Role: admin")
            print("\nYou can now login to the dashboard at:")
            print("   http://140.238.231.90:3002")
            return 0
    
    # Fallback: try registration endpoint
    print("\n🔄 Trying registration endpoint...")
    
    # Create test user
    user_id = register_user("1234567890", "test123", "Test User", "<EMAIL>")
    
    if user_id:
        print(f"\n⚠️  User created but needs admin approval.")
        print(f"   Since we don't have an admin yet, the user cannot login.")
        print(f"   Try the direct database method above.")
    
    return 1

if __name__ == "__main__":
    sys.exit(main())
