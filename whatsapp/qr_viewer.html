<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp QR Code Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 600;
        }
        
        .login-form {
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .qr-section {
            display: none;
            margin-top: 30px;
        }
        
        .qr-code {
            max-width: 300px;
            margin: 20px auto;
            border: 3px solid #667eea;
            border-radius: 15px;
            padding: 10px;
            background: white;
        }
        
        .qr-code img {
            width: 100%;
            height: auto;
            border-radius: 10px;
        }
        
        .instance-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: left;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status.ready { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.connected { background: #cce5ff; color: #004085; }
        
        .error {
            color: #dc3545;
            margin: 15px 0;
            padding: 10px;
            background: #f8d7da;
            border-radius: 8px;
        }
        
        .success {
            color: #155724;
            margin: 15px 0;
            padding: 10px;
            background: #d4edda;
            border-radius: 8px;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .instances-list {
            text-align: left;
            margin: 20px 0;
        }
        
        .instance-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .instance-item h4 {
            margin-bottom: 8px;
            color: #333;
        }
        
        .instance-item p {
            margin: 4px 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 WhatsApp QR Viewer</h1>
        
        <div id="loginSection" class="login-form">
            <div class="form-group">
                <label for="mobile">Mobile Number:</label>
                <input type="text" id="mobile" placeholder="919999999999" value="919999999999">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter password" value="admin123">
            </div>
            <button class="btn" onclick="login()">Login & View Instances</button>
        </div>
        
        <div id="instancesSection" class="qr-section">
            <h3>📱 WhatsApp Instances</h3>
            <div id="instancesList" class="instances-list"></div>
            <button class="btn" onclick="refreshInstances()">🔄 Refresh</button>
            <button class="btn" onclick="logout()">Logout</button>
        </div>
        
        <div id="qrSection" class="qr-section">
            <div id="qrCode" class="qr-code"></div>
            <div id="instanceInfo" class="instance-info"></div>
            <button class="btn" onclick="backToInstances()">← Back to Instances</button>
            <button class="btn" onclick="refreshQR()">🔄 Refresh QR</button>
        </div>
        
        <div id="message"></div>
    </div>

    <script>
        let authToken = '';
        let currentSessionId = '';
        const API_BASE = 'http://**************:3001';

        function showMessage(text, type = 'error') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${text}</div>`;
            setTimeout(() => messageDiv.innerHTML = '', 5000);
        }

        function showLoading(buttonId) {
            const btn = document.getElementById(buttonId) || event.target;
            btn.innerHTML = '<span class="loading"></span>Loading...';
            btn.disabled = true;
        }

        function hideLoading(buttonId, text) {
            const btn = document.getElementById(buttonId) || event.target;
            btn.innerHTML = text;
            btn.disabled = false;
        }

        async function login() {
            const mobile = document.getElementById('mobile').value;
            const password = document.getElementById('password').value;
            
            if (!mobile || !password) {
                showMessage('Please enter mobile number and password');
                return;
            }

            showLoading();
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ mobileNumber: mobile, password })
                });

                const result = await response.json();
                
                if (result.success) {
                    authToken = result.token;
                    showMessage('Login successful!', 'success');
                    document.getElementById('loginSection').style.display = 'none';
                    document.getElementById('instancesSection').style.display = 'block';
                    loadInstances();
                } else {
                    showMessage(result.message || 'Login failed');
                }
            } catch (error) {
                showMessage('Network error: ' + error.message);
            }
            
            hideLoading('Login & View Instances');
        }

        async function loadInstances() {
            try {
                const response = await fetch(`${API_BASE}/instances`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const instances = await response.json();
                displayInstances(instances);
            } catch (error) {
                showMessage('Failed to load instances: ' + error.message);
            }
        }

        function displayInstances(instances) {
            const listDiv = document.getElementById('instancesList');
            
            if (!instances || instances.length === 0) {
                listDiv.innerHTML = '<p>No instances found</p>';
                return;
            }

            listDiv.innerHTML = instances.map(instance => `
                <div class="instance-item">
                    <h4>📱 ${instance.sessionId}</h4>
                    <p><strong>Phone:</strong> ${instance.phoneNumber}</p>
                    <p><strong>Status:</strong> <span class="status ${instance.status.toLowerCase()}">${instance.status}</span></p>
                    <p><strong>Created:</strong> ${new Date(instance.createdAt).toLocaleString()}</p>
                    <button class="btn" onclick="viewQR('${instance.sessionId}')">📱 View QR Code</button>
                    <button class="btn" onclick="deleteInstance('${instance.sessionId}')">🗑️ Delete</button>
                </div>
            `).join('');
        }

        async function viewQR(sessionId) {
            currentSessionId = sessionId;
            showLoading();
            
            try {
                const response = await fetch(`${API_BASE}/instances/${sessionId}/qr`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const result = await response.json();
                
                if (result.success && result.image) {
                    document.getElementById('instancesSection').style.display = 'none';
                    document.getElementById('qrSection').style.display = 'block';
                    
                    document.getElementById('qrCode').innerHTML = 
                        `<img src="${result.image}" alt="QR Code for ${sessionId}">`;
                    
                    document.getElementById('instanceInfo').innerHTML = `
                        <h4>📱 Instance: ${sessionId}</h4>
                        <p><strong>Status:</strong> QR Ready</p>
                        <p><strong>Instructions:</strong> Scan this QR code with WhatsApp mobile app</p>
                        <p><strong>Auto-refresh:</strong> Every 10 seconds</p>
                    `;
                    
                    // Auto-refresh QR every 10 seconds
                    setTimeout(() => refreshQR(), 10000);
                } else {
                    showMessage(result.message || 'Failed to get QR code');
                }
            } catch (error) {
                showMessage('Failed to load QR code: ' + error.message);
            }
            
            hideLoading('View QR Code');
        }

        async function refreshQR() {
            if (currentSessionId) {
                await viewQR(currentSessionId);
            }
        }

        async function deleteInstance(sessionId) {
            if (!confirm(`Are you sure you want to delete instance ${sessionId}?`)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/instances/${sessionId}`, {
                    method: 'DELETE',
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                const result = await response.json();
                
                if (result.success) {
                    showMessage('Instance deleted successfully', 'success');
                    loadInstances();
                } else {
                    showMessage(result.message || 'Failed to delete instance');
                }
            } catch (error) {
                showMessage('Failed to delete instance: ' + error.message);
            }
        }

        function refreshInstances() {
            loadInstances();
        }

        function backToInstances() {
            document.getElementById('qrSection').style.display = 'none';
            document.getElementById('instancesSection').style.display = 'block';
            currentSessionId = '';
        }

        function logout() {
            authToken = '';
            currentSessionId = '';
            document.getElementById('instancesSection').style.display = 'none';
            document.getElementById('qrSection').style.display = 'none';
            document.getElementById('loginSection').style.display = 'block';
            showMessage('Logged out successfully', 'success');
        }

        // Auto-login if credentials are provided
        window.onload = function() {
            const mobile = document.getElementById('mobile').value;
            const password = document.getElementById('password').value;
            if (mobile && password) {
                // Auto-login after 1 second
                setTimeout(login, 1000);
            }
        };
    </script>
</body>
</html>
