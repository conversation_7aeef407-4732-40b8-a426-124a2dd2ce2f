"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  QrCode,
  Send,
  Settings,
  Database,
  Bot,
  Trash2,
  Activity,
  ExternalLink,
  Smartphone,
  CheckCircle,
  AlertCircle,
  Clock,
  XCircle,
  MessageSquare,
  Volume2,
  RefreshCw
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface WhatsAppInstance {
  sessionId: string;
  status: "connected" | "qr_ready" | "initializing" | "error";
  hasQR: boolean;
  messageCount?: number;
  lastActivity?: string;
}

interface InstanceCardProps {
  instance: WhatsAppInstance;
  onDelete: (sessionId: string) => Promise<{ success: boolean; error?: string }>;
}

export function InstanceCard({ instance, onDelete }: InstanceCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const result = await onDelete(instance.sessionId);
      if (!result.success) {
        alert(`Error: ${result.error}`);
      }
    } catch (error) {
      alert("Failed to delete instance");
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case "connected":
        return {
          icon: CheckCircle,
          color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
          label: "Connected",
          description: "Ready to send messages"
        };
      case "qr_ready":
        return {
          icon: QrCode,
          color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
          label: "QR Ready",
          description: "Scan QR code to connect"
        };
      case "initializing":
        return {
          icon: Clock,
          color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
          label: "Initializing",
          description: "Setting up connection"
        };
      case "error":
        return {
          icon: XCircle,
          color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
          label: "Error",
          description: "Connection failed"
        };
      default:
        return {
          icon: AlertCircle,
          color: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
          label: "Unknown",
          description: "Status unknown"
        };
    }
  };

  const statusConfig = getStatusConfig(instance.status);
  const StatusIcon = statusConfig.icon;

  const baseUrl = "http://**************:3001";
  const nextjsBaseUrl = "http://**************:3002";

  return (
    <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
      {/* Status indicator line */}
      <div className={`absolute top-0 left-0 right-0 h-1 ${
        instance.status === "connected" ? "bg-green-500" :
        instance.status === "qr_ready" ? "bg-yellow-500" :
        instance.status === "initializing" ? "bg-blue-500" :
        "bg-red-500"
      }`} />
      
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="p-2 bg-slate-100 dark:bg-slate-800 rounded-lg">
              <Smartphone className="h-5 w-5 text-slate-600 dark:text-slate-400" />
            </div>
            <span className="font-semibold text-slate-900 dark:text-slate-100">
              {instance.sessionId}
            </span>
          </CardTitle>
          
          <Badge className={`${statusConfig.color} border-0 font-medium`}>
            <StatusIcon className="h-3 w-3 mr-1" />
            {statusConfig.label}
          </Badge>
        </div>
        
        <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
          {statusConfig.description}
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Instance Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <div className="text-lg font-bold text-slate-900 dark:text-slate-100">
              {instance.messageCount || 0}
            </div>
            <div className="text-xs text-slate-500 dark:text-slate-400">
              Messages
            </div>
          </div>
          
          <div className="text-center p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <div className="text-lg font-bold text-slate-900 dark:text-slate-100">
              {instance.hasQR ? "✅" : "❌"}
            </div>
            <div className="text-xs text-slate-500 dark:text-slate-400">
              QR Code
            </div>
          </div>
        </div>

        {/* Last Activity */}
        {instance.lastActivity && (
          <div className="text-xs text-slate-500 dark:text-slate-400">
            <strong>Last Activity:</strong> {new Date(instance.lastActivity).toLocaleString()}
          </div>
        )}

        {/* Special Reconnect Button for Error Status */}
        {instance.status === "error" && (
          <div className="mb-3">
            <Button
              variant="default"
              size="sm"
              className="w-full bg-orange-600 hover:bg-orange-700 text-white font-medium"
              asChild
            >
              <a href={`${nextjsBaseUrl}/qr/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
                <RefreshCw className="h-4 w-4 mr-2" />
                🔄 Reconnect Instance
              </a>
            </Button>
          </div>
        )}

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            asChild
          >
            <a href={`${nextjsBaseUrl}/qr/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
              <QrCode className="h-4 w-4 mr-2" />
              QR Code
            </a>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            asChild
          >
            <a href={`${nextjsBaseUrl}/messages/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
              <MessageSquare className="h-4 w-4 mr-2" />
              Messages
            </a>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            asChild
          >
            <a href={`${nextjsBaseUrl}/test/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
              <Send className="h-4 w-4 mr-2" />
              Test
            </a>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            asChild
          >
            <a href={`${nextjsBaseUrl}/tools/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
              <Settings className="h-4 w-4 mr-2" />
              Tools
            </a>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            asChild
          >
            <a href={`${nextjsBaseUrl}/knowledge-base/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
              <Database className="h-4 w-4 mr-2" />
              Knowledge
            </a>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            asChild
          >
            <a href={`${nextjsBaseUrl}/chatbot/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
              <Bot className="h-4 w-4 mr-2" />
              Chatbot
            </a>
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            asChild
          >
            <a href={`${nextjsBaseUrl}/response-config/${instance.sessionId}`} target="_blank" rel="noopener noreferrer">
              <Volume2 className="h-4 w-4 mr-2" />
              Response Config
            </a>
          </Button>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-950"
                disabled={isDeleting}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {isDeleting ? "Deleting..." : "Delete"}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete WhatsApp Instance</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete the instance "{instance.sessionId}"? 
                  This action cannot be undone and will disconnect the WhatsApp session.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDelete}
                  className="bg-red-600 hover:bg-red-700 text-white"
                  disabled={isDeleting}
                >
                  {isDeleting ? "Deleting..." : "Delete Instance"}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardContent>
    </Card>
  );
}
