"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { withAuth } from "@/contexts/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  QrCode, 
  RefreshCw, 
  ArrowLeft, 
  Smartphone, 
  CheckCircle,
  AlertCircle,
  Clock,
  Wifi
} from "lucide-react";

interface QRStatus {
  hasQR: boolean;
  qrCode?: string;
  status: string;
  message?: string;
}

function QRCodePage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;
  
  const [qrStatus, setQrStatus] = useState<QRStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [restarting, setRestarting] = useState(false);

  const fetchQRStatus = async () => {
    try {
      setLoading(true);

      // Get auth token from localStorage (using correct key)
      const token = localStorage.getItem('auth_token');
      if (!token) {
        setError("Authentication required. Please login again.");
        return;
      }

      const response = await fetch(`http://140.238.231.90:3001/instances/${sessionId}/qr`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();

        if (data.success && data.image) {
          setQrStatus({
            hasQR: true,
            qrCode: data.image,
            status: "qr_ready",
            message: "Scan this QR code with WhatsApp on your phone"
          });
        } else if (data.error && data.error.includes('connected')) {
          setQrStatus({
            hasQR: false,
            status: "connected",
            message: "WhatsApp instance is already connected and authenticated!"
          });
        } else {
          setQrStatus({
            hasQR: false,
            status: "no_qr",
            message: data.error || "QR code not available. The instance might be initializing."
          });
        }
      } else {
        setError(`Failed to fetch QR code: ${response.status}`);
      }
    } catch (error) {
      setError("Network error while fetching QR code");
      console.error("QR fetch error:", error);
    } finally {
      setLoading(false);
    }
  };

  const restartInstance = async () => {
    try {
      setRestarting(true);
      setError("");

      const token = localStorage.getItem('authToken');
      if (!token) {
        setError("Authentication required. Please login again.");
        return;
      }

      // First, try to delete the existing instance
      const deleteResponse = await fetch(`http://140.238.231.90:3001/instances/${sessionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create a new instance with the same sessionId
      // We'll use the sessionId as the phone number for now, but this should be configurable
      const phoneNumber = sessionId.startsWith('91') ? sessionId : `91${sessionId}`;

      const createResponse = await fetch(`http://140.238.231.90:3001/instances`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: sessionId,
          phoneNumber: phoneNumber
        })
      });

      if (createResponse.ok) {
        const createData = await createResponse.json();
        if (createData.success) {
          // Wait a moment for the instance to initialize
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Now fetch the QR code
          await fetchQRStatus();
        } else {
          setError(`Failed to create instance: ${createData.error}`);
        }
      } else {
        setError(`Failed to create instance: ${createResponse.status}`);
      }
    } catch (error) {
      setError("Failed to restart instance");
      console.error("Restart error:", error);
    } finally {
      setRestarting(false);
    }
  };

  useEffect(() => {
    if (sessionId) {
      fetchQRStatus();
      
      // Auto-refresh every 3 seconds
      const interval = setInterval(fetchQRStatus, 3000);
      return () => clearInterval(interval);
    }
  }, [sessionId]);

  const getStatusConfig = (status: string) => {
    switch (status) {
      case "connected":
        return {
          icon: CheckCircle,
          color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
          bgColor: "from-green-500 to-green-600"
        };
      case "qr_ready":
        return {
          icon: QrCode,
          color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
          bgColor: "from-blue-500 to-blue-600"
        };
      case "no_qr":
        return {
          icon: Clock,
          color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
          bgColor: "from-yellow-500 to-yellow-600"
        };
      default:
        return {
          icon: AlertCircle,
          color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
          bgColor: "from-red-500 to-red-600"
        };
    }
  };

  const statusConfig = qrStatus ? getStatusConfig(qrStatus.status) : getStatusConfig("error");
  const StatusIcon = statusConfig.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
              WhatsApp QR Authentication
            </h1>
            <p className="text-slate-600 dark:text-slate-400">
              Session: {sessionId}
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* QR Code Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className={`p-2 bg-gradient-to-r ${statusConfig.bgColor} text-white rounded-lg`}>
                  <QrCode className="h-5 w-5" />
                </div>
                QR Code Authentication
              </CardTitle>
              <CardDescription>
                Scan the QR code with WhatsApp on your phone to authenticate
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading ? (
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <RefreshCw className="h-12 w-12 animate-spin text-slate-400" />
                  <p className="text-slate-600 dark:text-slate-400">Loading QR code...</p>
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <AlertCircle className="h-12 w-12 text-red-500" />
                  <p className="text-red-600 dark:text-red-400">{error}</p>
                  <Button onClick={fetchQRStatus} variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </div>
              ) : qrStatus?.hasQR ? (
                <div className="flex flex-col items-center space-y-4">
                  <div className="p-4 bg-white rounded-lg shadow-md">
                    <img 
                      src={qrStatus.qrCode} 
                      alt="WhatsApp QR Code" 
                      className="w-64 h-64 object-contain"
                    />
                  </div>
                  <Badge className={statusConfig.color}>
                    <StatusIcon className="h-3 w-3 mr-1" />
                    {qrStatus.message}
                  </Badge>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <StatusIcon className="h-12 w-12 text-slate-400" />
                  <p className="text-slate-600 dark:text-slate-400 text-center">
                    {qrStatus?.message || "QR code not available"}
                  </p>
                  {qrStatus?.status !== "connected" && (
                    <div className="flex gap-2">
                      <Button onClick={fetchQRStatus} variant="outline" disabled={restarting}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Check Again
                      </Button>
                      <Button
                        onClick={restartInstance}
                        variant="default"
                        disabled={restarting}
                        className="bg-orange-600 hover:bg-orange-700"
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${restarting ? 'animate-spin' : ''}`} />
                        {restarting ? 'Restarting...' : 'Restart Instance'}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Instructions Card */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg">
                  <Smartphone className="h-5 w-5" />
                </div>
                How to Connect
              </CardTitle>
              <CardDescription>
                Follow these steps to connect your WhatsApp
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ol className="space-y-4">
                <li className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    1
                  </div>
                  <div>
                    <p className="font-medium">Open WhatsApp on your phone</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Make sure you have WhatsApp installed and set up
                    </p>
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    2
                  </div>
                  <div>
                    <p className="font-medium">Go to Settings → Linked Devices</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Tap on "Link a Device" option
                    </p>
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    3
                  </div>
                  <div>
                    <p className="font-medium">Scan the QR code</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Point your camera at the QR code displayed above
                    </p>
                  </div>
                </li>
                <li className="flex gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    ✓
                  </div>
                  <div>
                    <p className="font-medium">Connection established!</p>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Your WhatsApp is now connected to the automation platform
                    </p>
                  </div>
                </li>
              </ol>

              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex gap-2">
                  <Wifi className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium text-blue-900 dark:text-blue-100">
                      Auto-refresh enabled
                    </p>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      This page automatically refreshes every 3 seconds to show the latest QR code status.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button onClick={fetchQRStatus} variant="outline" disabled={restarting}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
          <Button
            onClick={restartInstance}
            variant="default"
            disabled={restarting}
            className="bg-orange-600 hover:bg-orange-700"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${restarting ? 'animate-spin' : ''}`} />
            {restarting ? 'Restarting Instance...' : 'Restart Instance'}
          </Button>
          <Button variant="outline" asChild>
            <a href={`http://140.238.231.90:3001/test/${sessionId}`} target="_blank" rel="noopener noreferrer">
              Test Connection
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}

export default withAuth(QRCodePage);
