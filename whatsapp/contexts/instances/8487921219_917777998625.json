{"sessionId": "8487921219", "phoneNumber": "<EMAIL>", "messages": [{"role": "user", "content": "Hello sir", "timestamp": "2025-06-14T06:54:32.412Z", "messageId": "1749884072412_gnqr4a0af"}, {"role": "assistant", "content": "Hello! 😊 It's nice to meet you. Is there something I can help you with or would you like to know more about Arihant AI Solutions? 🤔", "timestamp": "2025-06-14T06:54:33.305Z", "messageId": "1749884073305_xoq7ydbdu"}, {"role": "user", "content": "Good afternoon", "timestamp": "2025-06-14T06:54:36.043Z", "messageId": "1749884076043_mbxbxd9q2"}, {"role": "assistant", "content": "Good afternoon! 😊 How can I assist you today? Do you have any questions or need information about our services?", "timestamp": "2025-06-14T06:54:36.613Z", "messageId": "1749884076613_w65wfrif7"}, {"role": "user", "content": "Good afternoon sir", "timestamp": "2025-06-14T07:11:47.122Z", "messageId": "1749885107122_xro2g9kht"}, {"role": "assistant", "content": "Good afternoon! 😊 How are you doing today? Is there something I can help you with or would you like to know more about Arihant AI Solutions? 🤔 \n\nBy the way, we have a few openings for internships and full-time positions. Would you like to know more about them? \n\n(Python Developer Internship, React Native Internship, ERP Support & Manual Tester, UI/UX Developer Internship)", "timestamp": "2025-06-14T07:11:47.922Z", "messageId": "1749885107922_2poedphjk"}, {"role": "user", "content": "I hope sir its a good time to speak to you.", "timestamp": "2025-06-14T07:13:19.908Z", "messageId": "1749885199908_0vlus320y"}, {"role": "assistant", "content": "It's great to speak with you too! 😊 I'm here to help and provide any information you need. Please feel free to ask me anything, whether it's about our company, services, or job openings. I'm all ears! \n\nBy the way, would you like to schedule an interview for one of our open positions? We have a few slots available this Friday. 📅", "timestamp": "2025-06-14T07:13:20.535Z", "messageId": "1749885200535_parm0v8x6"}, {"role": "user", "content": "Sir u told me to remind you about scheduling an interview for python Developer internship role on Tuesday", "timestamp": "2025-06-14T07:15:44.677Z", "messageId": "1749885344677_5pgsgity3"}, {"role": "assistant", "content": "I'm glad you reminded me! 📅 Unfortunately, I don't have any record of our previous conversation about scheduling an interview on Tuesday. Could you please provide more details like the candidate's name, position, and preferred interview time? \n\nAlso, please confirm if the interview is still scheduled for this Tuesday. I'll make sure to follow up and assist you further. 📝 \n\n(As a friendly chatbot, I don't have personal memories, so I rely on your reminders to help me stay on track!)", "timestamp": "2025-06-14T07:15:45.370Z", "messageId": "1749885345370_ekos5dobg"}], "userProfile": {"name": "<PERSON><PERSON>", "pushname": "<PERSON><PERSON>", "number": "917777998625", "isMyContact": false, "lastSeen": "2025-06-14T07:15:44.679Z"}, "currentTopic": null, "createdAt": "2025-06-10T13:29:29.994Z", "lastUpdated": "2025-06-14T07:15:45.370Z", "metadata": {}}