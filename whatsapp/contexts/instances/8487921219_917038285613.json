{"sessionId": "8487921219", "phoneNumber": "<EMAIL>", "messages": [{"role": "user", "content": "Career portal", "timestamp": "2025-06-20T04:18:34.493Z", "messageId": "1750393114493_7ontrhhdm"}, {"role": "assistant", "content": "We don't have a career portal. However, you can reach out to us directly through our email (<EMAIL>) or phone number (+91-8487921219) to inquire about job opportunities or internships. We're always looking for talented individuals to join our team! 💼 \n\nCurrently, we have openings for:\n\n* Python Developer Internship (1 Yr)\n* React Native Internship (6 months)\n* ERP Support & Manual Tester (Full Time Job)\n* UI/UX Developer Internship (6 months)\n\nIf you're interested in any of these positions, please let us know, and we'll be happy to guide you through the application process! 📝", "timestamp": "2025-06-20T04:18:35.720Z", "messageId": "1750393115720_6wbi8f4au"}, {"role": "user", "content": "Yes I am interested", "timestamp": "2025-06-20T04:18:59.378Z", "messageId": "1750393139378_kqfkebgpo"}, {"role": "assistant", "content": "😊 Wonderful! We're excited to hear that you're interested in exploring opportunities with Arihant AI Solutions.\n\nAs discussed earlier, we have openings for:\n\n1. Python Developer Internship (1 Yr)\n2. React Native Internship (6 months)\n3. ERP Support & Manual Tester (Full Time Job)\n4. UI/UX Developer Internship (6 months)\n\nTo proceed, could you please share your:\n\n1. Resume\n2. Relevant experience (if any)\n3. Education background\n\nYou can share this information via WhatsApp, email (<EMAIL>), or by scheduling a call with me directly.\n\nAlso, would you be available for an interview on this Friday, 6th June, between 10 AM - 6 PM? 📅\n\nLooking forward to hearing back from you!", "timestamp": "2025-06-20T04:19:00.730Z", "messageId": "1750393140730_vb3tmcecs"}, {"role": "user", "content": "Yes", "timestamp": "2025-06-20T04:19:18.114Z", "messageId": "1750393158114_egbjiaa1h"}, {"role": "assistant", "content": "😊 Great! We're excited to move forward with the next steps.\n\nTo confirm, we'll schedule an interview for this Friday, 6th June, between 10 AM - 6 PM. We'll send you a detailed interview schedule and requirements shortly.\n\nIn the meantime, please send your resume, relevant experience, and education <NAME_EMAIL> or WhatsApp +91-8487921219.\n\nAlso, here are the interview details:\n\n* Date: 6th June (Friday)\n* Time: Slot of your choice (10 AM - 6 PM)\n* Location: Arihant AI Office, Prahladnagar, Ahmedabad\n* Duration: 45 minutes\n* Process: Technical + HR Round\n\nLooking forward to meeting you on Friday! 📅 \n\nShall I generate a PDF with all the details for your reference?", "timestamp": "2025-06-20T04:19:19.458Z", "messageId": "1750393159458_5er3hyzce"}, {"role": "user", "content": "Yes", "timestamp": "2025-06-20T04:20:13.847Z", "messageId": "1750393213847_8v20h0uz2"}, {"role": "assistant", "content": "I've generated a PDF with the interview details for your reference. You can find it attached.\n\nInterview Details:\n\n* Date: 6th June (Friday)\n* Time: Slot of your choice (10 AM -6 PM)\n* Location: Arihant AI Office, Prahladnagar, Ahmedabad\n* Duration: 45 minutes\n* Process: Technical + HR Round\n\nRequired Documents:\n\n* Updated Resume\n* Photo ID Proof\n* Educational Certificates\n* Experience Letters (if applicable)\n\nFeel free to reach out to me directly if you have any questions or concerns. Looking forward to meeting you on Friday! 📅 \n\nPlease find the PDF attached: document_1750393214762.pdf", "timestamp": "2025-06-20T04:20:19.982Z", "messageId": "1750393219982_woia4fpez"}, {"role": "user", "content": "In which location", "timestamp": "2025-06-20T04:45:25.941Z", "messageId": "1750394725941_mltscpmpu"}, {"role": "assistant", "content": "The interview will be held at our office located in:\n\nArihant AI Office\nPrahladnagar, Ahmedabad\n\nPlease make sure to note down the location and plan your travel accordingly. If you need any assistance or have questions, feel free to reach out! 📍", "timestamp": "2025-06-20T04:45:26.596Z", "messageId": "1750394726596_dp0q01t2d"}], "userProfile": {"name": "<PERSON><PERSON>", "pushname": "<PERSON><PERSON>", "number": "917038285613", "isMyContact": false, "lastSeen": "2025-06-20T04:45:25.943Z"}, "currentTopic": null, "createdAt": "2025-06-20T04:14:24.121Z", "lastUpdated": "2025-06-20T04:45:26.596Z", "metadata": {}}