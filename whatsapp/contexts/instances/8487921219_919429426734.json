{"sessionId": "8487921219", "phoneNumber": "<EMAIL>", "messages": [{"role": "user", "content": "He has emailed also and is waiting for response from your end. He was told to remind you today, so as to discuss about scheduling the interview.", "timestamp": "2025-06-10T14:10:57.315Z", "messageId": "1749564657315_1ms91exw6"}, {"role": "assistant", "content": "I've sent a message to the number, and it was successfully delivered. Hopefully, <PERSON><PERSON><PERSON><PERSON> will get back to us soon to confirm a suitable interview slot.", "timestamp": "2025-06-10T14:11:06.833Z", "messageId": "1749564666833_wkzm1016b"}, {"role": "user", "content": "Thanks a lot 🙏", "timestamp": "2025-06-10T14:11:32.741Z", "messageId": "1749564692741_ou3uew6fu"}, {"role": "assistant", "content": "You're welcome! 😊 It was my pleasure to help. I hope <PERSON><PERSON><PERSON><PERSON>'s interview goes well and that he joins our team soon! 💻👍 If there's anything else I can assist you with, feel free to ask!", "timestamp": "2025-06-10T14:11:34.737Z", "messageId": "1749564694737_swv6zgjei"}, {"role": "user", "content": "Hello Sir", "timestamp": "2025-06-11T13:35:53.998Z", "messageId": "1749648953999_bxnkcqxsp"}, {"role": "assistant", "content": "Hello! 😊 It's nice to meet you. I'm here to help with any questions or tasks you have. How can I assist you today? 🤗 \n\n(If you have a specific query or need information, feel free to ask!)", "timestamp": "2025-06-11T13:35:54.619Z", "messageId": "1749648954619_cm8qv7tdx"}, {"role": "user", "content": "Greetings for the day", "timestamp": "2025-06-11T13:36:03.915Z", "messageId": "1749648963915_lgw19snkr"}, {"role": "assistant", "content": "Good morning! ☀️ I hope you're having a great start to the day. Is there something I can help you with or would you like to chat? 😊 \n\n(If you have a specific query or need information, feel free to ask!)", "timestamp": "2025-06-11T13:36:04.628Z", "messageId": "1749648964628_3b3uvbgrw"}, {"role": "user", "content": "Good morning Sir", "timestamp": "2025-06-19T03:11:23.519Z", "messageId": "1750302683519_5r2lft6kp"}, {"role": "assistant", "content": "Good morning! 😊 How can I assist you today? Do you have any questions or need information about Arihant AI Solutions? 🤔", "timestamp": "2025-06-19T03:11:24.942Z", "messageId": "1750302684942_6zw5ld1lg"}], "userProfile": {"name": "<PERSON><PERSON><PERSON>", "pushname": "<PERSON><PERSON><PERSON>", "number": "919429426734", "isMyContact": false, "profilePicUrl": "https://pps.whatsapp.net/v/t61.24694-24/457745572_592872286402155_7799977257744707730_n.jpg?ccb=11-4&oh=01_Q5Aa1wFaq0NZVnAH19IqrcxGAFlw-eFLVTageWTuqobpZAg_PA&oe=6860775E&_nc_sid=5e03e0&_nc_cat=104", "lastSeen": "2025-06-19T03:11:23.522Z"}, "currentTopic": null, "createdAt": "2025-06-10T14:03:57.251Z", "lastUpdated": "2025-06-19T03:11:24.942Z", "metadata": {}}