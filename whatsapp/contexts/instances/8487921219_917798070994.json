{"sessionId": "8487921219", "phoneNumber": "<EMAIL>", "messages": [{"role": "user", "content": "<PERSON>", "timestamp": "2025-06-13T10:25:48.819Z", "messageId": "1749810348819_gz2x0hbas"}, {"role": "user", "content": "Any update on interview?", "timestamp": "2025-06-13T10:25:49.351Z", "messageId": "1749810349351_86w3pjcfz"}, {"role": "assistant", "content": "Hi there! 😊 I'm happy to chat with you. How can I assist you today? Do you have any questions or topics you'd like to discuss? I'm here to help! 💬", "timestamp": "2025-06-13T10:25:49.850Z", "messageId": "1749810349850_d97984ppx"}, {"role": "assistant", "content": "There are two scheduled interviews:\n\n1. Interview rescheduled to Monday at 10 AM.\n2. Interview with <PERSON> for Python Developer Internship (rescheduled).\n\nWould you like more details?", "timestamp": "2025-06-13T10:25:50.839Z", "messageId": "1749810350839_ac557gsot"}, {"role": "user", "content": "I am okay with Monday at 10 AM", "timestamp": "2025-06-13T10:27:13.994Z", "messageId": "1749810433994_c5mp5c65m"}, {"role": "assistant", "content": "I've confirmed your availability for Monday at 10 AM. Please expect a call from us then. If you have any questions or need assistance, feel free to reach out! 📞", "timestamp": "2025-06-13T10:27:15.112Z", "messageId": "1749810435112_6q3gbdngj"}], "userProfile": {"name": "Alok", "pushname": "Alok", "number": "917798070994", "isMyContact": false, "lastSeen": "2025-06-13T10:27:14.021Z"}, "currentTopic": null, "createdAt": "2025-06-13T10:25:48.819Z", "lastUpdated": "2025-06-13T10:27:15.112Z", "metadata": {}}