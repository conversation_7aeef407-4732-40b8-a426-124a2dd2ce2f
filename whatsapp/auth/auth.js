const crypto = require('crypto');
const jwt = require('jsonwebtoken');

class AuthManager {
    constructor(databaseManager) {
        this.db = databaseManager;
        this.jwtSecret = process.env.JWT_SECRET || 'whatsapp-automation-secret-key-2024';
        this.tokenExpiry = 24 * 60 * 60 * 1000; // 24 hours
    }

    // Generate JWT token
    generateToken(user) {
        const payload = {
            userId: user.id,
            mobileNumber: user.mobile_number,
            role: user.role,
            corporateId: user.corporate_id,
            iat: Math.floor(Date.now() / 1000)
        };
        
        return jwt.sign(payload, this.jwtSecret, { expiresIn: '24h' });
    }

    // Verify JWT token
    verifyToken(token) {
        try {
            return jwt.verify(token, this.jwtSecret);
        } catch (error) {
            return null;
        }
    }

    // Hash password using MD5
    hashPassword(password) {
        return crypto.createHash('md5').update(password).digest('hex');
    }

    // Register new user
    async register(userData) {
        try {
            const { mobileNumber, password, fullName, email, corporateId } = userData;
            
            // Validate mobile number format
            if (!/^\d{10,15}$/.test(mobileNumber)) {
                throw new Error('Invalid mobile number format');
            }

            // Create user (status will be 'pending' by default)
            const user = await this.db.createUser({
                mobileNumber,
                password,
                fullName,
                email,
                corporateId
            });

            return {
                success: true,
                message: 'Registration successful. Please wait for admin approval.',
                user: {
                    id: user.id,
                    mobileNumber: user.mobileNumber,
                    fullName: user.fullName,
                    status: 'pending'
                }
            };
        } catch (error) {
            return {
                success: false,
                message: error.message.includes('UNIQUE constraint') ? 
                    'Mobile number already registered' : error.message
            };
        }
    }

    // Login user
    async login(mobileNumber, password, ipAddress, userAgent) {
        try {
            const user = await this.db.authenticateUser(mobileNumber, password);
            
            if (!user) {
                return {
                    success: false,
                    message: 'Invalid credentials or account not approved'
                };
            }

            // Generate JWT token
            const token = this.generateToken(user);
            const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
            const expiresAt = new Date(Date.now() + this.tokenExpiry);

            // Save session to database
            await this.db.createSession(user.id, tokenHash, expiresAt, ipAddress, userAgent);

            // Log login action
            await this.db.logAction(user.id, 'login', 'user', user.id, { ipAddress }, ipAddress);

            return {
                success: true,
                message: 'Login successful',
                token,
                user: {
                    id: user.id,
                    mobileNumber: user.mobile_number,
                    fullName: user.full_name,
                    role: user.role,
                    corporateId: user.corporate_id,
                    corporateName: user.corporate_name
                }
            };
        } catch (error) {
            return {
                success: false,
                message: 'Login failed: ' + error.message
            };
        }
    }

    // Middleware to authenticate requests
    async authenticateRequest(req, res, next) {
        try {
            const token = req.headers.authorization?.replace('Bearer ', '') || 
                         req.cookies?.auth_token ||
                         req.query.token;

            if (!token) {
                return res.status(401).json({
                    success: false,
                    message: 'Authentication token required'
                });
            }

            // Verify JWT token
            const decoded = this.verifyToken(token);
            if (!decoded) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid or expired token'
                });
            }

            // Validate session in database
            const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
            const session = await this.db.validateSession(tokenHash);
            
            if (!session) {
                return res.status(401).json({
                    success: false,
                    message: 'Session expired or invalid'
                });
            }

            // Add user info to request
            req.user = {
                id: session.user_id,
                mobileNumber: session.mobile_number,
                fullName: session.full_name,
                role: session.role,
                corporateId: session.corporate_id
            };

            next();
        } catch (error) {
            return res.status(401).json({
                success: false,
                message: 'Authentication failed: ' + error.message
            });
        }
    }

    // Middleware to check admin role
    requireAdmin(req, res, next) {
        if (req.user?.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Admin access required'
            });
        }
        next();
    }

    // Check if user can access instance
    async canAccessInstance(userId, sessionId, corporateId = null) {
        try {
            // Get instance details
            const instances = await this.db.getUserInstances(userId, corporateId);
            const instance = instances.find(inst => inst.session_id === sessionId);
            
            if (!instance) {
                return false;
            }

            // Individual user can only access their own instances
            if (!corporateId && instance.user_id !== userId) {
                return false;
            }

            // Corporate user can access any instance in their corporate
            if (corporateId && instance.corporate_id !== corporateId) {
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error checking instance access:', error);
            return false;
        }
    }

    // Middleware to validate instance access
    async validateInstanceAccess(req, res, next) {
        try {
            const sessionId = req.params.sessionId || req.body.sessionId;
            
            if (!sessionId) {
                return res.status(400).json({
                    success: false,
                    message: 'Session ID required'
                });
            }

            const canAccess = await this.canAccessInstance(
                req.user.id, 
                sessionId, 
                req.user.corporateId
            );

            if (!canAccess) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this WhatsApp instance'
                });
            }

            next();
        } catch (error) {
            return res.status(500).json({
                success: false,
                message: 'Error validating instance access: ' + error.message
            });
        }
    }

    // Approve user (admin only)
    async approveUser(adminId, userId) {
        try {
            const result = await this.db.approveUser(userId, adminId);
            
            if (result.changes > 0) {
                // Log approval action
                await this.db.logAction(adminId, 'approve_user', 'user', userId, { userId }, null);
                
                return {
                    success: true,
                    message: 'User approved successfully'
                };
            } else {
                return {
                    success: false,
                    message: 'User not found or already approved'
                };
            }
        } catch (error) {
            return {
                success: false,
                message: 'Error approving user: ' + error.message
            };
        }
    }

    // Get pending users (admin only)
    async getPendingUsers() {
        try {
            const users = await this.db.getPendingUsers();
            return {
                success: true,
                users: users.map(user => ({
                    id: user.id,
                    mobileNumber: user.mobile_number,
                    fullName: user.full_name,
                    email: user.email,
                    corporateName: user.corporate_name,
                    createdAt: user.created_at
                }))
            };
        } catch (error) {
            return {
                success: false,
                message: 'Error fetching pending users: ' + error.message
            };
        }
    }

    // Logout user
    async logout(token) {
        try {
            const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
            
            // Remove session from database
            await this.db.db.run('DELETE FROM user_sessions WHERE token_hash = ?', [tokenHash]);
            
            return {
                success: true,
                message: 'Logged out successfully'
            };
        } catch (error) {
            return {
                success: false,
                message: 'Error during logout: ' + error.message
            };
        }
    }
}

module.exports = AuthManager;
