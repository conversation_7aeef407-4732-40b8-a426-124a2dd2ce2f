# WhatsApp Integration Guide

## Overview
This guide provides complete instructions for integrating WhatsApp with Odoo 17 using wwebjs.dev and mudslide for QR authentication.

## System Status
✅ **WhatsApp Service**: Running on port 3001  
✅ **Odoo 17**: Running with WhatsApp module installed  
✅ **Mudslide**: Available for QR authentication  
✅ **Views**: Odoo 17 compatible views enabled  

## Architecture Components

### 1. WhatsApp Service (Port 3001)
- **Location**: `/mnt/whatsapp/`
- **Technology**: Node.js with wwebjs.dev
- **Features**: Multiple instances, file logging, QR authentication
- **Status**: Active and running

### 2. Odoo WhatsApp Module
- **Location**: `/mnt/extra-addons/whatsapp_conversations/`
- **Models**: Instance, Conversation, Message, Template, Attachment
- **Views**: Tree, Form, Kanban views for all models
- **Integration**: XMLRPC API for communication

### 3. Mudslide QR Authentication
- **Location**: `/mnt/whatsapp/mudslide/`
- **Purpose**: Generate authentic WhatsApp QR codes
- **Output**: PNG files for Odoo integration

## Quick Start Guide

### Step 1: Access Odoo WhatsApp Module
1. Login to Odoo: http://**************:8069
2. Navigate to **Apps** → Search "WhatsApp Conversations"
3. Access via main menu: **WhatsApp** → **Instances**

### Step 2: Create WhatsApp Instance
1. Go to **WhatsApp** → **Instances** → **Create**
2. Fill required fields:
   - **Name**: Instance identifier (e.g., "Main WhatsApp")
   - **Phone Number**: WhatsApp number format (+91XXXXXXXXXX)
   - **Session ID**: Unique identifier for this instance
3. Save the instance

### Step 3: Connect Instance
1. Click **Connect** button in the instance form
2. System will generate QR code using mudslide
3. Scan QR code with WhatsApp mobile app
4. Instance status will change to "Connected"

### Step 4: Start Messaging
1. Navigate to **WhatsApp** → **Conversations**
2. Create new conversation or view existing ones
3. Send/receive messages through the interface

## API Endpoints

### WhatsApp Service (Port 3001)
```
GET  /instances                    - List all instances
POST /instances                    - Create new instance
GET  /instances/:id/qr            - Get QR code
POST /instances/:id/send          - Send message
GET  /instances/:id/conversations - Get conversations
GET  /instances/:id/messages      - Get messages
```

### Odoo XMLRPC Integration
```python
import xmlrpc.client

# Connect to Odoo
url = 'http://**************:8069'
db = 'arihantai.com'
username = 'your_username'
password = 'your_password'

common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
uid = common.authenticate(db, username, password, {})

models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')

# Create WhatsApp instance
instance_id = models.execute_kw(db, uid, password,
    'whatsapp.instance', 'create',
    [{'name': 'API Instance', 'phone_number': '+91XXXXXXXXXX'}])
```

## File Structure
```
/mnt/whatsapp/
├── app.js                 # Main WhatsApp service
├── package.json           # Node.js dependencies
├── sessions/              # WhatsApp session data
├── logs/                  # Message logs (CSV format)
├── qr_codes/             # Generated QR codes
├── mudslide/             # QR authentication tool
└── INTEGRATION_GUIDE.md  # This guide

/mnt/extra-addons/whatsapp_conversations/
├── models/               # Odoo models
├── views/                # Odoo views (Odoo 17 compatible)
├── security/             # Access rights
├── data/                 # Default data
└── __manifest__.py       # Module manifest
```

## Configuration Files

### WhatsApp Service Config
- **Port**: 3001
- **Session Storage**: `/mnt/whatsapp/sessions/`
- **Log Directory**: `/mnt/whatsapp/logs/`
- **QR Directory**: `/mnt/whatsapp/qr_codes/`

### Odoo Configuration
- **Database**: arihantai.com
- **Module**: whatsapp_conversations
- **URL**: http://**************:8069

## Troubleshooting

### Common Issues

1. **QR Code Not Generating**
   - Check mudslide installation: `/mnt/whatsapp/mudslide/`
   - Verify permissions on QR directory
   - Check WhatsApp service logs

2. **Instance Not Connecting**
   - Ensure QR code is scanned within time limit
   - Check phone number format (+91XXXXXXXXXX)
   - Verify WhatsApp app is updated

3. **Messages Not Syncing**
   - Check XMLRPC connection between services
   - Verify Odoo database connectivity
   - Check message logging in CSV files

4. **Odoo Views Not Loading**
   - Module successfully upgraded to Odoo 17 compatibility
   - All views are now properly configured
   - Access via **WhatsApp** menu in Odoo

### Log Locations
- **WhatsApp Service**: Console output and CSV logs in `/mnt/whatsapp/logs/`
- **Odoo**: `/var/log/odoo/odoo-server.log`
- **System**: `journalctl -u whatsapp-service` (if systemd service configured)

## Advanced Features

### 1. Multiple Instance Support
- Each instance has unique session ID
- Separate QR codes and authentication
- Independent message logging

### 2. Message Templates
- Pre-defined message templates
- Variable substitution support
- Usage tracking and statistics

### 3. File Attachments
- Support for images, documents, audio
- Automatic file type detection
- Secure file storage

### 4. Conversation Management
- Thread-based conversations
- Message status tracking
- Read receipts and delivery status

## Security Considerations

1. **Session Security**
   - Sessions stored locally in `/mnt/whatsapp/sessions/`
   - Regular session cleanup recommended
   - Secure file permissions

2. **API Security**
   - XMLRPC authentication required
   - Rate limiting on API endpoints
   - Input validation and sanitization

3. **Data Privacy**
   - Message logs in CSV format
   - No cross-instance data sharing
   - Configurable data retention

## Next Steps

1. **Test Integration**: Create test instance and verify connectivity
2. **Configure Templates**: Set up message templates for common use cases
3. **Setup Automation**: Configure automated responses and workflows
4. **Monitor Performance**: Set up logging and monitoring
5. **Scale Deployment**: Add more instances as needed

## Support

For technical support:
1. Check logs in respective directories
2. Verify service status: `systemctl status odoo`
3. Test API endpoints manually
4. Review Odoo module configuration

## Version Information
- **Odoo**: 17.0
- **WhatsApp Service**: Node.js with wwebjs.dev
- **Module Version**: 1.0.0
- **Last Updated**: 2025-06-17
