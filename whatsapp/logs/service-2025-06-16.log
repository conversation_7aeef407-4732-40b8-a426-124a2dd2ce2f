{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T00:00:00.780Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:00:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:00:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:00:37.643Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:01:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:01:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:01:37.644Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:01:57.645Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:02:17.649Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:02:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:02:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:02:37.652Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:02:57.655Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:03:19.588Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:03:19.592Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:04:08.626Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:04:19.590Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:04:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:05:08.631Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:05:19.592Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:05:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:05:28.637Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:05:48.639Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:06:08.645Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:06:19.587Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:06:19.589Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:06:28.645Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:07:19.592Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:07:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:07:39.866Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:08:19.590Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:08:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:08:39.866Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:08:59.867Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:09:19.586Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:09:19.587Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:09:19.868Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:09:39.871Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:09:59.870Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:10:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:10:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:11:11.047Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:11:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:11:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:12:11.049Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:12:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:12:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:12:31.055Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:12:51.058Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:13:11.056Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:13:19.593Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:13:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:13:31.061Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:14:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:14:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:14:41.991Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:15:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:15:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:15:41.997Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:16:02.004Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:16:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:16:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:16:22.004Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:16:42.007Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:17:02.012Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:17:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:17:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:18:13.163Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:18:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:18:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:19:13.165Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:19:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:19:19.598Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:19:33.169Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:19:53.174Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:20:13.175Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:20:19.591Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:20:19.592Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:20:33.179Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:21:19.593Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:21:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:21:44.269Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:22:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:22:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:22:44.276Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:23:04.278Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:23:19.598Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:23:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:23:24.282Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:23:44.293Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:24:04.294Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:24:19.598Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:24:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:25:15.158Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:25:19.590Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:25:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:26:15.165Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:26:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:26:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:26:35.171Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:26:55.177Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:27:15.184Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:27:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:27:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:27:35.182Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:28:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:28:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:28:46.117Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:29:19.598Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:29:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:29:46.122Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:30:06.122Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:30:19.594Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:30:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:30:26.123Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:30:46.132Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:31:06.129Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:31:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:31:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:32:17.558Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:32:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:32:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:33:17.556Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:33:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:33:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:33:37.561Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:33:57.566Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:34:17.570Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:34:19.594Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:34:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:34:37.575Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:35:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:35:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:35:49.193Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:36:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:36:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:36:49.202Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:37:09.208Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:37:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:37:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:37:29.218Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:37:49.212Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:38:09.217Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:38:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:38:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:39:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:39:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:39:20.778Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:40:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:40:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:40:20.781Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:40:40.785Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:41:00.790Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:41:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:41:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:41:20.856Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:41:40.797Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:42:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:42:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:42:51.931Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:43:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:43:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:43:51.933Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:44:11.937Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:44:19.592Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:44:19.594Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:44:31.938Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:44:51.943Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:45:11.946Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:45:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:45:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:46:19.593Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:46:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:46:22.855Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:47:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:47:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:47:22.858Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:47:42.865Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:48:02.873Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:48:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:48:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:48:22.871Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:48:42.876Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:49:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:49:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:49:53.728Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:50:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:50:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:50:53.732Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:51:13.734Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:51:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:51:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:51:33.737Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:51:53.738Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:52:13.743Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:52:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:52:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:53:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:53:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:53:24.968Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:54:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:54:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:54:24.974Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:54:44.978Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:55:04.983Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:55:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:55:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:55:24.985Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:55:44.990Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:56:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:56:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:56:56.285Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:57:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:57:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:57:56.286Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:58:16.285Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:58:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:58:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:58:36.289Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:58:56.296Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T00:59:16.294Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T00:59:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T00:59:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T01:00:00.860Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:00:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:00:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:00:27.372Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:01:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:01:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:01:27.377Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:01:47.382Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:02:07.383Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:02:19.594Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:02:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:02:27.386Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:02:47.386Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:03:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:03:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:03:58.564Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:04:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:04:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:04:58.569Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:05:18.576Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:05:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:05:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:05:38.577Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:05:58.582Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:06:18.583Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:06:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:06:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:07:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:07:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:07:29.682Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:08:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:08:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:08:29.686Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:08:49.695Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:09:09.692Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:09:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:09:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:09:29.695Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:09:49.701Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:10:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:10:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:11:00.958Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:11:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:11:19.608Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:12:00.963Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:12:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:12:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:12:20.969Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:12:40.975Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:13:00.983Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:13:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:13:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:13:20.988Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:14:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:14:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:14:32.120Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:15:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:15:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:15:32.131Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:15:52.135Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:16:12.144Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:16:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:16:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:16:32.140Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:16:52.145Z"}
{"ip":"::ffff:************","level":"info","message":"GET /","timestamp":"2025-06-16T01:17:04.872Z","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:17:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:17:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:18:03.494Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:18:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:18:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:19:03.495Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:19:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:19:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:19:23.499Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:19:43.505Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:20:03.503Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:20:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:20:19.598Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:20:23.504Z"}
{"ip":"::ffff:************","level":"info","message":"GET /","timestamp":"2025-06-16T01:20:32.840Z","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:21:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:21:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:21:34.486Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:22:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:22:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:22:34.488Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:22:54.492Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:23:14.494Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:23:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:23:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:23:34.500Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:23:54.510Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:24:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:24:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:25:05.548Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:25:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:25:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:26:05.550Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:26:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:26:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:26:25.558Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:26:45.562Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:27:05.568Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:27:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:27:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:27:25.573Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:28:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:28:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:28:37.162Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:29:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:29:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:29:37.171Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:29:57.171Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:30:17.177Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:30:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:30:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:30:37.181Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:30:57.182Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:31:19.598Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:31:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:32:08.270Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:32:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:32:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:33:08.272Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:33:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:33:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:33:28.280Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:33:48.277Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:34:08.282Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:34:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:34:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:34:28.284Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:35:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:35:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:35:39.158Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:36:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:36:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:36:39.161Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:36:59.165Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:37:19.164Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:37:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:37:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:37:39.165Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:37:59.169Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:38:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:38:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:39:10.268Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:39:19.608Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:39:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:40:10.274Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:40:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:40:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:40:30.275Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:40:50.280Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:41:10.284Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:41:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:41:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:41:30.289Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:42:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:42:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:42:42.093Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:43:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:43:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:43:42.095Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:44:02.096Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:44:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:44:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:44:22.102Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:44:42.107Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:45:02.111Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:45:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:45:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:46:13.334Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:46:19.597Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:46:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:47:13.335Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:47:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:47:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:47:33.336Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:47:53.340Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:48:13.342Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:48:19.603Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:48:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:48:33.347Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:49:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:49:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:49:44.439Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:50:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:50:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:50:44.444Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:51:04.449Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:51:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:51:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:51:24.456Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:51:44.460Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:52:04.460Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:52:19.596Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:52:19.600Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:53:15.912Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:53:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:53:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:54:15.920Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:54:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:54:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:54:35.922Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:54:55.928Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:55:15.930Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:55:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:55:19.608Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:55:35.934Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:56:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:56:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:56:47.027Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:57:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:57:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:57:47.039Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:58:07.041Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:58:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:58:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:58:27.048Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:58:47.051Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T01:59:07.051Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T01:59:19.602Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T01:59:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T02:00:00.935Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:00:17.901Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:00:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:00:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:01:17.906Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:01:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:01:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:01:37.909Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:01:57.908Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:02:17.914Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:02:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:02:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:02:37.917Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:03:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:03:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:03:49.044Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:04:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:04:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:04:49.050Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:05:09.048Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:05:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:05:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:05:29.054Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:05:48.236Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:05:49.058Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:06:09.062Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:06:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:06:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:07:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:07:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:07:20.284Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:08:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:08:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:08:20.289Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:08:40.296Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:09:00.299Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:09:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:09:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:09:20.300Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:09:40.304Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:09:52.212Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:10:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:10:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:10:51.298Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:11:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:11:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:11:51.301Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:12:11.302Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:12:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:12:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:12:31.306Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:12:51.310Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:13:11.308Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:13:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:13:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:14:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:14:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:14:22.170Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:15:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:15:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:15:22.177Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:15:42.178Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:16:02.184Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:16:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:16:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:16:22.187Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:16:42.188Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:17:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:17:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:17:53.382Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:18:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:18:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:18:53.387Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:19:13.390Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:19:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:19:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:19:33.394Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:19:53.399Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:20:13.402Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:20:19.593Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:20:20.384Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:21:19.599Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:21:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:21:24.363Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:22:19.928Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:22:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:22:24.365Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:22:44.369Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:23:04.373Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:23:19.592Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:23:19.595Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:23:24.373Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:23:44.373Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:24:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:24:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:24:55.499Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:25:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:25:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:25:55.502Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:26:15.505Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:26:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:26:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:26:35.505Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:26:55.510Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:27:15.517Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:27:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:27:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:28:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:28:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:28:26.648Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:29:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:29:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:29:26.650Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:29:46.652Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:30:06.652Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:30:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:30:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:30:26.654Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:30:46.657Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:31:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:31:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:31:30.197Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:31:30.970Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:31:58.469Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:32:19.608Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:32:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:32:58.476Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:33:18.481Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:33:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:33:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:33:38.481Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:33:58.488Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:34:18.491Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:34:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:34:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:35:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:35:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:35:29.554Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:36:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:36:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:36:29.560Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:36:49.562Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:37:09.562Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:37:19.604Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:37:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:37:29.568Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:37:49.570Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:38:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:38:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:39:00.646Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:39:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:39:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:40:00.651Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:40:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:40:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:40:20.655Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:40:40.660Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:41:00.660Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:41:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:41:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:41:20.665Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:42:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:42:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:42:31.813Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:43:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:43:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:43:31.815Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:43:51.819Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:44:11.824Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:44:19.607Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:44:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:44:31.825Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:44:51.830Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:45:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:45:19.629Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:46:02.830Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:46:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:46:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:47:02.836Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:47:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:47:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:47:22.838Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:47:42.838Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:48:02.843Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:48:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:48:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:48:22.849Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:49:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:49:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:49:33.722Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:50:12.303Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:50:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:50:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:50:33.724Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:50:49.202Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:50:53.728Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:51:13.732Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:51:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:51:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:51:33.737Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:51:43.488Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:51:53.737Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:52:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:52:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:53:04.686Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:53:19.606Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:53:19.608Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:53:25.670Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:54:04.690Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:54:19.605Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:54:19.608Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:54:24.694Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:54:44.695Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:55:04.699Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:55:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:55:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:55:24.707Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:56:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:56:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:56:35.846Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:57:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:57:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:57:35.848Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:57:55.849Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:58:15.853Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:58:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:58:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:58:35.861Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T02:58:55.861Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T02:59:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T02:59:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T02:59:34.661Z"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T03:00:00.059Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:00:07.122Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:00:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:00:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:01:07.128Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:01:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:01:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:01:27.132Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:01:47.135Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:02:07.140Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:02:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:02:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:02:27.141Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:03:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:03:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:03:38.270Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:04:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:04:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:04:38.271Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:04:58.276Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:05:18.279Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:05:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:05:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:05:38.289Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:05:58.290Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:06:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:06:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:07:04.940Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:07:09.501Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:07:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:07:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:08:09.509Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:08:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:08:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:08:29.514Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:08:49.523Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:09:09.524Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:09:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:09:19.629Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:09:29.524Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:10:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:10:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:10:40.614Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:11:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:11:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:11:40.618Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:12:00.626Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:12:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:12:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:12:20.629Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:12:29.172Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:12:40.630Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:13:00.633Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:13:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:13:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:14:11.677Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:14:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:14:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:15:11.683Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:15:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:15:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:15:31.690Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:15:51.688Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:16:11.693Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:16:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:16:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:16:31.698Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:17:05.363Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:17:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:17:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:17:42.832Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:18:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:18:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:18:42.841Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:18:51.283Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:19:02.835Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:19:18.855Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:19:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:19:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:19:22.840Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:19:42.845Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:20:02.849Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:20:19.610Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:20:19.612Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:21:14.053Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:21:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:21:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:22:14.056Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:22:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:22:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:22:34.056Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:22:54.061Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:23:14.062Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:23:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:23:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:23:34.068Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:24:18.595Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:24:19.147Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:24:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:24:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:24:45.215Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:24:56.891Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:24:57.721Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:25:01.128Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:25:02.902Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:25:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:25:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:25:38.716Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:25:39.783Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:25:45.218Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:26:05.224Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:26:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:26:19.611Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:26:25.229Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:26:45.234Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:27:05.238Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:27:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:27:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:28:16.071Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:28:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:28:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:29:16.075Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:29:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:29:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:29:36.076Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:29:56.079Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:30:16.080Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:30:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:30:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:30:36.083Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:31:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:31:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:31:46.956Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:32:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:32:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:32:46.963Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:33:06.966Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:33:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:33:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:33:26.971Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:33:46.979Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:34:06.983Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:34:14.089Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:34:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:34:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:34:35.121Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:35:18.230Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:35:19.136Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:35:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:35:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:35:19.862Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:35:47.184Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:36:18.233Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:36:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:36:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:36:38.233Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:36:58.238Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:37:18.241Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:37:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:37:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:37:38.246Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:38:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:38:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:38:49.095Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:39:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:39:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:39:24.963Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:39:49.098Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T03:40:06.127Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:40:09.102Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:40:19.613Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:40:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:40:29.107Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:40:49.113Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:41:09.117Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:41:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:41:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:42:19.601Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:42:19.614Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:42:20.531Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:43:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:43:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:43:20.539Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:43:40.549Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:44:00.547Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:44:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:44:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:44:20.548Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:44:40.552Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:45:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:45:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:45:51.383Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:46:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:46:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:46:51.383Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:47:11.384Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:47:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:47:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:47:31.384Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:47:51.385Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:48:11.389Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:48:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:48:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:49:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:49:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:49:22.585Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:50:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:50:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:50:22.593Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:50:42.607Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:51:02.601Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:51:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:51:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:51:22.602Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:51:42.608Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:52:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:52:19.629Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:52:53.437Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:53:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:53:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:53:53.440Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:54:13.444Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:54:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:54:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:54:33.446Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:54:53.452Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:55:13.457Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:55:19.616Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:55:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:56:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:56:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:56:24.333Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:57:19.609Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:57:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:57:24.338Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:57:44.342Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:58:04.342Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:58:19.615Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:58:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:58:24.343Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:58:44.348Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T03:59:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T03:59:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T03:59:55.472Z"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T04:00:00.154Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:00:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:00:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:00:55.481Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:01:15.482Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:01:19.617Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:01:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:01:35.483Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:01:55.487Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:02:15.491Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:02:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:02:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T04:02:51.125Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:03:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:03:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:03:27.659Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:04:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:04:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:04:27.666Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:04:47.671Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:05:07.675Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:05:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:05:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:05:27.679Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:05:47.684Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:06:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:06:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:06:58.617Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:07:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:07:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:07:58.621Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:08:18.623Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:08:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:08:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:08:38.625Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:08:58.627Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:09:18.632Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:09:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:09:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:10:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:10:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:10:29.913Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:11:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:11:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:11:29.926Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:11:49.923Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:12:09.927Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:12:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:12:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:12:29.930Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:12:49.937Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:13:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:13:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:14:00.855Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:14:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:14:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:15:00.860Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:15:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:15:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:15:20.866Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:15:40.870Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:16:00.874Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:16:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:16:19.629Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:16:20.875Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:17:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:17:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:17:31.999Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:18:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:18:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:18:32.007Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:18:52.012Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:19:12.008Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:19:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:19:19.629Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:19:32.013Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:19:52.016Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:20:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:20:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:21:03.301Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:21:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:21:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:22:03.307Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:22:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:22:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:22:23.308Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:22:43.313Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:23:03.312Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:23:19.619Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:23:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:23:23.317Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:23:39.483Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:23:58.629Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:24:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:24:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:24:34.215Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:25:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:25:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:25:34.231Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:25:54.227Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:26:14.230Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:26:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:26:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:26:34.232Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:26:54.240Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:27:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:27:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:27:42.343Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:28:05.398Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:28:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:28:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:29:05.403Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:29:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:29:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:29:25.404Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:29:45.408Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:30:05.409Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:30:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:30:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:30:25.416Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:31:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:31:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:31:36.480Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:31:44.160Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:31:55.718Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:32:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:32:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:32:26.593Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:32:36.482Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:32:36.675Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:32:56.483Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:33:16.488Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:33:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:33:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:33:36.492Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:33:56.497Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:34:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:34:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:34:22.731Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:35:07.583Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:35:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:35:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:35:52.228Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:36:07.584Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:36:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:36:19.629Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:36:27.584Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:36:35.617Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:36:47.587Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T04:36:58.209Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:37:07.590Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:37:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:37:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:37:27.595Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /","timestamp":"2025-06-16T04:38:05.280Z","userAgent":"Mozilla/5.0 (compatible; InternetMeasurement/1.0; +https://internet-measurement.com/)"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:38:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:38:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:38:38.640Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:39:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:39:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:39:38.641Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:39:58.653Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:40:18.656Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:40:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:40:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:40:38.663Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T04:40:46.645Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:40:58.664Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:41:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:41:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:42:09.787Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:42:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:42:19.626Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:43:09.789Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:43:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:43:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:43:29.792Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:43:49.795Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:44:09.800Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:44:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:44:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:44:29.804Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:45:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:45:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T04:45:21.277Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:45:40.796Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:46:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:46:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:46:40.801Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:47:00.808Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:47:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:47:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:47:20.810Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:47:40.811Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:48:00.818Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:48:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:48:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:49:11.985Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:49:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:49:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:50:11.989Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:50:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:50:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:50:31.996Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:50:52.001Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:51:12.004Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:51:19.624Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:51:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:51:32.004Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:52:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:52:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:52:43.159Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:53:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:53:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:53:43.167Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:54:03.170Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:54:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:54:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:54:23.170Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:54:43.176Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:55:03.176Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:55:19.618Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:55:19.622Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:56:14.035Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:56:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:56:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:57:14.064Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:57:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:57:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:57:34.057Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:57:54.062Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:58:14.065Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:58:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:58:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:58:34.068Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T04:59:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T04:59:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T04:59:45.242Z"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T05:00:00.415Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:00:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:00:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:00:45.242Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:01:05.246Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T05:01:17.987Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T05:01:18.463Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T05:01:18.987Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:01:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:01:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:01:25.248Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:01:45.255Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:02:05.257Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:02:19.621Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:02:19.623Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:03:16.354Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:03:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:03:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:04:16.363Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:04:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:04:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:04:36.361Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:04:56.363Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:05:16.365Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:05:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:05:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:05:36.366Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:06:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:06:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:06:47.649Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:07:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:07:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:07:47.650Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:08:07.654Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:08:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:08:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:08:27.655Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:08:47.659Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:09:07.658Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:09:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:09:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:10:18.759Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:10:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:10:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:11:18.763Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:11:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:11:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:11:38.765Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:11:58.766Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:12:18.769Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:12:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:12:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:12:38.775Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:13:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:13:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:13:49.961Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:14:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:14:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:14:49.964Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:15:09.970Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:15:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:15:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:15:29.970Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:15:49.975Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:16:09.976Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:16:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:16:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:17:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:17:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:17:21.136Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:18:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:18:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:18:21.142Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:18:41.146Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:19:01.147Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:19:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:19:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:19:21.157Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:19:41.154Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:20:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:20:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:20:52.454Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:21:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:21:19.633Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:21:52.457Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:22:12.465Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:22:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:22:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:22:32.468Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:22:52.472Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:23:12.470Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:23:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:23:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:24:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:24:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:24:23.620Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:25:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:25:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:25:23.628Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:25:43.634Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:26:03.634Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:26:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:26:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:26:23.638Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:26:43.639Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:27:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:27:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:27:54.667Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:28:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:28:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:28:54.670Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:29:14.673Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:29:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:29:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:29:34.676Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:29:54.680Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:30:14.682Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:30:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:30:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:31:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:31:19.668Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:31:25.739Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:32:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:32:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:32:25.745Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:32:45.750Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:33:05.753Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:33:19.625Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:33:19.627Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:33:25.753Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:33:45.754Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:34:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:34:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:34:56.997Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:35:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:35:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:35:56.998Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:36:17.002Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:36:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:36:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:36:37.006Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:36:57.016Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:37:17.013Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:37:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:37:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:38:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:38:19.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:38:28.471Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:39:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:39:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:39:28.477Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:39:48.478Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:40:08.484Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:40:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:40:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:40:28.488Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:40:48.489Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:41:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:41:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:41:59.361Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:42:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:42:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:42:59.359Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:43:19.360Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:43:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:43:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:43:39.366Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:43:59.367Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:44:19.368Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:44:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:44:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:45:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:45:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:45:30.235Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:46:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:46:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:46:30.243Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:46:50.240Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:47:10.245Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:47:19.628Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:47:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:47:30.251Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:47:50.250Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:48:19.630Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:48:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:49:01.385Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:49:19.834Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:49:20.033Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:50:01.386Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:50:19.620Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:50:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:50:21.395Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:50:41.394Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:51:01.396Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:51:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:51:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:51:21.400Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:52:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:52:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:52:32.669Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:53:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:53:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:53:32.670Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:53:52.672Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:54:12.671Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:54:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:54:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:54:32.677Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:54:52.677Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:55:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:55:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:56:04.518Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:56:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:56:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:57:04.523Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:57:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:57:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:57:24.527Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:57:44.528Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:58:04.529Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:58:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:58:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:58:24.529Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T05:59:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T05:59:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T05:59:35.435Z"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T06:00:00.482Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:00:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:00:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:00:35.448Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:00:55.456Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:01:15.451Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:01:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:01:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:01:35.458Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:01:55.461Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:02:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:02:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:03:06.548Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:03:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:03:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:04:06.552Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:04:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:04:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:04:26.557Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:04:46.562Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:05:06.567Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:05:19.631Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:05:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:05:26.568Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:06:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:06:19.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:06:37.751Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:07:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:07:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:07:37.758Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:07:57.764Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:08:17.765Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:08:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:08:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:08:37.771Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:08:57.775Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:09:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:09:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:10:09.150Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:10:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:10:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:10:31.922Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:10:35.726Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:11:09.154Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:11:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:11:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:11:29.159Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:11:49.162Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:12:09.164Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:12:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:12:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:12:29.165Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:13:10.681Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:13:19.634Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:13:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:13:40.365Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:14:19.632Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:14:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:14:40.378Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:15:00.373Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:15:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:15:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:15:20.374Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:15:40.380Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:16:00.387Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:16:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:16:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:17:11.821Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:17:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:17:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:18:11.823Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:18:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:18:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:18:31.829Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:18:51.834Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:19:11.839Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:19:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:19:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:19:31.848Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:20:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:20:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:20:42.778Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:21:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:21:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:21:42.785Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:22:02.789Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:22:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:22:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:22:22.789Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:22:42.795Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:23:02.795Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:23:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:23:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:24:13.749Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:24:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:24:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:25:13.756Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:25:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:25:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:25:33.767Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:25:53.770Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:26:13.771Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:26:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:26:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:26:33.774Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:27:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:27:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:27:45.033Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:27:47.478Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:27:58.326Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:28:17.294Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:28:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:28:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:28:33.390Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:28:45.033Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:29:05.037Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:29:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:29:19.636Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:29:23.745Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:29:25.042Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:29:45.046Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:30:05.050Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:30:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:30:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:31:16.187Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:31:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:31:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:32:16.191Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:32:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:32:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:32:36.196Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:32:56.202Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:33:16.200Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:33:19.637Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:33:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:33:36.204Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T06:33:54.636Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:34:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:34:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:34:47.203Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:35:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:35:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:35:47.208Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:36:07.209Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:36:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:36:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:36:27.213Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:36:47.215Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:37:07.216Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:37:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:37:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:38:18.113Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:38:19.641Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:38:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:39:18.119Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:39:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:39:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:39:38.120Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:39:58.127Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:40:18.131Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:40:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:40:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:40:38.136Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:41:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:41:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:41:49.466Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:42:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:42:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:42:49.473Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:43:09.483Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:43:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:43:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:43:29.484Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:43:49.484Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:44:09.484Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:44:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:44:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:45:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:45:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:45:20.707Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:46:19.635Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:46:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:46:20.712Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:46:40.713Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:47:00.719Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:47:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:47:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:47:20.718Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:47:40.729Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:48:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:48:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:48:51.847Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:49:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:49:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:49:51.856Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:50:11.856Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:50:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:50:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:50:31.859Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:50:51.865Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:51:11.866Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:51:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:51:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:52:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:52:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:52:23.013Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:53:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:53:19.673Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:53:23.021Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:53:43.030Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:54:03.028Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:54:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:54:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:54:23.029Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:54:43.032Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:55:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:55:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:55:54.232Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:56:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:56:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:56:54.234Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:57:14.235Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:57:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:57:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:57:34.239Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:57:54.244Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:58:14.249Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:58:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:58:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T06:58:59.831Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T06:59:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T06:59:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T06:59:25.399Z"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T07:00:00.739Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:00:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:00:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:00:25.403Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:00:45.405Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:01:05.409Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:01:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:01:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:01:25.411Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:01:45.417Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:02:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:02:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:02:56.478Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:03:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:03:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:03:56.476Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:04:16.478Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:04:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:04:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:04:36.478Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:04:56.484Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:05:16.481Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:05:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:05:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:06:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:06:19.665Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:06:27.468Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:07:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:07:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:07:27.470Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:07:47.472Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:08:07.477Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:08:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:08:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:08:27.478Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:08:47.485Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:09:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:09:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:09:58.576Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:10:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:10:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:10:58.578Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:11:18.582Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:11:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:11:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:11:38.584Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:11:47.596Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:11:58.588Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:12:18.589Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:12:19.638Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:12:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:13:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:13:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:13:29.824Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:14:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:14:19.670Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:14:29.826Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:14:49.828Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:15:09.836Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:15:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:15:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:15:29.834Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:15:49.836Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:16:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:16:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:17:01.105Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:17:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:17:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:18:01.110Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:18:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:18:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:18:21.112Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:18:41.113Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:19:01.118Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:19:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:19:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:19:21.122Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:20:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:20:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:20:32.995Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:21:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:21:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:21:32.998Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:21:53.009Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:22:13.006Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:22:19.639Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:22:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:22:33.010Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:22:53.011Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:23:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:23:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:24:04.206Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:24:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:24:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:25:04.213Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:25:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:25:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:25:24.210Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:25:44.223Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:26:04.223Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:26:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:26:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:26:24.227Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:27:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:27:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:27:35.298Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:28:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:28:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:28:35.302Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:28:55.309Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:29:15.312Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:29:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:29:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:29:35.312Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:29:55.317Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:30:19.640Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:30:19.643Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:31:06.171Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:31:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:31:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:32:06.176Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:32:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:32:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:32:26.179Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:32:34.087Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:32:46.183Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:33:06.189Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:33:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:33:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:33:26.193Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:34:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:34:19.671Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:34:37.309Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:35:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:35:19.672Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:35:37.334Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:35:57.323Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:36:17.331Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:36:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:36:19.683Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:36:37.339Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:36:57.348Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:37:19.642Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:37:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:38:08.293Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:38:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:38:19.678Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:39:08.300Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:39:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:39:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:39:28.302Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:39:48.306Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:40:08.311Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:40:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:40:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:40:20.019Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:40:28.314Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:41:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:41:19.673Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:41:39.599Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:42:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:42:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:42:39.603Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:42:59.608Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:43:19.610Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:43:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:43:19.671Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:43:39.614Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:43:59.632Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:06.600Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:06.873Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:10.558Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:12.197Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:19.462Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:44:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:44:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:21.460Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:23.137Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:25.942Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:36.054Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:40.845Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:45.509Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:47.168Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:49.900Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:51.590Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:44:57.346Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:45:10.599Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:45:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:45:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:46:10.611Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:46:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:46:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:46:30.609Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:46:35.374Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:46:38.047Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:46:40.109Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:46:40.547Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:46:41.347Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T07:46:44.947Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:46:50.614Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:47:10.618Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:47:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:47:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:47:30.620Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:48:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:48:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:48:41.896Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:49:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:49:19.673Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:49:41.904Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:50:01.916Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:50:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:50:19.671Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:50:21.921Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:50:41.923Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:51:01.928Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:51:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:51:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:52:12.904Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:52:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:52:19.665Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:53:12.908Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:53:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:53:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:53:32.909Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:53:52.917Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:54:12.917Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:54:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:54:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:54:32.922Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:55:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:55:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:55:44.203Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:56:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:56:19.674Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:56:44.208Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:57:04.213Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:57:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:57:19.685Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:57:24.216Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:57:44.221Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:58:04.222Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:58:19.665Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:58:19.671Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T07:59:15.122Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T07:59:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T07:59:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T08:00:00.461Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:00:15.128Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:00:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:00:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:00:35.133Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:00:55.135Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:01:15.138Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:01:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:01:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:01:35.142Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:02:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:02:19.674Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /","timestamp":"2025-06-16T08:02:43.472Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.117 Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:02:46.288Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:03:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:03:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:03:46.298Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:04:06.300Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:04:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:04:19.675Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:04:26.302Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:04:46.306Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:05:06.314Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:05:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:05:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:06:17.702Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:06:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:06:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:07:17.714Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:07:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:07:19.677Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:07:37.722Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:07:57.724Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:08:17.724Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:08:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:08:19.668Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:08:37.728Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:09:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:09:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:09:49.001Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:10:19.667Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:10:19.675Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:10:49.013Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:11:09.012Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:11:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:11:19.668Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:11:29.013Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:11:49.019Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:12:09.024Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:12:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:12:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:13:19.678Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:13:19.709Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:13:20.367Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:14:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:14:19.670Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:14:20.368Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:14:40.376Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:15:00.374Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:15:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:15:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:15:20.378Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:15:40.388Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:16:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:16:19.670Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:16:51.567Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:17:19.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:17:19.676Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:17:51.572Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:18:11.578Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:18:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:18:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:18:31.580Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:18:51.586Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:19:11.594Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:19:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:19:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:20:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:20:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:20:22.777Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:21:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:21:19.665Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:21:22.786Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:21:42.792Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:22:02.794Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:22:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:22:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:22:22.795Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:22:42.801Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:23:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:23:19.672Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:23:54.159Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:24:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:24:19.669Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:24:54.171Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:25:14.175Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:25:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:25:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:25:34.183Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:25:54.185Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:26:14.248Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:26:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:26:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:27:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:27:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:27:25.339Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:28:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:28:19.669Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:28:25.347Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:28:45.347Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:29:05.350Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:29:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:29:19.674Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:29:25.356Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:29:45.362Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:30:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:30:19.681Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:30:56.341Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:31:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:31:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:31:56.343Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:32:16.353Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:32:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:32:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:32:36.366Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:32:56.361Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:33:16.361Z"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:33:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:33:19.672Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /health","timestamp":"2025-06-16T08:34:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************28","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:34:19.684Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:34:27.636Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:35:27.641Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:35:47.641Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:36:07.642Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:36:27.643Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:36:47.643Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:37:58.785Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:38:58.784Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:39:18.785Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:39:38.785Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:39:58.789Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:40:18.794Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:41:30.138Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:42:30.138Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:42:50.142Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:43:10.146Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:43:30.152Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:43:50.152Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-16T08:44:46.558Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:45:01.336Z"}
{"body":{"message":"Kya aap interview ke liye kal aa sakte hain?","to":"<EMAIL>","type":"text"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/send","timestamp":"2025-06-16T08:45:04.132Z","userAgent":"axios/1.9.0"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T08:45:04.142Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T08:45:04.568Z"}
{"body":{"message":"12 baje aa sakte hain! Interview ki taiyaariyan chal rahi hain.","to":"<EMAIL>","type":"text"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/send","timestamp":"2025-06-16T08:45:19.410Z","userAgent":"axios/1.9.0"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T08:45:19.419Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:45:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:45:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T08:45:19.843Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T08:45:30.094Z"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T08:45:46.591Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:46:01.340Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:46:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:46:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:46:21.340Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:46:41.345Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:47:01.349Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:47:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:47:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:47:21.354Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:48:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:48:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:48:32.372Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:49:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:49:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:49:32.372Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:49:52.376Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:50:12.380Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:50:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:50:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:50:32.385Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:50:52.386Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:51:19.645Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:51:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:52:03.209Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:52:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:52:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:53:03.213Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:53:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:53:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:53:23.214Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:53:43.215Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:54:03.219Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:54:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:54:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:54:23.223Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:55:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:55:19.647Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:55:34.290Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:56:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:56:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:56:34.296Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:56:54.299Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:57:14.300Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:57:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:57:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:57:34.305Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:57:54.309Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:58:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:58:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T08:59:05.336Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T08:59:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T08:59:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T09:00:00.848Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:00:05.337Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:00:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:00:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:00:25.337Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:00:45.339Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:01:05.339Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:01:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:01:19.667Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:01:25.344Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:02:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:02:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:02:36.364Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:03:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:03:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:03:36.366Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:03:56.368Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:04:16.372Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:04:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:04:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:04:36.377Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:04:56.379Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:05:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:05:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:06:07.407Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:06:19.697Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:06:19.703Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:07:07.413Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:07:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:07:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:07:27.417Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:07:47.418Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:08:07.423Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:08:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:08:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:08:27.426Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:09:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:09:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:09:38.578Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:10:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:10:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:10:38.587Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:10:58.589Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:11:18.593Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:11:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:11:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:11:38.597Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:11:58.598Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:12:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:12:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-16T09:12:30.915Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:13:09.440Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:13:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:13:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:14:09.445Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:14:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:14:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:14:29.450Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:14:49.451Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:15:09.454Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:15:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:15:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:15:29.459Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:16:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:16:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:16:40.343Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:17:19.665Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:17:19.668Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:17:40.344Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:18:00.345Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:18:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:18:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:18:20.345Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:18:40.349Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:19:00.350Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:19:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:19:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:20:11.428Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:20:19.644Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:20:19.646Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:21:11.428Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:21:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:21:19.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:21:31.428Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:21:51.433Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:22:11.437Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:22:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:22:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:22:31.438Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:23:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:23:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:23:42.365Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:24:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:24:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:24:42.366Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:25:02.366Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:25:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:25:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:25:22.370Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:25:42.371Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:26:02.375Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:26:19.649Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:26:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:27:13.618Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:27:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:27:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:28:13.622Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:28:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:28:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:28:33.627Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:28:53.627Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:29:13.632Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:29:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:29:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:29:33.636Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:30:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:30:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:30:44.714Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:31:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:31:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:31:44.719Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:32:04.720Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:32:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:32:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:32:24.722Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:32:44.726Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:33:04.730Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:33:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:33:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:34:16.002Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:34:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:34:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:35:16.008Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:35:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:35:19.664Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:35:36.012Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:35:56.016Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:36:16.020Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:36:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:36:19.661Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:36:36.021Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:37:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:37:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:37:46.916Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:38:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:38:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:38:46.916Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:39:06.921Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:39:19.648Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:39:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:39:26.925Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:39:46.925Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:40:06.926Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:40:19.650Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:40:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:41:17.769Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:41:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:41:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:42:17.769Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:42:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:42:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:42:37.769Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:42:57.773Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:43:17.774Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:43:19.651Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:43:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:43:37.776Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:44:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:44:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:44:48.826Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:45:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:45:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:45:48.827Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:46:08.827Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:46:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:46:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:46:28.839Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:46:48.837Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:47:08.838Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:47:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:47:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:48:19.652Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:48:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:48:19.967Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:49:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:49:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:49:19.971Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:49:39.972Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:49:59.976Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:50:19.656Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:50:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:50:19.981Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:50:39.983Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:51:19.667Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:51:19.670Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:51:51.134Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:52:19.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:52:19.665Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:52:51.140Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:53:11.140Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:53:19.659Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:53:19.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:53:31.144Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:53:51.145Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:54:11.149Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:54:19.660Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:54:19.663Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:55:19.657Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:55:19.658Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:55:22.260Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:56:19.654Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:56:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:56:22.265Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:56:42.266Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:57:02.267Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:57:19.653Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:57:19.655Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:57:22.271Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:57:42.278Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:58:19.662Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:58:19.666Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:58:53.474Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T09:59:19.707Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T09:59:19.739Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T09:59:53.531Z"}
{"level":"info","message":"Session stats - Active: 1/5, QR Codes: 1","timestamp":"2025-06-16T10:00:01.005Z"}
{"level":"info","message":"QR Code generated for session: test123","timestamp":"2025-06-16T10:00:13.594Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /instances","timestamp":"2025-06-16T10:00:19.675Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:*************","level":"info","message":"GET /health","timestamp":"2025-06-16T10:00:19.690Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
