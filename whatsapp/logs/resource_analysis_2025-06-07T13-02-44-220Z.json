{"system": {"platform": "linux", "architecture": "arm64", "hostname": "erp17-projects", "uptime": 4396, "nodeVersion": "v20.18.0", "totalMemory": 11.65, "cpuCores": 2, "cpuModel": "Neoverse-N1"}, "memory": {"total": 11.65, "used": 5, "free": 6.65, "usagePercent": 43, "available": 6.65}, "cpu": {"cores": 2, "model": "Neoverse-N1", "speed": 0, "architecture": "arm64", "load": {"1min": 0.06, "5min": 0.2, "15min": 0.24}}, "disk": {"filesystem": "/dev/sda1", "size": "97G", "used": "36G", "available": "62G", "usagePercent": 37, "mountPoint": "/"}, "network": {"interfaces": 2, "hasIPv4": true, "hasIPv6": true, "activeInterfaces": [{"name": "enp0s6", "family": "IPv4", "address": "*********", "netmask": "*************"}, {"name": "enp0s6", "family": "IPv6", "address": "fe80::17ff:fe01:5ac8", "netmask": "ffff:ffff:ffff:ffff::"}]}, "recommendations": {"instanceRequirements": {"memory": 0.5, "cpu": 0.2, "disk": 0.1}, "maxByMemory": 13, "maxByCPU": 10, "maxByDisk": 620, "recommendedInstances": 7, "safeLimit": 5, "performanceNotes": ["💡 Consider upgrading to a multi-core CPU for better performance."]}}