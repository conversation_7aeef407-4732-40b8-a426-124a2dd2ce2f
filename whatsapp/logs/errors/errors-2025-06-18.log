{"level":"error","message":"Error creating instance 8487921219: Protocol error (Target.setAutoAttach): Target closed.","name":"ProtocolError","originalMessage":"","stack":"ProtocolError: Protocol error (Target.setAutoAttach): Target closed.\n    at /mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Connection.js:104:24\n    at new Promise (<anonymous>)\n    at Connection.send (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Connection.js:100:16)\n    at ChromeTargetManager.initialize (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/ChromeTargetManager.js:253:82)\n    at CDPBrowser._attach (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Browser.js:155:76)\n    at CDPBrowser._create (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Browser.js:137:23)\n    at ChromeLauncher.launch (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/node/ChromeLauncher.js:92:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Client.initialize (/mnt/whatsapp/node_modules/whatsapp-web.js/src/Client.js:292:23)\n    at async WhatsAppService.createInstance (/mnt/whatsapp/whatsapp_service.js:4752:13)\n    at async /mnt/whatsapp/whatsapp_service.js:621:32","timestamp":"2025-06-18T05:14:35.829Z"}
{"level":"error","message":"Error creating instance test_new: Protocol error (Target.setAutoAttach): Target closed.","name":"ProtocolError","originalMessage":"","stack":"ProtocolError: Protocol error (Target.setAutoAttach): Target closed.\n    at /mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Connection.js:104:24\n    at new Promise (<anonymous>)\n    at Connection.send (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Connection.js:100:16)\n    at ChromeTargetManager.initialize (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/ChromeTargetManager.js:253:82)\n    at CDPBrowser._attach (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Browser.js:155:76)\n    at CDPBrowser._create (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Browser.js:137:23)\n    at ChromeLauncher.launch (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/node/ChromeLauncher.js:92:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Client.initialize (/mnt/whatsapp/node_modules/whatsapp-web.js/src/Client.js:292:23)\n    at async WhatsAppService.createInstance (/mnt/whatsapp/whatsapp_service.js:4752:13)\n    at async /mnt/whatsapp/whatsapp_service.js:621:32","timestamp":"2025-06-18T05:15:06.448Z"}
{"level":"error","message":"Error creating instance test_reconnect: Protocol error (Target.setAutoAttach): Target closed.","name":"ProtocolError","originalMessage":"","stack":"ProtocolError: Protocol error (Target.setAutoAttach): Target closed.\n    at /mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Connection.js:104:24\n    at new Promise (<anonymous>)\n    at Connection.send (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Connection.js:100:16)\n    at ChromeTargetManager.initialize (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/ChromeTargetManager.js:253:82)\n    at CDPBrowser._attach (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Browser.js:155:76)\n    at CDPBrowser._create (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/common/Browser.js:137:23)\n    at ChromeLauncher.launch (/mnt/whatsapp/node_modules/whatsapp-web.js/node_modules/puppeteer-core/lib/cjs/puppeteer/node/ChromeLauncher.js:92:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Client.initialize (/mnt/whatsapp/node_modules/whatsapp-web.js/src/Client.js:292:23)\n    at async WhatsAppService.createInstance (/mnt/whatsapp/whatsapp_service.js:4752:13)\n    at async /mnt/whatsapp/whatsapp_service.js:621:32","timestamp":"2025-06-18T05:20:18.623Z"}
{"level":"error","message":"Error disconnecting session 8487921219: Cannot read properties of null (reading 'close')","stack":"TypeError: Cannot read properties of null (reading 'close')\n    at Client.destroy (/mnt/whatsapp/node_modules/whatsapp-web.js/src/Client.js:782:31)\n    at WhatsAppService.shutdown (/mnt/whatsapp/whatsapp_service.js:6317:28)\n    at process.<anonymous> (/mnt/whatsapp/whatsapp_service.js:6274:46)\n    at process.emit (node:events:519:28)","timestamp":"2025-06-18T05:22:33.642Z"}
