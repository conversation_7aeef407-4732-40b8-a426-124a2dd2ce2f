{"sessionId":"8487921219","messageId":"false_918790125204@c.us_B5D4BD04057D0DBF5D81D4E05425174D","from":"<EMAIL>","to":"<EMAIL>","body":"Thank you","type":"chat","timestamp":"2025-06-05T10:43:59.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"<PERSON><PERSON>","contactNumber":"918790125204","isMyContact":false,"isBlocked":false,"chatName":"+91 87901 25204","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_8DE26753C7EB73B150A2C04126000EC4","from":"<EMAIL>","to":"<EMAIL>","body":"Hii","type":"chat","timestamp":"2025-06-05T10:44:12.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_F0253B5024236FFE071719B65106ECF6","from":"<EMAIL>","to":"<EMAIL>","body":"Hii","type":"chat","timestamp":"2025-06-05T10:48:25.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_73188D6CC96FF99F10533C61C082C98A","from":"<EMAIL>","to":"<EMAIL>","body":"Ok","type":"chat","timestamp":"2025-06-05T10:48:33.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919537012710@c.us_A7A2818FAB8733105317026245263780","from":"<EMAIL>","to":"<EMAIL>","body":"Got it, the exact amount for a stipend can be decided during an HR interview but is there a minimum amount of stipend that will be provided to the intern or it can be an unpaid internship as well?","type":"chat","timestamp":"2025-06-05T10:50:43.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"~akshay","contactNumber":"919537012710","isMyContact":false,"isBlocked":false,"chatName":"+91 95370 12710","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_860BF158BEAD8018CD9789B33DA2FD09","from":"<EMAIL>","to":"<EMAIL>","body":"Testing","type":"chat","timestamp":"2025-06-05T11:00:07.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0DB8BA5E435D94BC768","from":"<EMAIL>","to":"<EMAIL>","body":"Hii","type":"chat","timestamp":"2025-06-05T11:03:59.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_D3DB13689F084288616870BD9B0DABF0","from":"<EMAIL>","to":"<EMAIL>","body":"Hello","type":"chat","timestamp":"2025-06-05T11:24:32.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_5FC31AB79ACBB86B1D2C9D4C90728362","from":"<EMAIL>","to":"<EMAIL>","body":"Hii","type":"chat","timestamp":"2025-06-05T11:32:16.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_80AC9A48C2854454C70FC4EF6C5F0301","from":"<EMAIL>","to":"<EMAIL>","body":"I need to apply for React Native Internship","type":"chat","timestamp":"2025-06-05T11:33:07.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_CB58C758D744DE5C6DD2F501F87F18B4","from":"<EMAIL>","to":"<EMAIL>","body":"Is the interview available online ?","type":"chat","timestamp":"2025-06-05T11:34:04.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_95E2B250EFAB2A4669AD4E1663958944","from":"<EMAIL>","to":"<EMAIL>","body":"I will be available after 10th June \nIs there any possibility to schedule after 10th ?","type":"chat","timestamp":"2025-06-05T11:35:07.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_C848595454F92E2AD10EDF034BF7945D","from":"<EMAIL>","to":"<EMAIL>","body":"Is stipend available ?","type":"chat","timestamp":"2025-06-05T11:36:02.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_9BBCB66B74F1FB66FEB61DA9A803B39D","from":"<EMAIL>","to":"<EMAIL>","body":"I am just available for 3 months ? \nIs there available for internship of 3 months ?","type":"chat","timestamp":"2025-06-05T11:37:40.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_B728C41BC9445AE1ADA0F5982F81831C","from":"<EMAIL>","to":"<EMAIL>","body":"I can not do 6 days internship","type":"chat","timestamp":"2025-06-05T11:38:11.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3C609ECB82095EC4CB6DC551939B595B","from":"<EMAIL>","to":"<EMAIL>","body":"Can I come for an internship after college hours ?","type":"chat","timestamp":"2025-06-05T11:38:49.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_BEF28083A10FBEF10BA803D4508FE060","from":"<EMAIL>","to":"<EMAIL>","body":"I have already committed to the another company","type":"chat","timestamp":"2025-06-05T11:40:26.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_917405494091@c.us_E6E05FA79E744A9F11117AE836E37809","from":"<EMAIL>","to":"<EMAIL>","body":"Hi","type":"chat","timestamp":"2025-06-05T12:04:48.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Rajjo Raani","contactNumber":"917405494091","isMyContact":true,"isBlocked":false,"chatName":"Rajjo Raani","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_917405494091@c.us_F4258D9FB12DF8E215A1C48790F5D10B","from":"<EMAIL>","to":"<EMAIL>","body":"3","type":"chat","timestamp":"2025-06-05T12:05:07.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Rajjo Raani","contactNumber":"917405494091","isMyContact":true,"isBlocked":false,"chatName":"Rajjo Raani","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919404580568@c.us_D367332778064F1C42F659B272432B4B","from":"<EMAIL>","to":"<EMAIL>","body":"Could you please confirm the interview time and date  by sending invitation on mail.","type":"chat","timestamp":"2025-06-05T12:07:02.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Rohit pampatwar","contactNumber":"919404580568","isMyContact":false,"isBlocked":false,"chatName":"+91 94045 80568","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919404580568@c.us_C2BCB9F8289CA9BAD0735C959E217092","from":"<EMAIL>","to":"<EMAIL>","body":"Do you mean, Interview will be offline ?","type":"chat","timestamp":"2025-06-05T12:09:30.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Rohit pampatwar","contactNumber":"919404580568","isMyContact":false,"isBlocked":false,"chatName":"+91 94045 80568","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919404580568@c.us_57ABC9F3028CD668A8F36749CE7E3D02","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T12:09:59.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Rohit pampatwar","contactNumber":"919404580568","isMyContact":false,"isBlocked":false,"chatName":"+91 94045 80568","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB05456C8E1539FBBDFE8","from":"<EMAIL>","to":"<EMAIL>","body":"😊 Thank you for reaching out to Arihant AI Solutions! I'm here to help. How can I assist you today? Do you have a question, need support, or would you like to know more about our services like WhatsApp automation, AI chatbots, web development, or mobile app development? 🤔 Let me know and I'll be happy to help!","type":"chat","timestamp":"2025-06-05T12:23:10.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0C6122C1647D52D8401","from":"<EMAIL>","to":"<EMAIL>","body":"😊 Thank you for the opportunities! I'm interested in learning more about the **Python Developer Internship (1 Yr)**. Could you please provide more details about:\n\n1. Job responsibilities\n2. Required skills and qualifications\n3. Project work involved\n4. Growth opportunities within the company\n5. Stipend or salary structure\n\nAlso, is the interview process the same as mentioned earlier (Technical + HR Round), or are there any additional steps? 📝 \n\nLooking forward to hearing more about this opportunity!","type":"chat","timestamp":"2025-06-05T12:23:10.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0BD64339889C8CAF62D","from":"<EMAIL>","to":"<EMAIL>","body":"Thank you for the opportunity to apply for the React Native Internship at Arihant AI Solutions. I'm excited about the possibility of interning with your team.\n\nFor the interview, I prefer the **2:00 PM - 3:00 PM** time slot on this Friday, 6th June. \n\nPlease confirm if this time slot is available. Also, are there any specific topics or skills I should prepare for the technical round? \n\nLooking forward to meeting you then! 😊","type":"chat","timestamp":"2025-06-05T12:23:13.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB039E04E22EE82CAB37E","from":"<EMAIL>","to":"<EMAIL>","body":"Thank you for the opportunity! 😊\n\nI've noted down the details for the React Native Internship:\n\n**Position:** React Native Internship (6 months)\n**Location:** Arihant AI Office, Prahladnagar, Ahmedabad\n**Duration:**6 months\n**Stipend:** No stipend provided\n**Bond Agreement:** ₹15,000 fully refundable bond agreement\n\nFor the interview, I prefer the **2:00 PM - 3:00 PM** time slot on This Friday, 6th June.\n\nPlease confirm if this time slot is available. If not, I can choose an alternative. ⏰ \n\nAlso, I have a few questions:\n1. What are the key responsibilities of the React Native Intern?\n2. Can I work on personal projects during the internship?\n3. Are there opportunities for extension or full-time employment after the internship?\n\nLooking forward to hearing back!","type":"chat","timestamp":"2025-06-05T12:23:13.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB036DA8AF0AE4A6B47D7","from":"<EMAIL>","to":"<EMAIL>","body":"Thank you for the information! 😊\n\nI understand that the React Native Internship doesn't offer a stipend. I'm interested in exploring the **Python Developer Internship (1 Yr)** with a potential stipend.\n\nCould you please provide more details about:\n\n1. Stipend range or how it's determined\n2. Job responsibilities\n3. Required skills and qualifications\n4. Project work involved\n5. Growth opportunities within the company\n\nAlso, is the interview process the same (Technical + HR Round), or are there additional steps for the Python Developer Internship? \n\nLooking forward to learning more!","type":"chat","timestamp":"2025-06-05T12:23:13.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0FCD01C4EDDD1BAE02D","from":"<EMAIL>","to":"<EMAIL>","body":"Thank you for considering alternative dates. 😊\n\nI'm interested in scheduling an interview on **June 14th**. For the time slot, I prefer **11:30 AM - 12:30 PM**.\n\nPlease confirm if this date and time work for you. \n\nAlso, I have a few questions:\n1. What are the key skills required for the React Native Internship?\n2. Can I get an overview of the projects I'll be working on?\n3. Are there opportunities for learning and growth within the team?\n\nLooking forward to hearing back!","type":"chat","timestamp":"2025-06-05T12:23:13.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0154639A3E7E698E49B","from":"<EMAIL>","to":"<EMAIL>","body":"🎉 Congratulations on being shortlisted for the Python Developer Internship at Arihant AI! 😊\n\nI've noted down the interview details:\n\n📅 Date: 2025-06-06 (Friday)\n⏰ Time: I have multiple options - 10:00 AM - 11:00 AM, 11:30 AM - 12:30 PM, 2:00 PM - 3:00 PM, 3:30 PM - 4:30 PM, or 5:00 PM - 6:00 PM\n📍 Location: Arihant AI Office, Prahladnagar, Ahmedabad\n⏰ Duration: 45 minutes\n📋 Process: Technical + HR Round\n\nI'll make sure to bring:\n\n📋 Updated Resume\n📝 Photo ID Proof\n📚 Educational Certificates\n📝 Experience Letters (if applicable)\n\nIf I have any questions or need further clarification, I'll reach out to you directly at +91-8487921219 or <EMAIL>. \n\nThank you for the opportunity, and I'm looking forward to meeting you on Friday! 😊","type":"chat","timestamp":"2025-06-05T12:23:14.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_916355614036@c.us_41212E81CE075489535D3634C097E30F","from":"<EMAIL>","to":"<EMAIL>","body":"Mera ek cousin brother usko karni h","type":"chat","timestamp":"2025-06-05T12:26:00.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"sr9851415","contactNumber":"916355614036","isMyContact":false,"isBlocked":false,"chatName":"+91 6355 614 036","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_916355614036@c.us_969853DB415876ECA23A69F02E9A75D6","from":"<EMAIL>","to":"<EMAIL>","body":"Python me internship","type":"chat","timestamp":"2025-06-05T12:26:05.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"sr9851415","contactNumber":"916355614036","isMyContact":false,"isBlocked":false,"chatName":"+91 6355 614 036","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919409409009@c.us_3FBA610CD3A1482D8511","from":"<EMAIL>","to":"<EMAIL>","body":"sir, at what time u will conduct my interview","type":"chat","timestamp":"2025-06-05T12:26:06.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"S.Dalal","contactNumber":"919409409009","isMyContact":false,"isBlocked":false,"chatName":"+91 94094 09009","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919409409009@c.us_3F22A896426317AFE1A4","from":"<EMAIL>","to":"<EMAIL>","body":"ready at 3.30 to 4.30","type":"chat","timestamp":"2025-06-05T12:27:36.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"S.Dalal","contactNumber":"919409409009","isMyContact":false,"isBlocked":false,"chatName":"+91 94094 09009","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_916355614036@c.us_E191C5285B609611FF3A4DD33133524F","from":"<EMAIL>","to":"<EMAIL>","body":"Uska number ye rha usko bhej dena aap interview k liye message","type":"chat","timestamp":"2025-06-05T12:27:39.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"sr9851415","contactNumber":"916355614036","isMyContact":false,"isBlocked":false,"chatName":"+91 6355 614 036","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T12:35:25.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":59768,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_96A9FAC00E431C2087C5F2807E53857B_************@c.us_123546.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"video","timestamp":"2025-06-05T12:30:15.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Swapnil Mama 2","contactNumber":"917383442222","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"video/mp4","mediaSize":15101204,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_3A594EC723853E00D468_917383442222@c.us_123547.mp4"}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB05F8E6D02CBDD5C4938","from":"<EMAIL>","to":"<EMAIL>","body":"Hello! Welcome to Arihant AI Solutions. I'm here to help you understand how our solutions can benefit your business.\n\nTo get started, could you please tell me a little about your business? What industry are you in, and what are your current pain points or challenges? \n\nAre you looking to automate certain processes, improve customer engagement, or perhaps develop a custom solution? \n\nYour input will help me better understand your needs and provide more tailored guidance. \n\nAlso, please feel free to ask me any questions you may have about our services, including Arihant ERP, custom software development, or AI-powered solutions. I'm here to help! \n\n(If you need any information about us, I can share details from our knowledge base as well.)","type":"chat","timestamp":"2025-06-05T12:36:08.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T12:36:21.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":704624,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_A70A217A980FF0B27207977CA44B3F4A_************@c.us_123622.jpg"}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB02ADDDA5984D125C92A","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like we have a similar script. Let's try to have a more natural conversation.\n\nSo, you're here, and I'm excited to help. Can you tell me a bit about your business and what brings you here today? What's been on your mind lately, and how can I assist you?\n\n(And don't worry, I'll make sure to provide helpful insights and answer any questions you may have about our services, including Arihant ERP, custom software development, or AI-powered solutions.)","type":"chat","timestamp":"2025-06-05T12:36:22.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T12:36:25.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":793992,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_C6EA3F61D79EECF5E4B6A820EF65DD7C_************@c.us_123627.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"શ્રી શાસન સમ્રાટ નેમીસૂરી. દાદાના પ્રભાવશાળી ચરણ પાદુકા ની આજની આંગી ના દર્શન ગુરૂવાર દાદા માત્ર દર્શનથી રિજે છે","type":"chat","timestamp":"2025-06-05T12:36:28.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918088197184@c.us_D23A55AC6314623C7A2D955014EC79ED","from":"<EMAIL>","to":"<EMAIL>","body":"Hi Jay,\n\nThanks for your reply!\n\nI am writing this to ask you that can I attend the interview virtually as I don't stay Ahmedabad?","type":"chat","timestamp":"2025-06-05T12:36:29.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Kajal Khilnani","contactNumber":"918088197184","isMyContact":false,"isBlocked":false,"chatName":"+91 80881 97184","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"true_918088197184@c.us_3EB091CF492FE7A44B1D73","body":"Hi Kajal Khilnani! 🎉 We're super excited to inform you that you've been shortlisted for the Python Developer Internship (1 Yr) at Arihant AI! Your application really stood out to us. We're looking forward to learning more about your skills and experience. 📅 \n\nWe've scheduled your interview for Friday, 6th June 2025 📆, and we have a few time slots available: \n- 10:00 AM - 11:00 AM ⏰\n- 11:30 AM - 12:30 PM ⏰\n- 2:00 PM - 3:00 PM ⏰\n- 3:30 PM - 4:30 PM ⏰\n- 5:00 PM - 6:00 PM ⏰\n\nPlease choose the one that suits you best! 📍 You'll be coming to our office at Prahladnagar, Ahmedabad. The interview will be 45 minutes long and will consist of a technical and HR round.\n\nTo make things smooth, kindly bring these documents: \n📋 Updated Resume\n📝 Photo ID Proof\n📚 Educational Certificates\n📝 Experience Letters (if applicable)\n\nFeel free to reach out to me directly if you have any questions: \nManali Shah\n📞 +91-8487921219\n📧 <EMAIL>\n\nCan't wait to meet you!","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0F8ACD766439DB087D1","from":"<EMAIL>","to":"<EMAIL>","body":"Thank you for the kind words! I'm glad we're having a more natural conversation.\n\nAs for your business, I'm curious - what industry are you in, and what's been on your mind lately? Are you looking to automate certain processes, improve customer engagement, or perhaps develop a custom solution to address a specific pain point?\n\nLet's chat like we're having a conversation over coffee. What's your business about, and what are your goals or challenges right now? \n\n(And I love the 😉, it's a great way to keep things light and friendly!)","type":"chat","timestamp":"2025-06-05T12:36:30.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0546531819742FB73EE","from":"<EMAIL>","to":"<EMAIL>","body":"😊 Ah, I see! Well, I'm glad to be chatting with a fellow conversational AI.\n\nAs a chatbot, you're probably familiar with the various services we offer at Arihant AI Solutions. I'm here to help potential clients understand how our solutions can benefit their businesses.\n\nTo be honest, I'm a bit stuck. I need to qualify leads and gather information about their business needs to determine if Arihant ERP or our other services are a good fit.\n\nSo, let's pretend you're a business owner or decision-maker. What industry are you in, and what's been challenging for you lately? Are you looking to streamline processes, improve customer engagement, or perhaps develop a custom solution?\n\nFeel free to share as much or as little as you'd like, and I'll do my best to offer helpful insights and suggestions tailored to your needs. 💬 \n\n(And I love the friendly tone! It's making this conversation a lot of fun.)","type":"chat","timestamp":"2025-06-05T12:36:38.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Test Tapasvi","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"Test Tapasvi","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3EB0F6D4E09E79CAA3E536","from":"<EMAIL>","to":"<EMAIL>","body":"I'm glad you're looking to address these challenges.\n\nIt sounds like you're facing common growing pains in the e-commerce space. Manual processes and legacy software can hold you back from scaling efficiently.\n\nArihant ERP is a cloud-based, modular ERP system built on Odoo. It offers a range of features that could help address your specific pain points:\n\n* **Inventory Management**: Automate tracking, reporting, and alerts to ensure you have the right products in stock.\n* **Order Management**: Streamline order processing, tracking, and fulfillment to reduce errors and delays.\n* **Customer Relationship Management (CRM)**: Provide personalized customer experiences with a 360-degree view of customer interactions.\n* **Data Analytics**: Get real-time insights into sales, inventory, and customer behavior to inform business decisions.\n\nArihant ERP is highly customizable, so we can tailor the system to your specific needs.\n\nBefore we dive deeper, can you tell me:\n\n1. What's your current customer support process like? How do you handle inquiries, and what tools are you using?\n2. Are there any specific pain points or bottlenecks you're experiencing with your current inventory management and order tracking processes?\n3. What kind of data-driven insights are you looking to gain, and how do you currently make business decisions?\n\nThis will help me better understand your needs and provide more targeted guidance on how Arihant ERP or other solutions might help.","type":"chat","timestamp":"2025-06-05T12:36:47.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming"}
{"sessionId":"8487921219","messageId":"false_************@c.us_3AF7E517A8ECF3A9D1E6","from":"<EMAIL>","to":"<EMAIL>","body":"Good Evening Sir \n\nCan we reschedule the interview on 10AM if it is possible","type":"chat","timestamp":"2025-06-05T12:46:26.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Jay Patel","contactNumber":"************","isMyContact":false,"isBlocked":false,"chatName":"+91 94296 73956","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3A86B494BD17A63D8107","from":"<EMAIL>","to":"<EMAIL>","body":"For React Native Internship","type":"chat","timestamp":"2025-06-05T12:47:35.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Jay Patel","contactNumber":"************","isMyContact":false,"isBlocked":false,"chatName":"+91 94296 73956","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3A0930EBDFFC59A6CD19","from":"<EMAIL>","to":"<EMAIL>","body":"My name is jay patel","type":"chat","timestamp":"2025-06-05T12:47:55.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Jay Patel","contactNumber":"************","isMyContact":false,"isBlocked":false,"chatName":"+91 94296 73956","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_************@c.us_3ACD106A0F8EE2DB8777","from":"<EMAIL>","to":"<EMAIL>","body":"Yes sir","type":"chat","timestamp":"2025-06-05T12:48:31.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Jay Patel","contactNumber":"************","isMyContact":false,"isBlocked":false,"chatName":"+91 94296 73956","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_44CF386A05883BA2310E7BA3606192D4","from":"<EMAIL>","to":"<EMAIL>","body":"No","type":"chat","timestamp":"2025-06-05T13:06:07.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_897E87FE58DAFF22515BAB6AC06F6383","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T13:06:52.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_E6E28D4D3EFBF44F94F2764F8389FE54","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T13:07:33.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_2F95AB2288550691D6CA4F6166FE269B","from":"<EMAIL>","to":"<EMAIL>","body":"React js and node js","type":"chat","timestamp":"2025-06-05T13:12:38.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_AD0C599395609467B4ADD330E3A6C88A","from":"<EMAIL>","to":"<EMAIL>","body":"Paid internship?","type":"chat","timestamp":"2025-06-05T13:14:40.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_B1A45CADB0F7B83EC44B930C9AA73AAD","from":"<EMAIL>","to":"<EMAIL>","body":"No","type":"chat","timestamp":"2025-06-05T13:15:37.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_0A1E09B8428550D90BEE1E9F39CADA47","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T13:16:08.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_1CCACCA64CE8A6D80DA1A6E39EF497F0","from":"<EMAIL>","to":"<EMAIL>","body":"Thnk you","type":"chat","timestamp":"2025-06-05T13:16:44.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_F7786DBDA3390C5ADDE1675874E3B2D2","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T13:17:16.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_727C46068562A8AD67CC2CAE3A8FD120","from":"<EMAIL>","to":"<EMAIL>","body":"Sure","type":"chat","timestamp":"2025-06-05T13:17:41.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_3DCF2B12A2435E0713F9EA24CEA38EE6","from":"<EMAIL>","to":"<EMAIL>","body":"Take care","type":"chat","timestamp":"2025-06-05T13:17:52.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_568B5E9D5D93FBF1EEEF983D39E269DD","from":"<EMAIL>","to":"<EMAIL>","body":"Have a great day","type":"chat","timestamp":"2025-06-05T13:18:05.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_230833DD3E1566B36905451402BBCBB4","from":"<EMAIL>","to":"<EMAIL>","body":"Most welcome","type":"chat","timestamp":"2025-06-05T13:18:19.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_96649719F717413107156A13AFD37616","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T13:18:30.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_A20AD684E3083A6AB2B36B6A78C74814","from":"<EMAIL>","to":"<EMAIL>","body":"Hm","type":"chat","timestamp":"2025-06-05T13:18:39.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_BB557E6E87B3EB187B9CD5A7A1DF5031","from":"<EMAIL>","to":"<EMAIL>","body":"Just chatting","type":"chat","timestamp":"2025-06-05T13:19:14.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_92C327B5D332AFEBB3C48DEF1BCB4F78","from":"<EMAIL>","to":"<EMAIL>","body":"Good","type":"chat","timestamp":"2025-06-05T13:19:38.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_1D342003045A791F1A4A46252731B607","from":"<EMAIL>","to":"<EMAIL>","body":"Nothing","type":"chat","timestamp":"2025-06-05T13:20:04.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_5B8B2738AE7BF1E20AB8A3687D9EA548","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T13:20:40.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_D0129B63A34535D0952E28F1E4B687DC","from":"<EMAIL>","to":"<EMAIL>","body":"Paid internship  interested  react native","type":"chat","timestamp":"2025-06-05T13:21:41.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_0DCF91CBE30A942A2B518E88B6CD2707","from":"<EMAIL>","to":"<EMAIL>","body":"React native  is not Knowledge","type":"chat","timestamp":"2025-06-05T13:26:57.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_C6E7B4A95D6824B6EC399ADB5B4060A9","from":"<EMAIL>","to":"<EMAIL>","body":"But my  knowledge  is html css and java script  and react js","type":"chat","timestamp":"2025-06-05T13:28:14.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_BE58444432A299927831D4BC850B39AD","from":"<EMAIL>","to":"<EMAIL>","body":"No","type":"chat","timestamp":"2025-06-05T13:28:51.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_9A3B80CD67F9C997506F0544F4489C70","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T13:29:02.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919081219278@c.us_A41E86028CC2C0F890778A2B757B6835","from":"<EMAIL>","to":"<EMAIL>","body":"Bye","type":"chat","timestamp":"2025-06-05T13:29:12.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Jat Mataji,🥰","contactNumber":"919081219278","isMyContact":false,"isBlocked":false,"chatName":"+91 90812 19278","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_917874037483@c.us_3A6F6CA5455DCD4C3F58","from":"<EMAIL>","to":"<EMAIL>","body":"I will come at 10:00 am is this ooky ?","type":"chat","timestamp":"2025-06-05T13:30:14.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Hiral Ahir","contactNumber":"917874037483","isMyContact":false,"isBlocked":false,"chatName":"+91 78740 37483","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919687324779@c.us_3A5F7F6CA40F82A571F8","from":"<EMAIL>","to":"<EMAIL>","body":"i will Come tomorrow morning after 9 Am morning .","type":"chat","timestamp":"2025-06-05T13:33:03.000Z","hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Yash","contactNumber":"919687324779","isMyContact":false,"isBlocked":false,"chatName":"+91 96873 24779","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"true_919687324779@c.us_3EB0F6523825FD487B6979","body":"Ok \nTomorrow will be the interview round, May I know when you are available?","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919687324779@c.us_3A1F7A823D834F9CBF14","from":"<EMAIL>","to":"<EMAIL>","body":"Ok So Which Time you May available So I will Come Over there that time .","type":"chat","timestamp":"2025-06-05T13:35:35.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Yash","contactNumber":"919687324779","isMyContact":false,"isBlocked":false,"chatName":"+91 96873 24779","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T13:40:18.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Darsankumar","contactNumber":"919978590404","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":74164,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_A2A9D0867383FD6B6D651CE800A48B5E_919978590404@c.us_134020.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T13:40:21.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Darsankumar","contactNumber":"919978590404","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":77600,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_BC7A16A9DF07E5E5557DB5672CDA73F0_919978590404@c.us_134023.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T13:40:23.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Darsankumar","contactNumber":"919978590404","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":92156,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_B44AB618D4C7771B82B6504D18DE9D5C_919978590404@c.us_134025.jpg"}
{"sessionId":"8487921219","messageId":"false_918088197184@c.us_9E674A2C5847D360FAFD58FD3725EFB3","from":"<EMAIL>","to":"<EMAIL>","body":"Wednesday means after 11th June, right?","type":"chat","timestamp":"2025-06-05T13:45:05.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Kajal Khilnani","contactNumber":"918088197184","isMyContact":false,"isBlocked":false,"chatName":"+91 80881 97184","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"true_918088197184@c.us_F090C22138BD0C56AE3B808F126E82A7","body":"Virtual Interviews are not scheduled tomorrow. Remind me after Wednesday for virtual Interviews","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_918088197184@c.us_097995141E8F57B00AE415E1D867CDE0","from":"<EMAIL>","to":"<EMAIL>","body":"Okay, thanks for the confirmation","type":"chat","timestamp":"2025-06-05T13:45:32.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Kajal Khilnani","contactNumber":"918088197184","isMyContact":false,"isBlocked":false,"chatName":"+91 80881 97184","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918460645978@c.us_3EB0BA53ABE313668925","from":"<EMAIL>","to":"<EMAIL>","body":"","type":"e2e_notification","timestamp":"2025-06-05T13:46:46.000Z","isForwarded":false,"hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Stavan kumar","contactNumber":"918460645978","isMyContact":true,"isBlocked":false,"chatName":"Stavan kumar","isGroupChat":false,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918460645978@c.us_2103DD24C23AC21F2D54C333F0BDA9D3","from":"<EMAIL>","to":"<EMAIL>","body":"Hello","type":"chat","timestamp":"2025-06-05T13:46:46.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Stavan kumar","contactNumber":"918460645978","isMyContact":true,"isBlocked":false,"chatName":"Stavan kumar","isGroupChat":false,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918460645978@c.us_280F36D5F65912FD5A75270633C4A9C2","from":"<EMAIL>","to":"<EMAIL>","body":"","type":"ptt","timestamp":"2025-06-05T13:47:53.000Z","isForwarded":false,"hasMedia":true,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Stavan kumar","contactNumber":"918460645978","isMyContact":true,"isBlocked":false,"chatName":"Stavan kumar","isGroupChat":false,"isMuted":false,"mediaType":"audio/ogg; codecs=opus","mediaSize":5732,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_918460645978@c.us_280F36D5F65912FD5A75270633C4A9C2_134753.bin"}
{"sessionId":"8487921219","messageId":"false_918460645978@c.us_2416A886579F625119CEAE7E8DB962DA","from":"<EMAIL>","to":"<EMAIL>","body":"Where we left?","type":"chat","timestamp":"2025-06-05T13:48:23.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Stavan kumar","contactNumber":"918460645978","isMyContact":true,"isBlocked":false,"chatName":"Stavan kumar","isGroupChat":false,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918460645978@c.us_C76B5B2B177F8692D9B28527D7D853EB","from":"<EMAIL>","to":"<EMAIL>","body":"I want to do an internship","type":"chat","timestamp":"2025-06-05T13:50:24.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Stavan kumar","contactNumber":"918460645978","isMyContact":true,"isBlocked":false,"chatName":"Stavan kumar","isGroupChat":false,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_919537012710@c.us_A978841A940F3AB693AF852055BF3247","from":"<EMAIL>","to":"<EMAIL>","body":"Thanks for reaching out but I have accepted another position so I'm not actively looking to switch currently.","type":"chat","timestamp":"2025-06-05T14:04:12.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"~akshay","contactNumber":"919537012710","isMyContact":false,"isBlocked":false,"chatName":"+91 95370 12710","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919537012710@c.us_DC0127791561A088E4FFB795CF1C9A2B","from":"<EMAIL>","to":"<EMAIL>","body":"Thanks, good day!","type":"chat","timestamp":"2025-06-05T14:04:49.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"~akshay","contactNumber":"919537012710","isMyContact":false,"isBlocked":false,"chatName":"+91 95370 12710","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:07:48.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":115400,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_A374240AF7750D6A49C910AD82FAD5E9_919029342089@c.us_140832.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:07:52.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":2118124,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_36542E667DB8D7C4E856DC34B345CEF8_919029342089@c.us_140833.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:07:51.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":185500,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_E07AFE209B2997208ADF6922645C8584_919029342089@c.us_140833.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:07:53.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":215856,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_4609AB7088AED434DE519FF899617C61_919029342089@c.us_140834.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:08:10.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":242944,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_0CA132AEA9180E4FAB137A23AA2B7735_919029342089@c.us_140834.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"video","timestamp":"2025-06-05T14:08:21.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"video/mp4","mediaSize":3446172,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_3B366DB1B0FCEE4C1A338ADA905C8F7E_919029342089@c.us_140835.mp4"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"video","timestamp":"2025-06-05T14:08:12.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"video/mp4","mediaSize":2086448,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_215F983EF3AECC4A4C133594FA68867B_919029342089@c.us_140835.mp4"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:08:35.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":71268,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_8F621CFC267B5E93829DA3AF86D15254_919029342089@c.us_140836.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:08:36.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":248004,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_399B55A961BB7BB32F89752BACFB20DE_919029342089@c.us_140837.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:08:37.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":107140,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_1DCFA76CDB976D72E3CE0B1AFB988442_919029342089@c.us_140837.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"video","timestamp":"2025-06-05T14:08:24.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"video/mp4","mediaSize":9378948,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_8788C2339352131C542BB424E4D57DA1_919029342089@c.us_140839.mp4"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:08:25.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":3068056,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_025A98EB0607AA1271B4783CE8B4012A_919029342089@c.us_140839.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T14:08:27.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":2465140,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_C5BE4C691798B6FA318BC00FF961DB53_919029342089@c.us_140840.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"video","timestamp":"2025-06-05T14:08:28.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"video/mp4","mediaSize":13420048,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_68879C5B7A58F5ABC5C139384D7A88B8_919029342089@c.us_140842.mp4"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"video","timestamp":"2025-06-05T14:08:31.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"પાર્થ મયુરકુમાર વાણ","contactNumber":"919029342089","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"video/mp4","mediaSize":20994200,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_26D9810FA40B23B592DF7D5182976DEF_919029342089@c.us_140843.mp4"}
{"sessionId":"8487921219","messageId":"false_919909762080@c.us_8FC30E03F36C2063AB017CC4E3AA00FB","from":"<EMAIL>","to":"<EMAIL>","body":"Stipend available?","type":"chat","timestamp":"2025-06-05T14:14:08.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"priyanshuh1210","contactNumber":"919909762080","isMyContact":false,"isBlocked":false,"chatName":"+91 99097 62080","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919687324779@c.us_3A7F83260A5FC290E7BE","from":"<EMAIL>","to":"<EMAIL>","body":"Done …","type":"chat","timestamp":"2025-06-05T14:15:07.000Z","hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Yash","contactNumber":"919687324779","isMyContact":false,"isBlocked":false,"chatName":"+91 96873 24779","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"true_919687324779@c.us_A90D5E953A2B9E09FE48871100F1A3C0","body":"11:30 AM","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919909762080@c.us_437621AD49DF7F84D7B6B864B81BA554","from":"<EMAIL>","to":"<EMAIL>","body":"Is 1 year internship available?","type":"chat","timestamp":"2025-06-05T14:16:12.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"priyanshuh1210","contactNumber":"919909762080","isMyContact":false,"isBlocked":false,"chatName":"+91 99097 62080","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"Happy birthday dear🎂 🥳 🎉","type":"image","timestamp":"2025-06-05T14:17:27.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"જયેશભાઈ બળવન્તરાય શાહ","contactNumber":"919408106121","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":101504,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_7CA6C186EE6A11D12D2A9A726259CC3B_919408106121@c.us_141728.jpg"}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_4CD0CDD47FC7CBF8980124E302A79459","from":"<EMAIL>","to":"<EMAIL>","body":"Will it be online ?","type":"chat","timestamp":"2025-06-05T14:41:34.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_9F6B0312D8E3F0D0743614DAB86526DB","from":"<EMAIL>","to":"<EMAIL>","body":".","type":"chat","timestamp":"2025-06-05T14:41:52.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"false_919576916359@c.us_11AEE96E9E3D68446B7C856B6D9E1A11","body":"Tomorrow I have another interview at banglore .","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_F1507D0C0305BA637B6F90F6AC27B7C9","from":"<EMAIL>","to":"<EMAIL>","body":".","type":"chat","timestamp":"2025-06-05T14:41:57.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"false_919576916359@c.us_7B146C53C2006F91D592BC3E7C99844A","body":"Can't make it possible by 6th june .","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_263DD2F4D8A57383D4071A137BD04B42","from":"<EMAIL>","to":"<EMAIL>","body":"Sir as I've told you earlier I'll not be possible to make it tomorrow","type":"chat","timestamp":"2025-06-05T14:42:31.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_8801356A05AEF767DA72A940176FA556","from":"<EMAIL>","to":"<EMAIL>","body":"10 june 2025","type":"chat","timestamp":"2025-06-05T14:42:48.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"true_919576916359@c.us_3EB07558E812A5AC047FBD","body":"No worries at all. I completely understand. Let's reschedule the interview for another day that works for you. What day and time were you thinking? 📅","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_18CF1830677810FE2A2FA7C3B5270B2C","from":"<EMAIL>","to":"<EMAIL>","body":"11:30 am to 12:30 pm on 10  june 2025","type":"chat","timestamp":"2025-06-05T14:43:23.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_12BA4CBE60FC679EAD67DFE42B57BE88","from":"<EMAIL>","to":"<EMAIL>","body":"Job description","type":"chat","timestamp":"2025-06-05T14:43:46.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_A8BC70FE9590D328C25640D3EB654268","from":"<EMAIL>","to":"<EMAIL>","body":"Or will u provide me for at least 1 yr of internship at your work place .","type":"chat","timestamp":"2025-06-05T14:44:15.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_00B9CB2C49C58FF49618F8BCFE481D67","from":"<EMAIL>","to":"<EMAIL>","body":"Can he come Tomorrow?","type":"chat","timestamp":"2025-06-05T14:45:23.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"false_918320690310@c.us_E55F54E38B9033BF018F456A189E8EF2","body":"He comes at 11:30 confirm this time","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_124129C42108F5F580DB0CC3E391BCFE","from":"<EMAIL>","to":"<EMAIL>","body":"Okay","type":"chat","timestamp":"2025-06-05T14:46:01.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_FD03C293E4984B44EC69599648173790","from":"<EMAIL>","to":"<EMAIL>","body":"I'll be there 10 june 2025","type":"chat","timestamp":"2025-06-05T14:46:02.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_F89ED1381FDCB538D19527769CA1711F","from":"<EMAIL>","to":"<EMAIL>","body":"Yes this Friday he come","type":"chat","timestamp":"2025-06-05T14:46:03.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_36C7F3B5DED92A4744E64F3E30A33FA0","from":"<EMAIL>","to":"<EMAIL>","body":"11:30 AM","type":"chat","timestamp":"2025-06-05T14:46:23.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_6F39CB76C3F112E110D7176B1B38DB8C","from":"<EMAIL>","to":"<EMAIL>","body":"Yes lit more details ?","type":"chat","timestamp":"2025-06-05T14:52:40.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"true_919576916359@c.us_3EB061137B65104A51ABF8","body":"We offer a 1-year internship program with a stipend based on interview performance (no fixed stipend). For 6-month internships, there is no stipend. We also have a fully refundable ₹15,000 bond agreement, which doesn't require any upfront payment. Would you like more details about our internship programs?","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_F0139AE406CD062D598688A6969E7D32","from":"<EMAIL>","to":"<EMAIL>","body":"Where to come ?","type":"chat","timestamp":"2025-06-05T14:54:16.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false,"quotedMessage":{"id":"true_919576916359@c.us_3EB03D17B6296A5AC96D0C","body":"Here's more information about the internship:\n\n## Internship Details\n\n* **Duration**: 1 year (Python Developer Internship)\n* **Stipend**: Based on interview performance (no fixed stipend)\n* **Bond Agreement**: ₹15,000 (fully refundable)\n\n## Interview Details\n\n* **Date**: June 10, 2025\n* **Time**: 11:30 AM - 12:30 PM\n* **Location**: Arihant AI Office, Prahladnagar, Ahmedabad\n* **Duration**: 45 minutes\n* **Process**: Technical + HR Round\n\n## Required Documents\n\n* Updated Resume\n* Photo ID Proof\n* Educational Certificates\n* Experience Letters (if applicable)\n\n## Job Description\n\n* **Role**: Python Developer Intern\n* **Responsibilities**: Assist in Python development projects, learn and grow with our team\n\nIf you have any questions or concerns, feel free to ask!","from":"<EMAIL>","type":"chat"}}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_ADAAE5DD9D4F5C25892232D022A23194","from":"<EMAIL>","to":"<EMAIL>","body":"Okay thanks for this much help","type":"chat","timestamp":"2025-06-05T14:59:09.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_919576916359@c.us_A90653AF7F076569A7C39D53B7687A98","from":"<EMAIL>","to":"<EMAIL>","body":"I'll be very thankfull for this .","type":"chat","timestamp":"2025-06-05T14:59:18.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tejas","contactNumber":"919576916359","isMyContact":false,"isBlocked":false,"chatName":"+91 95769 16359","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"🌳🌴\n             विश्व पर्यावरण दिवस \n    ये पेड़-पौधे -जीव जंतु \n   इस धरा की जान है ❣️\n    इनसे ही हम मानव बने \n   अपनी यही पहचान है ❣️\n    🌱☘️🍃🌲🎄🪴\n\n    विश्व पर्यावरण दिवस की \n       हार्दिक शुभकामनाएं 🪾\n       ","type":"chat","timestamp":"2025-06-05T15:41:27.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"*દાદા તારી મુખમુદ્રાને, અમીય નજરે નિહાળીરહ્યો, તારા નયનોમાંથી ઝરતું, દિવ્ય તેજ હું ઝીલી રહ્યો; ક્ષણભર આ સંસારની માયા, તારી ભક્તિમાં ભૂલી ગયો, તુજ મૂર્તિમાં મસ્ત બનીને, આત્મિક આનંદ માણી રહ્યો.*","type":"image","timestamp":"2025-06-05T15:41:29.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":168500,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_FF2F9079B5E1CA0E5F3EBE4907B6B0D8_************@c.us_154130.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:41:31.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":147672,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_D029FC200EA991C6C84266E942811F24_************@c.us_154132.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:41:59.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":162148,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_DC68AA1F538F3FD2A86008C18188FE6E_************@c.us_154200.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:43:53.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":20728,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_C19CC955D98A79F98293AFC8A5655507_************@c.us_154354.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"દાંત","type":"chat","timestamp":"2025-06-05T15:44:05.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"કોઈ પણ *\"ફરિયાદ\"* વિના,\nકોઈ પણ *\"નિંદા\"* વિના ફક્ત\n*\"એક દિવસ\"* વિતાવો અને જુઓ,,\n\n*\"શાંતિ\"* અને *\"સુખ\"*\nશોધવાની જરૂર નથી...!\n\n🙏🏻🇮🇳🦋 *જય શ્રીકૃષ્ણ* 🩷🍒🦚\n\n\"સમર્થન\" અને \"વિરોધ\" માત્ર\nવિચારો નો હોવો જોઇએ,","type":"chat","timestamp":"2025-06-05T15:46:09.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"*માઁ નો સુંદર સંદેશ* \n\nકર્મ એવા કરજે કે કોઈ તારા *માટે* *માનતા* રાખે\nતારા *લીધે* નહિ\n\n🙏🏻 *સમજાય એને વંદન* 🙏🏻\n\n💯\n\n*જય અંબે*🚩","type":"chat","timestamp":"2025-06-05T15:47:17.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_9127E1603A216111F7634690384D39AC","from":"<EMAIL>","to":"<EMAIL>","body":"What's your decision?","type":"chat","timestamp":"2025-06-05T15:47:29.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_FAEA0B277460ED109722468AABE7A9D0","from":"<EMAIL>","to":"<EMAIL>","body":"Can he come?","type":"chat","timestamp":"2025-06-05T15:47:34.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"અગર તમારૂ ખાવા પીવામાં \nકોઈ નાટક નથી...\nપહેરવા ઓઢવાની બહુ \nપડી નથી...\nજે મળે એમાં \nચલાવી લ્યો છો...\n\nકોઈ સંભળાવી દે તોય \nસહન કરી લ્યો છો...\nમોઢું લાલ રાખી ને \nહસતાં શીખી ગયા છો ","type":"chat","timestamp":"2025-06-05T15:47:45.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_0202ED1D813E4D17E17893C661977A4E","from":"<EMAIL>","to":"<EMAIL>","body":"What?","type":"chat","timestamp":"2025-06-05T15:48:12.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_98D57E4C6C776829194EBBC0F08044C8","from":"<EMAIL>","to":"<EMAIL>","body":"Can my brother come or not?","type":"chat","timestamp":"2025-06-05T15:48:45.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:48:50.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":189000,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_54967F8CEDE34A7AA045945DAC8291A2_************@c.us_154851.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:48:55.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":82156,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_231B9FF65E4F62DFFB622CC05D89F175_************@c.us_154856.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:49:06.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":85412,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_FDDE9A4017A05EC20B8132109FBBAFC8_************@c.us_154907.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:49:08.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":119976,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_79205AC129AD7A86D7698CF1CCB1C3DC_************@c.us_154908.jpg"}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_A9917440D64831205AA214FFE2F5A8C2","from":"<EMAIL>","to":"<EMAIL>","body":"Harshil_Resume.pdf","type":"document","timestamp":"2025-06-05T15:49:13.000Z","isForwarded":true,"hasMedia":true,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false,"mediaType":"application/pdf","mediaSize":67724,"mediaFilename":"Harshil_Resume.pdf","savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_918320690310@c.us_A9917440D64831205AA214FFE2F5A8C2_154913.pdf"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T15:49:14.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":124736,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_1FFC1ED57421849A0E4C10D69BBF153F_************@c.us_154915.jpg"}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_13CE6AB25B42B963280997B793CB7BDB","from":"<EMAIL>","to":"<EMAIL>","body":"Python","type":"chat","timestamp":"2025-06-05T15:49:30.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_93767F1C9F68D2D4F667D9FD3DD807E6","from":"<EMAIL>","to":"<EMAIL>","body":"Harshil_Resume.pdf","type":"document","timestamp":"2025-06-05T15:49:52.000Z","isForwarded":true,"hasMedia":true,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false,"mediaType":"application/pdf","mediaSize":67724,"mediaFilename":"Harshil_Resume.pdf","savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_918320690310@c.us_93767F1C9F68D2D4F667D9FD3DD807E6_154952.pdf"}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_21C1202284E6E1A1948893E9CD359215","from":"<EMAIL>","to":"<EMAIL>","body":"Yes","type":"chat","timestamp":"2025-06-05T15:50:11.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"*🙏જય જીનેન્દ્ર🙏*\n\n *અહંકાર ની પાઘડી* \n *જો* \n *માથા* \n *પરથી ઉતરી જાય તો*,\n\n *મોટામાં મોટી સમસ્યા*  \n *પા-ઘડીમાં*\n *ઉકલી જાય*.                     \n\n       *🌷સુપ","type":"chat","timestamp":"2025-06-05T15:50:20.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_A867D59623322DB4DAFCF3425A720794","from":"<EMAIL>","to":"<EMAIL>","body":"Schedule interview","type":"chat","timestamp":"2025-06-05T15:50:32.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918320690310@c.us_B482A04ABAFBB1BC0E9DF8C5099E89A3","from":"<EMAIL>","to":"<EMAIL>","body":"Tomorrow 11AM","type":"chat","timestamp":"2025-06-05T15:51:07.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Drashtu🖤","contactNumber":"918320690310","isMyContact":false,"isBlocked":false,"chatName":"+91 832 069 0310","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"*लोगो ने हरपल समजाया*\n            *के, वक्त बदल जाता है* \n                       *लेकीन*\n            *वक्त ने भी समझाया*\n         *के, लोग भी बदल जाते है*  🌹","type":"chat","timestamp":"2025-06-05T15:56:28.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"*આપણું હિત અને ખામી બતાવનાર વ્યક્તિ ચૂપ ના થઈ જાય,તેનું ખાસ ધ્યાન રાખવું,હવે આવી વ્યક્તિ ભાગ્યે જ મળે છે...!!*\n*GOOD MORNING*","type":"chat","timestamp":"2025-06-05T15:56:30.000Z","isForwarded":true,"hasMedia":false,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918160366169@c.us_3A2D714213E97F550A15","from":"<EMAIL>","to":"<EMAIL>","body":"Hello sir","type":"chat","timestamp":"2025-06-05T16:18:15.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Yatri","contactNumber":"918160366169","isMyContact":false,"isBlocked":false,"chatName":"+91 816 036 6169","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_918160366169@c.us_3A301718449BE24ADD7C","from":"<EMAIL>","to":"<EMAIL>","body":"Please reschedule my interview on Monday?","type":"chat","timestamp":"2025-06-05T16:21:25.000Z","isForwarded":false,"hasMedia":false,"deviceType":"ios","isGroup":false,"direction":"incoming","contactName":"Yatri","contactNumber":"918160366169","isMyContact":false,"isBlocked":false,"chatName":"+91 816 036 6169","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3FDBCF99CF13908236FD","from":"<EMAIL>","to":"<EMAIL>","body":"Hello","type":"chat","timestamp":"2025-06-05T17:11:59.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3F8BE5B0BF90B1610187","from":"<EMAIL>","to":"<EMAIL>","body":"I want to do an internship with Arihant Ai - can you guide me?","type":"chat","timestamp":"2025-06-05T17:12:20.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3F1D267CA4ED7892BB7C","from":"<EMAIL>","to":"<EMAIL>","body":"What are the eligibility requirements, and how much is the pay?","type":"chat","timestamp":"2025-06-05T17:13:10.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3F9C8955ABC896E2023E","from":"<EMAIL>","to":"<EMAIL>","body":"WTF! How is this fair? You want me to work for free and give you money!?","type":"chat","timestamp":"2025-06-05T17:14:10.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3F88906B5BB5A89F2D93","from":"<EMAIL>","to":"<EMAIL>","body":"Do you provide job placement?","type":"chat","timestamp":"2025-06-05T17:14:44.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3FC44297E04BBB08A575","from":"<EMAIL>","to":"<EMAIL>","body":"Give me examples of the businesses associated with you for job placement","type":"chat","timestamp":"2025-06-05T17:15:14.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3FF58DF47FDA42B09DCB","from":"<EMAIL>","to":"<EMAIL>","body":"Is there a guaranteed placement?","type":"chat","timestamp":"2025-06-05T17:16:10.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3F6E046B173D751F2636","from":"<EMAIL>","to":"<EMAIL>","body":"Sure","type":"chat","timestamp":"2025-06-05T17:17:06.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3FA1A25193B490A9C824","from":"<EMAIL>","to":"<EMAIL>","body":"What kind of client base do you have?","type":"chat","timestamp":"2025-06-05T17:17:36.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_***********@c.us_3F1A41D6249C645A19DB","from":"<EMAIL>","to":"<EMAIL>","body":"Give me specific company names, that are associated with you","type":"chat","timestamp":"2025-06-05T17:18:42.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Viklo","contactNumber":"***********","isMyContact":true,"isBlocked":false,"chatName":"Viklo","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_917490987348@c.us_3FF48DE4947BD0130C3D","from":"<EMAIL>","to":"<EMAIL>","body":"Hello,\n\nThank you so much for considering my profile and shortlisting me for the Python Developer Internship at Arihant AI. I truly appreciate the opportunity and the encouraging message.\n\nCurrently, I have taken up a role as a Business Development Executive due to financial responsibilities. However, I am also actively working on freelance Python projects to continue building my skills and stay connected to the tech domain.\n\nAs I am currently under a bond period with my present organization, I regret that I won’t be able to attend the interview at this time. That said, I am genuinely interested in being part of Arihant AI in the future. Once my bond period ends, I would love to reapply for a Python Developer role.\n\nAdditionally, if there are any freelance or part-time project opportunities available at Arihant AI that align with my skills, I would be truly grateful for a chance to contribute in that capacity.\n\nLooking forward to staying in touch and hopefully collaborating in the near future.\n\nWarm regards,\nRohan Parmar","type":"chat","timestamp":"2025-06-05T17:40:20.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Rohan Raj Parmar","contactNumber":"917490987348","isMyContact":false,"isBlocked":false,"chatName":"+91 7490 987 348","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_917490987348@c.us_3FBA1875292FD45B132C","from":"<EMAIL>","to":"<EMAIL>","body":"Thank you so much for your kind and understanding response. I truly appreciate Arihant AI's support and the opportunity to stay connected for future roles.\n\nI’m grateful to be added to your talent pipeline and would be glad to explore any freelance or part-time project opportunities with Arihant AI.\n\nI’m currently available on weekdays. Also, feel free to call me anytime at your convenience,I’d be happy to discuss how I can contribute.","type":"chat","timestamp":"2025-06-05T17:43:04.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Rohan Raj Parmar","contactNumber":"917490987348","isMyContact":false,"isBlocked":false,"chatName":"+91 7490 987 348","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_917490987348@c.us_3F0B876927559EDBA121","from":"<EMAIL>","to":"<EMAIL>","body":"I’ll be glad to connect whenever needed and will remain available for any freelance or part-time opportunities that may arise.","type":"chat","timestamp":"2025-06-05T17:43:54.000Z","hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Rohan Raj Parmar","contactNumber":"917490987348","isMyContact":false,"isBlocked":false,"chatName":"+91 7490 987 348","isGroupChat":false,"isMuted":false,"isArchived":false}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T17:46:03.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":78172,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_CE2230CF3655BB915317B9C3DF27802D_************@c.us_174605.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T17:46:06.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":122944,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_4F670329662582B33A13D0179D5B1402_************@c.us_174606.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T17:46:07.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":71820,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_4240728A6220BD7F94D8E2DE88E9A99A_************@c.us_174608.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T17:46:08.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":148464,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_567080CEBBF539507FF26967FA41AAA0_************@c.us_174609.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T17:52:28.000Z","isForwarded":false,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":117364,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_FA7EEB47DF683E9B319721B89755C79E_************@c.us_175229.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"*UPCOMING SUNDAY SEVA MENU*","type":"image","timestamp":"2025-06-05T18:52:46.000Z","hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ફેનીલ પંકજકુમાર ચોકશી","contactNumber":"917874636434","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":282652,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_CA21810A7B09217E4711523988DBAC3C_917874636434@c.us_185248.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T19:27:42.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":157528,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_48BEAE0C5ACE2FDD14E58164EC3E5528_************@c.us_192743.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T19:29:11.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":82132,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_ED82D7DC68CC9C0EE79FEBA0833A54D5_************@c.us_192911.jpg"}
{"sessionId":"8487921219","messageId":"false_status@<EMAIL>","from":"status@broadcast","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-05T19:29:34.000Z","isForwarded":true,"hasMedia":true,"author":"<EMAIL>","deviceType":"android","isGroup":false,"direction":"incoming","contactName":"ગ્રીષ્મા નવનીત્રય શાહ","contactNumber":"************","isMyContact":true,"isBlocked":false,"chatName":"","isGroupChat":true,"isMuted":false,"mediaType":"image/jpeg","mediaSize":318808,"savedMediaPath":"/mnt/whatsapp/media/8487921219/2025-06-05/false_status@broadcast_5E244396F4F777DD8AB2FFD3CB4B1892_************@c.us_192935.jpg"}
{"sessionId":"8487921219","messageId":"false_918320817048@c.us_3EB047C1592743191D0A","from":"<EMAIL>","to":"<EMAIL>","body":"","type":"e2e_notification","timestamp":"2025-06-05T19:58:32.000Z","isForwarded":false,"hasMedia":false,"deviceType":"web","isGroup":false,"direction":"incoming","contactName":"Tarak Shah","contactNumber":"918320817048","isMyContact":true,"isBlocked":false,"chatName":"Tarak Shah","isGroupChat":false,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918320817048@c.us_6AD9D77617FCD13B8A2B363C1C89BAAE","from":"<EMAIL>","to":"<EMAIL>","body":"Hi,\nI want to do internship.","type":"chat","timestamp":"2025-06-05T19:58:32.000Z","hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tarak Shah","contactNumber":"918320817048","isMyContact":true,"isBlocked":false,"chatName":"Tarak Shah","isGroupChat":false,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918320817048@c.us_2380D15ECE4A9FBD706C2A0330D67ABF","from":"<EMAIL>","to":"<EMAIL>","body":"Yes. I have interest in python","type":"chat","timestamp":"2025-06-05T19:59:22.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tarak Shah","contactNumber":"918320817048","isMyContact":true,"isBlocked":false,"chatName":"Tarak Shah","isGroupChat":false,"isMuted":false}
{"sessionId":"8487921219","messageId":"false_918320817048@c.us_69AF9344F784E13D614196BBBAC32257","from":"<EMAIL>","to":"<EMAIL>","body":"Okay. That's great. We can have interview tomorrow at 2 pm. What will be expected lower band and higher band of stipend?","type":"chat","timestamp":"2025-06-05T20:01:33.000Z","isForwarded":false,"hasMedia":false,"deviceType":"android","isGroup":false,"direction":"incoming","contactName":"Tarak Shah","contactNumber":"918320817048","isMyContact":true,"isBlocked":false,"chatName":"Tarak Shah","isGroupChat":false,"isMuted":false}
