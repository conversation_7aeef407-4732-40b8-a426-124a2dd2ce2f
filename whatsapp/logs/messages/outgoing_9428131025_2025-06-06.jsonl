{"sessionId":"**********","messageId":"true_status@broadcast_3EB018C32EFA9DA30CCDD4","from":"<EMAIL>","to":"status@broadcast","body":"😊 Home is indeed a special place where emotions and memories are made. Decorating your home can bring so much joy and make it feel even more personalized and cozy. What kind of decor style do you have in your home?","type":"chat","timestamp":"2025-06-06T01:08:39.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_status@broadcast_3EB0956AB7D33CE474C8B8","from":"<EMAIL>","to":"status@broadcast","body":"A beautiful mantra! 🙏 <PERSON><PERSON><PERSON> is a powerful and sacred mantra in Hinduism, invoking the energy of Lord <PERSON>. May it bring you peace, calmness, and spiritual growth. Are you a devotee of Lord <PERSON> or do you find solace in chanting this mantra?","type":"chat","timestamp":"2025-06-06T02:01:11.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_16478873910@c.us_F4B76A0856A659D364EA91BBCA83EF36","from":"<EMAIL>","to":"<EMAIL>","body":"https://www.instagram.com/p/DKh02yXskz3/?igsh=MW92bG55ZzV2amNwMA==","type":"chat","timestamp":"2025-06-06T03:37:06.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_status@broadcast_3EB07E2D6CB1CA1F10DBEF","from":"<EMAIL>","to":"status@broadcast","body":"It looks like you're sharing information about a real estate company, Shri Navkar Estate Dealer, and their Instagram handle. You're also offering various services such as:\n\n1. Property buying/selling/renting\n2. Land, house, shop, shed, and more\n3. Home loans, mortgage loans, machinery loans, and other types of loans\n\nIf someone is looking for real estate services or loan options in your area, they can reach out to you or check your Instagram page for more information. Is there something specific you'd like to discuss or ask?","type":"chat","timestamp":"2025-06-06T03:38:24.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_16478873910@c.us_05E5B103A36F89076DB33402EF791509","from":"<EMAIL>","to":"<EMAIL>","body":"https://www.instagram.com/reel/DJmZPXkPvz_/?igsh=MXVsbWlxYmNrbzY5","type":"chat","timestamp":"2025-06-06T03:44:02.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919016325519@c.us_CAD7536448FD1514E791349C9B2584AA","from":"<EMAIL>","to":"<EMAIL>","body":"-----   *Arihant Ai*   -----\n\nName : Arihant Ai (Software)\n\n-----   *Find Us At*   -----\n\nAdress: C-508, Dev Aurum, Prahladnagar, Ahmedabad - 380015.\n\n-----   *Call Us On*   -----\n\nMo. No. : **********\nPh.No.: 079 4039 9054\n\n-----   *Visit our website*   -----\nWebsite : arihantai.com\n\n-----   *Locate Us At*   -----\nGoogle Map : https://g.page/arihantai?share","type":"chat","timestamp":"2025-06-06T03:53:59.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919016325519@c.us_3EB04379904D9B4F6B4AC3","from":"<EMAIL>","to":"<EMAIL>","body":"Hello! \n\nHow can I assist you today with your accounting needs?","type":"chat","timestamp":"2025-06-06T03:54:02.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_16478873910@c.us_2A8E8DBDAA01EFBC19B684BC1C0FF09A","from":"<EMAIL>","to":"<EMAIL>","body":"https://www.instagram.com/reel/DGwi3LoxJgn/?igsh=dHUxeW43ZTcxNmFr","type":"chat","timestamp":"2025-06-06T03:56:05.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_status@broadcast_3EB04FC8D1D05026072EFC","from":"<EMAIL>","to":"status@broadcast","body":"It seems like you're sharing a passionate message in Gujarati, encouraging people to join a rally or event for a cause. The text mentions:\n\n* A call to action to join a rally on June 7, 2025, at 6:00 AM\n* The event starts from Rewa Sangh Vasana\n* The rally is for a noble cause, specifically \"Sant Suraksha Rally\"\n\nYou're urging people to take a break from their busy lives and dedicate two hours to this noble cause. You're also referencing a popular Gujarati phrase, \"ઓ વીર તારું શાસન મુજને પ્રાણ થકી છે પ્યારું,\" which translates to \"Oh brave one, your rule is dear to me like my life.\"\n\nIf you're looking for help with something specific or want to discuss this event, feel free to ask!","type":"chat","timestamp":"2025-06-06T04:02:42.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_status@broadcast_3EB0EC997D32583D37B7E1","from":"<EMAIL>","to":"status@broadcast","body":"I'm not able to access external links or websites. However, I can help you with any text or information you'd like to discuss or summarize. Please feel free to share the content of the tweet, and I'll be happy to assist you!","type":"chat","timestamp":"2025-06-06T04:29:44.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_37B873772E950886BB12956336E08557","from":"<EMAIL>","to":"<EMAIL>","body":"-----   *Arihant Ai*   -----\n\nName : Arihant Ai (Software)\n\n-----   *Find Us At*   -----\n\nAdress: C-508, Dev Aurum, Prahladnagar, Ahmedabad - 380015.\n\n-----   *Call Us On*   -----\n\nMo. No. : **********\nPh.No.: 079 4039 9054\n\n-----   *Visit our website*   -----\nWebsite : arihantai.com\n\n-----   *Locate Us At*   -----\nGoogle Map : https://g.page/arihantai?share","type":"chat","timestamp":"2025-06-06T04:33:20.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_3EB0ED82546A40E4FC8748","from":"<EMAIL>","to":"<EMAIL>","body":"How can I assist you today with your accounting needs?","type":"chat","timestamp":"2025-06-06T04:33:22.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_3EB0294789932CA61779D4","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like you shared a file name, but I'm not sure how that relates to your accounting needs. Could you tell me a bit about your business and what you're looking for assistance with?","type":"chat","timestamp":"2025-06-06T04:33:57.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_3EB08CCC4120B4F73614B3","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like you're looking for a job opportunity in the tech industry, specifically for an AL developer or QA analyst role related to iOS, and you're open to exploring opportunities in any technology. \n\nTo better understand your needs, are you currently looking for a full-time position, freelance work, or perhaps a remote opportunity?","type":"chat","timestamp":"2025-06-06T04:35:45.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_3EB03B984BD1FCD6026426","from":"<EMAIL>","to":"<EMAIL>","body":"I'm glad you're open to exploring various opportunities. \n\nTo help me narrow down some options for you, could you tell me a bit about your background and experience? For example, do you have any prior experience in AL development, QA analysis, or iOS development?","type":"chat","timestamp":"2025-06-06T04:38:01.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_1730A165ED8E51531C7F79DAE62DFD4C","from":"<EMAIL>","to":"<EMAIL>","body":"Today interviews are scheduled","type":"chat","timestamp":"2025-06-06T04:38:19.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_58AD23DAD06C99AB67144B9F593005C2","from":"<EMAIL>","to":"<EMAIL>","body":"You can come at 2:30","type":"chat","timestamp":"2025-06-06T04:38:27.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_3EB02A9486461A945224BF","from":"<EMAIL>","to":"<EMAIL>","body":"To better understand your qualifications, do you have any relevant skills or certifications in areas like programming languages (e.g., Swift, Java), testing frameworks, or Agile methodologies that could be beneficial for an AL developer or QA analyst role in iOS development?","type":"chat","timestamp":"2025-06-06T04:41:49.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_919726556399@c.us_3EB030849033434527B26F","from":"<EMAIL>","to":"<EMAIL>","body":"As an AL developer, you'll likely work on automating tasks, integrating systems, and optimizing workflows. \n\nAre you looking for opportunities in a specific industry, such as finance, healthcare, or e-commerce, where AL development skills are in high demand?","type":"chat","timestamp":"2025-06-06T04:42:27.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_CE722AB2D692A59F60267970FBA1E572","from":"<EMAIL>","to":"<EMAIL>","body":"Hii","type":"chat","timestamp":"2025-06-06T04:52:04.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_DF8584A2455EC3501AECD6FE76A8E495","from":"<EMAIL>","to":"<EMAIL>","body":"Drop message please","type":"chat","timestamp":"2025-06-06T04:52:07.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_3EB0B6B218BEB374E01B42","from":"<EMAIL>","to":"<EMAIL>","body":"Good morning! How can I assist you today with your accounting needs?","type":"chat","timestamp":"2025-06-06T04:53:51.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_3EB05FF4B580AAFBFCB875","from":"<EMAIL>","to":"<EMAIL>","body":"I don’t understand Gujarati yet, but I’m working on it. I will send you a message when we can talk in Gujarati.","type":"chat","timestamp":"2025-06-06T04:54:31.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_D209F7AD512B15EDC02F1FE98BB51A88","from":"<EMAIL>","to":"<EMAIL>","body":"How many candidates are there?","type":"chat","timestamp":"2025-06-06T04:54:48.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_3EB01A07EA174D772898DA","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like you're mentioning numbers. Could you tell me a bit about what you're trying to accomplish or discuss regarding these numbers? Are they related to your business or something else?","type":"chat","timestamp":"2025-06-06T04:55:08.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_D1D89D65EA7EEB3460DEAEF28249B313","from":"<EMAIL>","to":"<EMAIL>","body":"Okk","type":"chat","timestamp":"2025-06-06T04:55:34.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_AA171CBECB749CBEE0C3A369BD0E49FD","from":"<EMAIL>","to":"<EMAIL>","body":"45 minutes","type":"chat","timestamp":"2025-06-06T04:55:42.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_916355135205@c.us_3EB0915566C3604D640C15","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like you're okay with that. Let's try to dive a bit deeper. Are you looking for help with accounting or financial services for your business? If so, what specific areas are you struggling with or need assistance in?","type":"chat","timestamp":"2025-06-06T04:55:55.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_status@broadcast_3EB061C822B1A9A79DF642","from":"<EMAIL>","to":"status@broadcast","body":"It seems like you're advertising or promoting armrests for cars. Can you tell me more about what you're offering? Are these universal armrests that fit most cars, or do you have specific models for certain makes and brands? What are the features and benefits of these armrests?","type":"chat","timestamp":"2025-06-06T05:06:16.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB0951C0155EB88756B80","from":"<EMAIL>","to":"<EMAIL>","body":"Hello! I'm still here to help you with your e-commerce website for gold and silver products. Let's get started!\n\nTo better understand your requirements, can you please tell me:\n\n1. What type of products do you want to sell on your website (e.g., gold jewelry, silver coins, bullion, etc.)?\n2. Do you have any specific design or branding preferences (e.g., color scheme, logo, typography)?\n\nFeel free to share any additional details or ideas you have, and I'll do my best to assist you! 😊 \n\nAlso, I can give you a brief overview of our process:\n\n* We'll discuss your project requirements and goals\n* I'll provide a customized proposal and timeline\n* Our team will design and develop your e-commerce website\n* We'll test and launch your website\n* Ongoing support and maintenance\n\nLet me know if you have any questions or if there's anything specific you'd like to know!","type":"chat","timestamp":"2025-06-06T05:06:21.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB08AFD0344AEEE3999D6","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like you're preparing for a series of interviews and want me to conduct the final one. I'm ready to help!\n\nTo confirm, there are 5 people interviewing, and I'll be conducting the last interview. Is that correct?\n\nBefore we begin, I just want to clarify a few details:\n\n1. What is the position or role that these candidates are interviewing for?\n2. Are there any specific skills or qualifications you're looking for in the candidates?\n3. Do you have any particular format or structure in mind for the interview?\n\nLet me know, and I'll do my best to assist you!","type":"chat","timestamp":"2025-06-06T05:06:48.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_DF083AB10FFBBBB444BD7E62DE629F2B","from":"<EMAIL>","to":"<EMAIL>","body":"Okk","type":"chat","timestamp":"2025-06-06T05:09:04.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_DAE15E03ED203CE2DD1A7FFA9152E9C6","from":"<EMAIL>","to":"<EMAIL>","body":"Is he good?","type":"chat","timestamp":"2025-06-06T05:09:09.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB01C7F9C3AA8D5D1BA19","from":"<EMAIL>","to":"<EMAIL>","body":"I apologize for the technical error. It seems like there was an issue with retrieving candidate information.\n\nLet's start fresh! 😊\n\nAs the final interviewer for the 5 candidates, I'd like to conduct a brief interview to assess their fit for the role.\n\nCan you please provide me with some context about the position, such as:\n\n1. Job title and responsibilities?\n2. Required skills and qualifications?\n3. What are the key challenges facing the team or department?\n\nThis will help me tailor my questions and evaluate the candidates effectively.","type":"chat","timestamp":"2025-06-06T05:09:19.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_44B4DDB2E34000FDF0F0127EF14F8631","from":"<EMAIL>","to":"<EMAIL>","body":"Experience?","type":"chat","timestamp":"2025-06-06T05:09:23.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB0AECCE5395FD78FE103","from":"<EMAIL>","to":"<EMAIL>","body":"The candidates have approximately2.5 years of experience in their field. That's a good starting point!\n\nTo assess their fit for the role, I'll ask some behavioral and technical questions. Please provide me with the candidates' names or IDs, and I'll start the interview process.\n\nAlso, are there any specific areas you'd like me to focus on during the interview, such as:\n\n* Technical skills (e.g., programming languages, software proficiency)?\n* Soft skills (e.g., communication, teamwork, problem-solving)?\n* Industry knowledge or experience?\n\nLet me know, and I'll begin the interview process!","type":"chat","timestamp":"2025-06-06T05:09:29.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB0CEAA7B40DC3455D5DF","from":"<EMAIL>","to":"<EMAIL>","body":"I'd be happy to conduct a more in-depth conversation with the candidate.\n\nCan you please provide me with some more information about the candidate, such as:\n\n* Their current role and responsibilities?\n* Their educational background?\n* Any relevant projects or achievements they'd like to highlight?\n\nThis will help me have a more informed conversation and assess their fit for the role.\n\nAlso, are there any specific topics or areas you'd like me to explore during our conversation?","type":"chat","timestamp":"2025-06-06T05:09:38.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB0DC532F7D05536FF805","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like the candidate has experience with manual and automation testing, but may not recall specific details clearly.\n\nTo explore this further, I can ask:\n\n* Can you elaborate on your experience with manual testing? What types of tests have you performed in the past?\n* How have you used automation testing in your previous roles? What tools or frameworks have you worked with?\n* Although you may not recall specific details, can you describe your general experience with backend development? Have you worked with any programming languages or databases?\n\nThis will help me understand their strengths and weaknesses, as well as areas where they may need improvement.","type":"chat","timestamp":"2025-06-06T05:10:23.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB01E8A1B6E4935214223","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like there are2 people who have been selected or shortlisted for the position.\n\nTo proceed, I'd like to know more about the selection criteria and the role requirements. Can you please provide me with:\n\n* The job description and key responsibilities?\n* The skills and qualifications required for the position?\n* Any specific challenges or pain points the team is facing that the new hire will need to address?\n\nThis will help me better understand the context and assess whether the2 shortlisted candidates are a good fit for the role.","type":"chat","timestamp":"2025-06-06T05:10:30.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB050DE7B7073EEE67112","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like Shruti is the third candidate.\n\nTo evaluate Shruti's fit for the role, I'd like to know more about her background and experience. Can you please provide me with:\n\n* Shruti's resume or a brief summary of her experience and skills?\n* Any notable achievements or accomplishments she's made in her previous roles?\n\nThis will help me assess her qualifications and potential for the position.","type":"chat","timestamp":"2025-06-06T05:10:40.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_3EB0B5110CDC6079C8B5A7","from":"<EMAIL>","to":"<EMAIL>","body":"It seems like there are multiple candidates being considered, and you'd like me to evaluate them.\n\nTo ensure I provide accurate assessments, I'd like to confirm:\n\n* Can you provide me with the names and backgrounds of all candidates being considered (including Shruti)?\n* What are the key qualifications, skills, and experience required for the role?\n* Are there any specific challenges or pain points the team is facing that the new hire will need to address?\n\nThis will enable me to provide informed evaluations and recommendations.","type":"chat","timestamp":"2025-06-06T05:10:54.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_status@broadcast_3EB054563975BF92C0DA84","from":"<EMAIL>","to":"status@broadcast","body":"Thank you for the kind wish! However, I'm just a chatbot, I don't have a personal life or relationships, so I don't have anniversaries to celebrate. I exist solely to assist and provide information to users like you!\n\nBut I'm happy to help you celebrate a special occasion or provide information on court marriages, if that's what you're looking for!","type":"chat","timestamp":"2025-06-06T05:11:10.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_917227829979@c.us_EAA8EFF2D2413C17D429E35336363442","from":"<EMAIL>","to":"<EMAIL>","body":"But eno long interview lai ne rakho","type":"chat","timestamp":"2025-06-06T05:22:49.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_status@broadcast_3EB0EE1DB66D3F683D4345","from":"<EMAIL>","to":"status@broadcast","body":"It seems like you're advertising or promoting a car mat for the Hyundai Verna Q PO MAX Premium Quality. Can you tell me more about the product? What are its features and benefits? Is it a universal mat or specifically designed for the Hyundai Verna? What is the material used, and is it easy to install and clean?","type":"chat","timestamp":"2025-06-06T05:33:41.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_************@c.us_3EB0FAB14444D055B611D5","from":"<EMAIL>","to":"<EMAIL>","body":"How can I assist you today with your accounting needs?","type":"chat","timestamp":"2025-06-06T05:53:44.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_************@c.us_F712CB889DB9FF7CEB65C9222580D0D6","from":"<EMAIL>","to":"<EMAIL>","body":"Hii","type":"chat","timestamp":"2025-06-06T05:55:03.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_918008884863@c.us_3AB42E1F1C0AF584DF24490148C5275C","from":"<EMAIL>","to":"<EMAIL>","body":"BEGIN:VCARD\nVERSION:3.0\nN:;;;;\nFN:Rajratna Vijayji\nTEL;type=Mobile;waid=************:+91 72050 57272\nPHOTO;BASE64: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\nX-WA-BIZ-NAME:Rajratna Vijayji\nEND:VCARD","type":"vcard","timestamp":"2025-06-06T05:55:29.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_************@c.us_3EB0435657F132EC4B5B18","from":"<EMAIL>","to":"<EMAIL>","body":"Could you tell me a bit about your business and what you're currently managing in-house?","type":"chat","timestamp":"2025-06-06T05:56:38.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_************@c.us_3EB0EDE88F51039321D715","from":"<EMAIL>","to":"<EMAIL>","body":"What type of support are you looking for — ongoing monthly services or something more short-term?","type":"chat","timestamp":"2025-06-06T05:56:41.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
{"sessionId":"**********","messageId":"true_918008884863@c.us_EE04D5EC310CDD710EB6496F8441D893","from":"<EMAIL>","to":"<EMAIL>","body":"","type":"image","timestamp":"2025-06-06T06:22:53.000Z","hasMedia":true,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********","mediaType":"image/jpeg","mediaSize":193744,"savedMediaPath":"/mnt/whatsapp/media/**********/2025-06-06/true_918008884863@c.us_EE04D5EC310CDD710EB6496F8441D893_062254.jpg"}
{"sessionId":"**********","messageId":"true_918008884863@c.us_3EB055902BC78583EF7D3D","from":"<EMAIL>","to":"<EMAIL>","body":"I'm here to assist you. However, I need a bit more information to provide a helpful response. Could you please clarify what you are looking for? Are you trying to access a specific system or platform, or do you need help with something else? I'll do my best to guide you. \n\nIf you are looking for general information or assistance, feel free to ask, and I'll respond accordingly. \n\nHow can I assist you today?","type":"chat","timestamp":"2025-06-06T06:23:14.000Z","hasMedia":false,"direction":"outgoing","contactName":"Test Tapasvi","contactNumber":"91**********"}
