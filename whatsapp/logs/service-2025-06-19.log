{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T00:00:00.162Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:00:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:00:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:01:00.168Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:01:05.909Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:01:19.954Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:01:19.956Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:01:54.372Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:02:00.169Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:02:19.953Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:02:19.955Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:02:20.170Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:02:40.174Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:02:54.428Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:03:00.179Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:03:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:03:19.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:03:20.180Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:03:54.375Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:04:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:04:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:04:31.801Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:04:54.458Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:05:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:05:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:05:31.800Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:05:51.805Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:05:54.458Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:06:11.809Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:06:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:06:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:06:31.817Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:06:51.816Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:06:55.024Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:07:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:07:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:07:55.172Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:08:02.684Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:08:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:08:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:08:55.051Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:09:02.688Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:09:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:09:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:09:22.692Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:09:42.693Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:09:55.116Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:10:02.697Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:10:19.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:10:19.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:10:22.697Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:10:55.435Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:11:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:11:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:11:33.606Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:11:55.937Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:12:19.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:12:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:12:33.606Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:12:53.611Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:12:55.941Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:13:13.615Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:13:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:13:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:13:33.619Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:13:53.620Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:13:55.965Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:14:19.954Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:14:19.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:14:55.942Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:15:05.113Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:15:19.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:15:19.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:15:55.969Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:16:05.117Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:16:19.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:16:19.958Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:16:25.121Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:16:45.126Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:16:56.122Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:17:05.126Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:17:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:17:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:17:25.130Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:17:56.013Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:18:19.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:18:19.958Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:18:36.398Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:18:56.033Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:19:19.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:19:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:19:36.403Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:19:55.992Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:19:56.407Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:20:16.408Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:20:19.953Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:20:19.955Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:20:36.412Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:20:56.026Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:20:56.416Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:21:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:21:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:21:56.123Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:22:07.339Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:22:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:22:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:22:56.010Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:23:07.344Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:23:19.958Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:23:19.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:23:27.348Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:23:47.352Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:23:56.007Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:24:07.353Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:24:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:24:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:24:27.353Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:24:56.020Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:25:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:25:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:25:38.322Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:25:56.217Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:26:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:26:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:26:38.326Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:26:56.138Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:26:58.331Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:27:18.331Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:27:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:27:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:27:38.332Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:27:56.134Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:27:58.332Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:28:19.948Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:28:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:28:56.131Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:29:09.952Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:29:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:29:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:29:56.203Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:30:09.953Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:30:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:30:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:30:29.953Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:30:49.954Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:30:56.152Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:31:09.958Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:31:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:31:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:31:29.962Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:31:56.194Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:32:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:32:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:32:40.776Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:32:56.161Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:33:19.956Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:33:19.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:33:40.777Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:33:56.221Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:34:00.778Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:34:19.960Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:34:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:34:20.779Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:34:40.780Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:34:56.231Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:35:00.784Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:35:19.957Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:35:19.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:35:56.172Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:36:11.936Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:36:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:36:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:36:56.172Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:37:11.941Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:37:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:37:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:37:31.946Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:37:51.950Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:37:56.175Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:38:11.956Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:38:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:38:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:38:31.960Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:38:56.196Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:39:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:39:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:39:43.015Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:39:56.275Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:40:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:40:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:40:43.019Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:40:56.267Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:41:03.020Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:41:19.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:41:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:41:23.023Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:41:43.027Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:41:56.236Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:42:03.029Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:42:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:42:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:42:56.233Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:43:13.903Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:43:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:43:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:43:56.255Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:44:13.903Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:44:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:44:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:44:33.907Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:44:53.908Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:44:56.257Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:45:13.914Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:45:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:45:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:45:33.914Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:45:56.228Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:46:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:46:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:46:45.035Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:46:56.232Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:47:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:47:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:47:45.039Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:47:56.226Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:48:05.040Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:48:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:48:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:48:25.044Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:48:45.048Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:48:56.202Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:49:05.052Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:49:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:49:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:49:56.286Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:50:15.888Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:50:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:50:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:50:56.278Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:51:15.891Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:51:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:51:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:51:35.895Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:51:55.900Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:51:56.254Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:52:15.904Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:52:19.959Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:52:19.960Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:52:35.908Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:52:56.224Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:53:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:53:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:53:46.921Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:53:56.236Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:54:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:54:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:54:46.920Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:54:56.324Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:55:06.924Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:55:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:55:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:55:26.928Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:55:46.932Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:55:56.264Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:56:06.937Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:56:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:56:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:56:56.240Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:57:17.795Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:57:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:57:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:57:56.324Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:58:17.794Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:58:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:58:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:58:37.795Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:58:56.277Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:58:57.795Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:59:17.795Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T00:59:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:59:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T00:59:37.800Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T00:59:56.274Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T01:00:00.953Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:00:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:00:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:00:49.021Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:00:56.916Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:01:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:01:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:01:49.025Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:01:56.939Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:02:09.030Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:02:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:02:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:02:29.034Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:02:49.035Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:02:56.967Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:03:09.039Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:03:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:03:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:03:56.938Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:04:19.956Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:04:19.961Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:04:20.207Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:04:56.941Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:05:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:05:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:05:20.210Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:05:40.215Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:05:56.966Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:06:00.219Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:06:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:06:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:06:20.220Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:06:40.224Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:06:56.986Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:07:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:07:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:07:51.399Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:07:56.971Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:08:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:08:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:08:51.402Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:08:56.959Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:09:11.407Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:09:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:09:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:09:31.411Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:09:51.412Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:09:57.024Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:10:11.415Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:10:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:10:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:10:57.082Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:11:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:11:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:11:22.485Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:11:57.168Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:12:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:12:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:12:22.489Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:12:42.493Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:12:57.183Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:13:02.493Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:13:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:13:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:13:22.498Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:13:42.498Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:13:57.152Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:14:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:14:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:14:53.580Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:14:57.204Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:15:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:15:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:15:53.588Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:15:57.607Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:16:13.592Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:16:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:16:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:16:33.596Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:16:53.600Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:16:57.779Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:17:13.604Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:17:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:17:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:17:57.680Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:18:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:18:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:18:24.685Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:18:58.315Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:19:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:19:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:19:24.686Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:19:44.686Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:19:57.548Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:20:04.690Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:20:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:20:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:20:24.695Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:20:44.695Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:20:57.587Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:21:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:21:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:21:55.693Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:21:57.569Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:*************","level":"info","message":"GET /","timestamp":"2025-06-19T01:22:11.164Z","userAgent":"Mozilla/5.0 zgrab/0.x"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:22:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:22:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:22:55.694Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:22:57.569Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:23:15.700Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:23:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:23:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:23:35.705Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:23:55.709Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:23:57.680Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:24:15.713Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:24:19.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:24:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:24:57.563Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:25:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:25:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:25:26.578Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:25:57.569Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:26:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:26:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:26:26.583Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:26:46.584Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:26:57.631Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:27:06.585Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:27:19.962Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:27:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:27:26.585Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:27:46.586Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:27:57.580Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:28:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:28:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:28:57.599Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:28:57.656Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:29:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:29:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:29:57.577Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:29:57.658Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:30:17.658Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:30:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:30:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:30:37.662Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:30:57.643Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:30:57.664Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:31:17.664Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:31:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:31:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:31:57.670Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:32:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:32:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:32:28.582Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:32:57.606Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:33:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:33:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:33:28.587Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:33:48.592Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:33:57.631Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:34:08.596Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:34:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:34:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:34:28.601Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:34:48.605Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:34:57.587Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:35:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:35:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:35:57.652Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:35:59.566Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:36:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:36:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:36:57.649Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:36:59.571Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:37:19.575Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:37:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:37:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:37:39.580Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:37:57.600Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:37:59.580Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:38:19.581Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:38:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:38:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:38:57.667Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:39:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:39:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:39:30.724Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:39:57.611Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:40:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:40:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:40:30.726Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:40:50.730Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:40:57.611Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:41:10.731Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:41:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:41:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:41:30.735Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:41:50.739Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:41:57.633Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:42:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:42:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:42:57.677Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:43:01.768Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:43:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:43:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:43:57.682Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:44:01.770Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:44:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:44:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:44:21.774Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:44:41.774Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:44:57.650Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:45:01.779Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:45:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:45:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:45:21.780Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:45:57.650Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:46:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:46:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:46:32.977Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:46:57.677Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:47:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:47:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:47:32.980Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:47:52.985Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:47:57.663Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:48:12.989Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:48:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:48:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:48:32.989Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:48:52.994Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:48:57.665Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:49:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:49:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:49:57.684Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:50:03.887Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:50:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:50:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:50:57.676Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:51:03.891Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:51:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:51:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:51:23.896Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:51:43.901Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:51:57.715Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:52:03.901Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:52:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:52:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:52:23.902Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:52:57.680Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:53:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:53:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:53:34.749Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:53:57.710Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:54:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:54:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:54:34.754Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:54:54.754Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:54:57.704Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:55:14.754Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:55:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:55:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:55:34.755Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:55:54.759Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:55:58.092Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:56:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:56:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:56:58.087Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:57:05.772Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:57:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:57:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:57:58.084Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:58:05.773Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:58:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:58:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:58:25.777Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:58:45.782Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:58:58.095Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:59:05.786Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:59:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T01:59:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T01:59:25.790Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T01:59:58.107Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T02:00:00.792Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:00:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:00:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:00:36.876Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:00:58.091Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:01:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:01:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:01:36.880Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:01:56.884Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:01:58.295Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:02:16.889Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:02:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:02:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:02:36.893Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:02:56.893Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:02:58.378Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:03:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:03:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:03:58.404Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:04:07.931Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:04:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:04:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:04:58.840Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:05:07.931Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:05:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:05:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:05:27.936Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:05:47.940Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:05:58.962Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:06:07.945Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:06:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:06:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:06:27.949Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:06:58.892Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:07:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:07:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:07:38.933Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:07:58.921Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:08:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:08:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:08:38.938Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:08:58.908Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:08:58.938Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:09:18.943Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:09:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:09:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:09:38.943Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:09:58.943Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:09:58.979Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:10:19.964Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:10:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:10:59.093Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:11:10.047Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:11:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:11:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:11:59.000Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:12:10.048Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:12:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:12:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:12:30.052Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:12:50.058Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:12:58.927Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:13:10.062Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:13:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:13:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:13:30.066Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:13:58.930Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:14:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:14:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:14:40.903Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:14:58.981Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:15:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:15:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:15:40.903Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:15:59.135Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:16:00.904Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:16:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:16:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:16:20.909Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:16:40.909Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:16:59.314Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:17:00.914Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:17:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:17:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:17:59.216Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:18:11.788Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:18:19.967Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:18:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:18:59.203Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:19:11.789Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:19:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:19:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:19:31.789Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:19:51.794Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:19:59.252Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:20:11.798Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:20:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:20:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:20:31.798Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:20:59.207Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:21:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:21:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:21:42.923Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:21:59.208Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:22:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:22:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:22:42.924Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:22:59.199Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:23:02.929Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:23:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:23:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:23:22.933Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:23:42.937Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:23:59.201Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:24:02.941Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:24:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:24:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:24:59.209Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:25:14.216Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:25:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:25:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:25:59.196Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:26:14.215Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:26:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:26:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:26:34.219Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:26:54.220Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:26:59.223Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:27:14.221Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:27:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:27:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:27:34.221Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:27:59.216Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:28:19.958Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:28:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:28:45.189Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:28:59.284Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:29:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:29:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:29:45.189Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:29:59.276Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:30:05.194Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:30:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:30:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:30:25.198Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:30:45.202Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:30:59.280Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:31:05.207Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:31:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:31:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:31:59.307Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:32:16.757Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:32:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:32:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:32:59.239Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:33:16.758Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:33:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:33:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:33:36.758Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:33:56.763Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:33:59.208Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:34:16.767Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:34:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:34:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:34:36.771Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:34:59.207Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:35:19.963Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:35:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:35:47.858Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:35:59.242Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:36:19.965Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:36:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:36:47.858Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:36:59.292Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:37:07.862Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:37:19.968Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:37:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:37:27.868Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:37:47.872Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:37:59.298Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:38:07.872Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:38:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:38:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:38:59.299Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:39:18.745Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:39:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:39:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:39:59.304Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:40:18.787Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:40:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:40:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T02:40:20.246Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T02:40:25.204Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T02:40:26.537Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:40:38.753Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:40:58.758Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:40:59.300Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:41:18.764Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:41:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:41:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:41:38.768Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:41:59.313Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:42:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:42:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:42:49.658Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:42:59.317Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:43:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:43:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:43:49.662Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T02:43:50.135Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:43:59.315Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:44:09.667Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:44:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:44:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:44:29.671Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:44:49.675Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:44:59.413Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:45:09.676Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:45:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:45:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:45:59.358Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:46:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:46:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:46:20.698Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:46:59.389Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:47:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:47:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:47:20.699Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:47:40.699Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:47:59.343Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:48:00.700Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:48:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:48:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:48:20.700Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:48:40.701Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:48:59.398Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:49:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:49:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:49:51.838Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:49:59.387Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:50:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:50:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:50:51.842Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:50:59.341Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:51:11.843Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:51:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:51:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:51:31.847Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:51:51.851Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:51:59.323Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:52:11.856Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:52:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:52:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:52:59.335Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:53:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:53:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:53:22.690Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:53:59.377Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:54:19.966Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:54:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:54:22.690Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:54:42.694Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:54:59.386Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:55:02.699Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:55:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:55:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:55:22.699Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:55:42.704Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:55:59.373Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:56:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:56:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T02:56:21.058Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:56:53.824Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:56:59.459Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:57:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:57:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T02:57:52.552Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:57:53.826Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T02:57:57.130Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:57:59.388Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:58:13.830Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:58:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:58:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:58:33.833Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:58:53.840Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:58:59.407Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T02:59:13.840Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T02:59:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:59:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T02:59:59.404Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T03:00:00.643Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:00:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:00:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:00:24.957Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:00:36.308Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:00:59.471Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:01:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:01:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:01:24.962Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:01:44.966Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:01:59.436Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:02:04.971Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:02:11.040Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:02:12.366Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:02:16.983Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:02:19.009Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:02:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:02:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:02:20.261Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:02:24.972Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:02:44.976Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:03:12.785Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:03:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:03:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:03:55.946Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:04:05.216Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:04:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:04:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:04:55.950Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:05:06.062Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:05:15.954Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:05:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:05:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:05:35.959Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:05:55.964Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:06:05.804Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:06:15.968Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:06:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:06:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:************","level":"info","message":"GET /","timestamp":"2025-06-19T03:06:53.361Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:************","level":"info","message":"GET /favicon.ico","timestamp":"2025-06-19T03:06:56.045Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:06:56.049Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:06:56.397Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:07:05.836Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:07:06.589Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:07:06.610Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:07:08.741Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:07:08.746Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:07:19.969Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:07:19.970Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:07:27.148Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:08:05.820Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:08:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:08:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:08:27.153Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:08:47.157Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:09:06.426Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:09:07.162Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:09:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:09:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:09:27.166Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:09:47.168Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:10:05.892Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:10:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:10:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:10:58.189Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:11:05.790Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:11:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:11:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-19T03:11:24.941Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:11:58.190Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:12:06.061Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:12:18.190Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:12:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:12:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:12:38.199Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:12:58.200Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:13:05.990Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:13:18.206Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:13:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:13:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:14:05.984Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:14:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:14:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:14:29.671Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:15:05.969Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:15:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:15:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:15:29.673Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:15:49.674Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:16:06.307Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:16:09.678Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:16:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:16:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:16:29.682Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:16:49.687Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:17:06.087Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:17:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:17:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:18:01.005Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:18:06.021Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:18:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:18:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:19:01.009Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:19:06.155Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:19:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:19:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:19:21.012Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:19:41.016Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:20:01.020Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:20:06.501Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:20:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:20:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:20:21.024Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:21:06.720Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:21:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:21:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:21:31.894Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:22:06.561Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:22:19.971Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:22:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:22:31.899Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:22:51.902Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:23:06.594Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:23:11.907Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:23:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:23:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:23:31.907Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:23:51.911Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:24:06.699Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:24:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:24:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:25:03.195Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:25:06.568Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:25:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:25:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:26:03.200Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:26:06.597Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:26:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:26:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:26:23.204Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:26:43.208Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:27:03.212Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:27:06.576Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:27:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:27:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:27:23.214Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:28:06.707Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:28:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:28:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:28:34.085Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:29:06.785Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:29:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:29:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:29:34.088Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:29:54.092Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:30:06.896Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:30:14.096Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:30:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:30:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:30:34.100Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:30:54.105Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:31:06.915Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:31:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:31:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:32:05.047Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:32:06.910Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:32:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:32:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:33:05.048Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:33:06.684Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:33:19.960Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:33:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:33:25.048Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:33:45.048Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:34:05.053Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:34:06.699Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:34:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:34:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:34:25.053Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:35:06.830Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:35:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:35:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:35:36.192Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:36:07.216Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:36:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:36:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:36:36.193Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:36:56.194Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:37:07.173Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:37:16.195Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:37:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:37:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:37:36.203Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:37:56.201Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:38:07.208Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:38:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:38:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:38:40.187Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:39:07.419Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:39:07.574Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:39:14.542Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:39:15.306Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:39:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:39:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:40:07.394Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:40:07.426Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:40:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:40:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:40:27.428Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:40:34.377Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:40:47.432Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T03:40:56.644Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:41:07.205Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:41:07.432Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:41:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:41:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:41:27.437Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:42:07.709Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:42:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:42:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:42:38.375Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:43:07.204Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:43:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:43:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:43:38.380Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:43:58.384Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:44:07.318Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:44:18.388Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:44:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:44:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:44:38.392Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:44:58.393Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:45:07.281Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:45:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:45:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:46:07.483Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:46:09.568Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:46:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:46:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:47:07.468Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:47:09.572Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:47:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:47:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:47:29.577Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:47:49.581Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:48:07.373Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:48:09.585Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:48:19.972Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:48:19.973Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:48:29.589Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:49:08.325Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:49:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:49:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:49:40.799Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:50:08.345Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:50:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:50:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:50:40.800Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:51:00.801Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:51:08.262Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:51:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:51:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:51:20.806Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:51:40.810Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:52:00.814Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:52:08.218Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:52:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:52:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:53:08.304Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:53:11.918Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:53:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:53:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:54:08.439Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:54:11.919Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:54:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:54:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:54:31.919Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:54:51.920Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:55:08.313Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:55:11.921Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:55:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:55:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:55:31.925Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:56:08.770Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:56:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:56:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:56:43.003Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:57:08.661Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:57:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:57:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:57:43.007Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:58:03.012Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:58:08.835Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:58:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:58:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:58:23.017Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:58:43.018Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T03:59:03.022Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:59:09.012Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T03:59:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T03:59:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T04:00:00.516Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:00:08.799Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:00:13.887Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:00:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:00:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:01:08.796Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:01:13.886Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:01:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:01:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:01:33.888Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:01:53.891Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:02:08.748Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:02:13.896Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:02:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:02:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:02:33.900Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:03:08.800Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:03:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:03:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:03:44.953Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:04:08.959Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:04:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:04:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:04:44.955Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:05:04.955Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:05:08.965Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:05:16.712Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:05:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:05:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:05:24.956Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:05:44.957Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:06:04.957Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:06:05.847Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:06:09.169Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:06:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:06:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:07:08.951Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:07:15.858Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:07:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:07:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:07:20.016Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:07:51.475Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:08:08.977Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:08:15.862Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:08:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:08:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:08:35.867Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:08:55.871Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:09:08.940Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:09:15.875Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:09:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:09:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:09:35.883Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:10:04.970Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:10:09.078Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:10:19.975Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:10:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:10:46.851Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:11:08.992Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:11:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:11:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:11:46.852Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:12:06.857Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:12:09.012Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:12:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:12:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:12:21.832Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:12:26.862Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:12:46.863Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:12:56.272Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:13:06.864Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:13:08.980Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:13:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:13:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:14:08.999Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:14:17.734Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:14:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:14:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:14:42.623Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:15:09.708Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:15:17.736Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:15:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:15:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:15:37.735Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:15:57.740Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:16:10.584Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:16:17.740Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:16:19.974Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:16:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:16:37.746Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:17:10.607Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:17:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:17:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:17:48.832Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:18:10.626Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:18:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:18:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:18:48.832Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:19:08.837Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:19:10.642Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:19:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:19:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:19:28.840Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:19:48.844Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:20:08.845Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:20:10.594Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:20:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:20:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:21:10.584Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:21:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:21:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:21:20.005Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:22:10.590Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:22:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:22:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:22:20.007Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:22:40.007Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:23:00.008Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:23:10.596Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:23:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:23:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:23:20.008Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:23:40.012Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:24:10.640Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:24:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:24:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:24:50.851Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:25:11.287Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:25:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:25:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:25:50.852Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:26:10.857Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:26:12.239Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:26:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:26:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:26:30.861Z"}
{"body":{"message":"Hi Gatsby","to":"919428131025","type":"text"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/send","timestamp":"2025-06-19T04:26:48.116Z","userAgent":"python-requests/2.25.1"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:26:50.866Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:27:10.866Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:27:12.916Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:27:19.976Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:27:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:28:12.977Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:28:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:28:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:28:22.144Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:29:12.935Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:29:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:29:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:29:22.147Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:29:42.149Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:30:02.149Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:30:13.827Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:30:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:30:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:30:22.153Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:30:42.157Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:31:13.507Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:31:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:31:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:31:53.215Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:32:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:32:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:32:31.872Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:32:53.219Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:33:13.225Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:33:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:33:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:26.131Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:29.407Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:33:29.903Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:30.616Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:32.027Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:33.194Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:33:33.225Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:35.420Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:36.541Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T04:33:38.583Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:33:53.225Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:34:13.226Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:34:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:34:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:34:29.906Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:35:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:35:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:35:24.359Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:35:29.851Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:36:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:36:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:36:24.365Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:36:29.878Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:36:44.369Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:37:04.373Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:37:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:37:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:37:24.374Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:37:29.869Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:37:44.378Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:38:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:38:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:38:29.965Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:38:55.509Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:39:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:39:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:39:30.083Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:39:55.521Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:40:15.520Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:40:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:40:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:40:29.898Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:40:35.523Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:40:55.529Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:41:15.533Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:41:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:41:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:41:29.880Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:42:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:42:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:42:26.485Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:42:30.675Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:43:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:43:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:43:26.486Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:43:30.724Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:43:46.490Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:44:06.492Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:44:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:44:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:44:26.492Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:44:30.732Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:44:46.496Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:45:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:45:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:45:30.684Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:45:57.419Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:46:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:46:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:46:30.765Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:46:57.419Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:47:17.420Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:47:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:47:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:47:30.627Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:47:37.424Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:47:57.428Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:48:17.433Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:48:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:48:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:48:30.678Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:49:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:49:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:49:28.692Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:49:30.668Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:50:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:50:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:50:28.699Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:50:30.721Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:50:48.704Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:51:08.704Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:51:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:51:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:51:28.708Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:51:30.686Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:51:48.709Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:52:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:52:19.983Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:52:30.692Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:52:59.589Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:53:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:53:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:53:30.713Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:*************","level":"info","message":"GET /","timestamp":"2025-06-19T04:53:32.294Z","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:53:59.594Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:54:19.602Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:54:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:54:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:54:31.680Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:54:39.601Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:54:59.601Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:55:19.605Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:55:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:55:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:55:31.701Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:56:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:56:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:56:30.739Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:56:31.628Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:57:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:57:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:57:30.744Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:57:31.745Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:57:50.745Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:58:10.749Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:58:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:58:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:58:30.750Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:58:31.671Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T04:58:50.754Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T04:59:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:59:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T04:59:31.698Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T05:00:00.375Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:00:01.553Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:00:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:00:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:00:32.760Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:01:01.562Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:01:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:01:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:01:21.561Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:01:32.924Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:01:41.566Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:02:01.567Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:02:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:02:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:02:21.566Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:02:32.849Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:03:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:03:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:03:32.683Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:03:32.872Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:04:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:04:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:04:32.689Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:04:32.860Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:04:52.689Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:05:12.693Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:05:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:05:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:05:32.693Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:05:32.848Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:05:52.698Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:06:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:06:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:06:32.873Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:07:03.583Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:07:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:07:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:07:32.881Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:08:03.588Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:08:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:08:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:08:23.588Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:08:32.948Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:08:43.591Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:09:03.590Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:09:19.979Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:09:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:09:23.590Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:09:32.895Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:10:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:10:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:10:32.993Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:10:34.516Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:11:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:11:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:11:32.948Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:11:34.521Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:11:54.526Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:12:14.530Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:12:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:12:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:12:32.965Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:12:34.534Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:12:54.543Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:13:19.978Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:13:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:13:32.900Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:14:05.654Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:14:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:14:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:14:32.992Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:15:05.655Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:15:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:15:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:15:25.656Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:15:32.993Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:15:45.657Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:16:05.661Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:16:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:16:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:16:25.667Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:16:32.920Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:17:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:17:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:17:33.096Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:17:36.935Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:18:06.073Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:18:15.639Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:18:19.980Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:18:19.981Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:18:32.969Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:18:36.940Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:18:56.944Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:19:16.946Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:19:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:19:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:19:32.988Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:19:36.950Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:19:56.956Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:20:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:20:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:20:33.094Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:21:08.131Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:21:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:21:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:21:33.063Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:22:08.136Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:22:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:22:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:22:28.139Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:22:33.075Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:22:48.144Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:23:08.148Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:23:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:23:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:23:28.152Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:23:33.015Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:24:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:24:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:24:33.044Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:24:39.004Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:25:19.982Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:25:19.984Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:25:30.509Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:25:33.073Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:25:39.004Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:25:59.007Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:26:19.009Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:26:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:26:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:26:33.042Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:26:39.013Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:26:59.014Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:27:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:27:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:27:33.606Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:28:09.904Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:28:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:28:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"number":"919428131025","text":"The Great Gatsby"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/sendText","timestamp":"2025-06-19T05:28:32.629Z","userAgent":"python-requests/2.25.1"}
{"body":{"message":"The Great Gatsby","to":"<EMAIL>","type":"text"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/send","timestamp":"2025-06-19T05:28:32.638Z","userAgent":"python-requests/2.25.1"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-19T05:28:32.659Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:28:33.664Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Message sent from session ********** to <EMAIL>","timestamp":"2025-06-19T05:28:51.546Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:29:09.906Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/chats","timestamp":"2025-06-19T05:29:19.576Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:29:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:29:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:29:29.917Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:29:33.929Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:29:49.915Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:30:09.919Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:30:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:30:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:30:29.923Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:30:33.803Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:31:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:31:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:31:34.844Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:31:40.907Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:32:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:32:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:32:34.809Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:32:40.911Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:33:00.914Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:33:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:33:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:33:20.919Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:33:34.701Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:33:40.924Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:34:00.925Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:34:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:34:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:34:34.742Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:35:12.052Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:35:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:35:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:35:34.783Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"State changed for **********: OPENING","timestamp":"2025-06-19T05:35:40.755Z"}
{"level":"info","message":"State changed for **********: PAIRING","timestamp":"2025-06-19T05:35:41.489Z"}
{"level":"info","message":"State changed for **********: CONNECTED","timestamp":"2025-06-19T05:35:41.490Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:36:12.056Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:36:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:36:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:36:32.060Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:36:34.764Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:36:52.062Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:37:12.064Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:37:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:37:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:37:32.063Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:37:34.789Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:38:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:38:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:38:34.766Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:38:43.115Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:39:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:39:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:39:34.800Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:39:43.117Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:40:03.117Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:40:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:40:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:40:23.125Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:40:34.972Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:40:43.128Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:41:03.127Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:41:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:41:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:41:35.107Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:42:14.200Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:42:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:42:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:42:34.967Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:43:14.204Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:43:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:43:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:43:34.204Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:43:34.910Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:43:54.207Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:44:14.212Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:44:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:44:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:44:34.217Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:44:34.982Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:45:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:45:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:45:35.226Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:45:45.057Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:46:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:46:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:46:34.923Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:46:45.058Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:47:05.059Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:47:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:47:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:47:25.063Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:47:34.933Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:47:45.067Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:48:05.068Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:48:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:48:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:48:34.990Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:49:16.004Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:49:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:49:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:49:35.285Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:50:16.004Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:50:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:50:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:50:34.969Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:50:36.008Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:50:56.013Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:51:16.013Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:51:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:51:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:51:34.963Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:51:36.017Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:52:15.250Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:52:16.619Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:52:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:52:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:52:34.992Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:52:47.056Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:52:50.006Z"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/chats","timestamp":"2025-06-19T05:52:51.534Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:53:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:53:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:53:34.994Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********","webhook":"https://arihantai.com/whatsapp/webhook/**********","webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T05:53:37.330Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********","webhook":"https://arihantai.com/whatsapp/webhook/**********","webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T05:53:37.334Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********","webhook":"https://arihantai.com/whatsapp/webhook/**********","webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings/webhook","timestamp":"2025-06-19T05:53:37.342Z","userAgent":"python-requests/2.25.1"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:53:38.775Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:53:47.059Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:54:07.059Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:54:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:54:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:54:27.064Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:54:35.533Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:54:47.067Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:55:07.071Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:55:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:55:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:55:35.541Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:56:18.208Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:56:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:56:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:56:35.697Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:57:18.211Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:57:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:57:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:57:35.600Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:57:38.212Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:57:58.212Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:58:12.512Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:58:18.213Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:58:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:58:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:58:37.588Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:58:37.714Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:58:38.214Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:58:39.271Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:59:16.560Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:59:17.981Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:59:19.529Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:59:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T05:59:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:59:21.021Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T05:59:23.983Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T05:59:37.722Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T05:59:49.297Z"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T06:00:00.370Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:00:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:00:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:00:37.626Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:00:49.298Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:01:09.303Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:01:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:01:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:01:29.307Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:01:37.666Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:01:49.311Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:02:09.312Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:02:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:02:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:02:38.018Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T06:03:12.979Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:03:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:03:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:03:20.305Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T06:03:26.161Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:03:37.613Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:04:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:04:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:04:20.310Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /","timestamp":"2025-06-19T06:04:25.274Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /","timestamp":"2025-06-19T06:04:34.338Z","userAgent":"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:04:37.692Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:*************","level":"info","message":"GET /favicon.ico","timestamp":"2025-06-19T06:04:38.935Z","userAgent":"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:04:40.314Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /favicon.ico","timestamp":"2025-06-19T06:04:56.101Z","userAgent":"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:05:00.315Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:05:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:05:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:05:20.315Z"}
{"ip":"::ffff:*************","level":"info","message":"GET /sitemap.xml","timestamp":"2025-06-19T06:05:20.466Z","userAgent":"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:05:37.781Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:05:40.320Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:06:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:06:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:06:37.670Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:06:51.328Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:07:19.986Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:07:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:07:37.598Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:07:51.328Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:08:11.333Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:08:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:08:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:08:31.333Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:08:37.613Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:08:51.338Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:09:11.342Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:09:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:09:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:09:37.770Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:10:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:10:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:10:22.415Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:10:37.849Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:11:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:11:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:11:22.415Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:11:37.688Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:11:42.419Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:12:02.419Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:12:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:12:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:12:22.420Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:12:37.850Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:12:42.424Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:13:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:13:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:13:37.714Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:13:53.445Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:14:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:14:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:14:37.705Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:14:53.450Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:15:13.455Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:15:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:15:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:15:33.455Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:15:37.780Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:15:53.459Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T06:16:11.422Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:16:13.460Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:16:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:16:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:16:37.675Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:17:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:17:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:17:24.282Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:17:37.892Z","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:18:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:18:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:18:24.286Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:18:44.290Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:19:04.295Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:19:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:19:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:19:24.300Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:19:44.300Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:20:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:20:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:20:55.415Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:21:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:21:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:21:55.419Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:22:15.421Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:22:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:22:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:22:35.425Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:22:55.434Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:23:15.434Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:23:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:23:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:24:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:24:20.010Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:24:26.605Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:25:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:25:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:25:26.607Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:25:46.611Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:26:06.617Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:26:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:26:20.012Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:26:26.617Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:26:46.621Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:27:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:27:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:27:57.541Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:28:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:28:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:28:57.542Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:29:17.543Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:29:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:29:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:29:37.547Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:29:57.551Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:30:17.555Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:30:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:30:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:31:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:31:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:31:28.619Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:32:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:32:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:32:28.620Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:32:48.625Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:33:08.629Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:33:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:33:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:33:28.630Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:33:48.634Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:34:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:34:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:35:00.162Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:35:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:35:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:36:00.166Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:36:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:36:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:36:20.170Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:36:40.176Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:37:00.175Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:37:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:37:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:37:20.180Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:38:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:38:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:38:31.059Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:39:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:39:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:39:31.067Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:39:51.066Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:40:11.066Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:40:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:40:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:40:31.071Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:40:51.075Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:41:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:41:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:42:01.951Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:42:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:42:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:43:01.951Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:43:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:43:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:43:21.955Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:43:41.959Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:44:01.960Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:44:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:44:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:44:21.960Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:45:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:45:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:45:32.895Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:46:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:46:20.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:46:32.913Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:46:52.920Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:47:12.924Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:47:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:47:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:47:32.930Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:47:52.933Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:48:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:48:20.011Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:49:04.715Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:49:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:49:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:50:04.718Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:50:20.041Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:50:20.042Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:50:24.723Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:50:44.727Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:51:04.727Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:51:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:51:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:51:24.731Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:52:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:52:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:52:35.817Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:53:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:53:20.022Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:53:35.818Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:53:55.822Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:54:15.822Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:54:19.987Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:54:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:54:35.826Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:54:55.827Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:55:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:55:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:56:06.815Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:56:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:56:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:57:06.818Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:57:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:57:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:57:26.823Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:57:46.827Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:58:06.827Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:58:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:58:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:58:26.828Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T06:59:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T06:59:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T06:59:38.860Z"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T07:00:00.333Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:00:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:00:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:00:38.862Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:00:58.867Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:01:18.870Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:01:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:01:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:01:38.871Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T07:01:57.927Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:01:58.877Z"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T07:02:00.138Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:02:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:02:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:03:10.024Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:03:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:03:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:04:10.025Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:04:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:04:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:04:30.025Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:04:50.029Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:05:10.034Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:05:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:05:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:05:30.038Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:06:19.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:06:19.991Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:06:40.839Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:07:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:07:20.011Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:07:40.845Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:08:00.850Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:08:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:08:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:08:20.854Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:08:40.858Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:09:00.862Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:09:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:09:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:10:11.728Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:10:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:10:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:11:11.732Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:11:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:11:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:11:31.736Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:11:51.737Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:12:11.741Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:12:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:12:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:12:31.745Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:13:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:13:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:13:42.856Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:14:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:14:20.010Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:14:42.861Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:15:02.861Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:15:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:15:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:15:22.862Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:15:42.863Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:16:02.867Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:16:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:16:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:17:13.894Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:17:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:17:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:18:13.898Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:18:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:18:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:18:33.902Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:18:53.902Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:19:13.909Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:19:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:19:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:19:33.912Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:20:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:20:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:20:44.737Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:21:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:21:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:21:44.741Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:22:04.746Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:22:19.989Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:22:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:22:24.750Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:22:44.754Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:23:04.758Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:23:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:23:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:24:15.923Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:24:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:24:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:25:15.928Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:25:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:25:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:25:35.932Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:25:55.933Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:26:15.937Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:26:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:26:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:26:35.938Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:27:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:27:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:27:47.481Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:28:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:28:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:28:47.481Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:29:07.481Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:29:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:29:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:29:27.485Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:29:47.489Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:30:07.490Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:30:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:30:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:31:18.549Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:31:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:31:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:32:18.551Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:32:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:32:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:32:38.556Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:32:58.560Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:33:18.564Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:33:19.993Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:33:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:33:38.568Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:34:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:34:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:34:49.503Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:35:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:35:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:35:49.508Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:36:09.512Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:36:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:36:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:36:29.516Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:36:49.523Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:37:09.528Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:37:19.995Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:37:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:38:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:38:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:38:20.383Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:39:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:39:20.014Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:39:20.388Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:39:40.393Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:40:00.397Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:40:19.977Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:40:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:40:20.401Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:40:40.405Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:41:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:41:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:41:51.469Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:42:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:42:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:42:51.473Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:43:11.478Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:43:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:43:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:43:31.482Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:43:51.486Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:44:11.490Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:44:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:44:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:45:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:45:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:45:22.429Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:46:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:46:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:46:22.430Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:46:42.438Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:47:02.437Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:47:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:47:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:47:22.441Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:47:42.441Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:48:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:48:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:48:53.596Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:49:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:49:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:49:53.600Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:50:13.600Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:50:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:50:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:50:33.605Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:50:53.606Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:51:13.611Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:51:19.990Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:51:19.994Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /","timestamp":"2025-06-19T07:51:25.006Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /favicon.ico","timestamp":"2025-06-19T07:51:25.492Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4 240.111 Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /robots.txt","timestamp":"2025-06-19T07:51:25.974Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4 240.111 Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /.well-known/security.txt","timestamp":"2025-06-19T07:51:26.419Z","userAgent":"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4 240.111 Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:52:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:52:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:52:24.513Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:53:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:53:20.010Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:53:24.513Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:53:44.514Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:54:04.518Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:54:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:54:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:54:24.522Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:54:44.526Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:55:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:55:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:55:55.466Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:56:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:56:20.011Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:56:55.472Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:57:15.473Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:57:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:57:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:57:35.477Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:57:55.481Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:58:15.482Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:58:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:58:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T07:59:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T07:59:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T07:59:26.387Z"}
{"level":"info","message":"Session stats - Active: 2/5, QR Codes: 1","timestamp":"2025-06-19T08:00:00.375Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:00:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:00:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:00:26.388Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:00:46.392Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:01:06.396Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:01:19.999Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:01:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:01:26.400Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:01:46.405Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:02:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:02:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:02:57.236Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:03:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:03:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:03:57.236Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:04:17.236Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:04:20.000Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:04:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:04:37.240Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:04:57.245Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:05:17.249Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:05:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:05:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:06:20.006Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:06:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:06:28.807Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:07:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:07:20.013Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:07:28.812Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:07:48.816Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:08:08.820Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:08:20.004Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:08:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:08:28.825Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:08:48.829Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:09:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:09:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:09:59.714Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:10:19.992Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:10:19.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:10:59.714Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:11:19.722Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:11:19.996Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:11:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:11:39.720Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:11:59.721Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:12:19.725Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:12:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:12:20.011Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:13:20.005Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:13:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:13:30.742Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:14:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:14:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:14:30.742Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:14:50.747Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:15:10.747Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:15:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:15:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:15:30.752Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:15:50.757Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:16:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:16:20.001Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:17:01.616Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:17:19.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:17:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:18:01.616Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:18:20.002Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:18:20.003Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:18:21.620Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:18:41.624Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:19:01.628Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:19:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:19:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:19:21.633Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:20:20.013Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:20:20.014Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:20:32.640Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:21:20.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:21:20.030Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:21:32.642Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:21:52.646Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:22:12.650Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:22:20.020Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:22:20.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:22:32.655Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:22:52.659Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:23:20.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:23:20.024Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:24:03.529Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:24:20.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:24:20.022Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:25:03.529Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:25:20.023Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:25:20.025Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:25:23.533Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:25:43.534Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:26:03.535Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:26:20.013Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:26:20.015Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:26:23.535Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:27:20.015Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:27:20.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:27:34.553Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:28:20.020Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:28:20.022Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:28:34.555Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:28:54.559Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:29:14.564Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:29:20.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:29:20.020Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:29:34.568Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:29:54.569Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:30:20.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:30:20.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:31:05.576Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:31:20.020Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:31:20.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:32:05.578Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:32:20.022Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:32:20.023Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:32:25.582Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:32:45.582Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:33:05.586Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:33:20.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:33:20.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:33:25.587Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:34:20.008Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:34:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:34:36.643Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:35:20.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:35:20.024Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:35:36.648Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:35:56.649Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:36:16.654Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:36:20.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:36:20.026Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:36:36.658Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:36:56.667Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:37:20.010Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:37:20.012Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:38:07.914Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:38:20.040Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:38:20.065Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Message sent from session ********** to status@broadcast","timestamp":"2025-06-19T08:39:05.513Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:39:07.921Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:39:20.023Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:39:20.035Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:39:27.926Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:39:47.929Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:40:04.168Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:40:04.172Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:40:05.997Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:40:05.998Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:40:07.934Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:40:16.182Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:40:16.185Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:40:26.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:40:26.022Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:40:27.941Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:40:36.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:40:36.028Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:40:47.023Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:40:47.028Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:40:55.988Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:40:56.013Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:41:06.022Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:41:06.025Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:41:20.024Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:41:20.028Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:41:40.038Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:42:20.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:42:20.025Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:42:40.044Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:43:00.059Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:43:20.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:43:20.028Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:43:20.061Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:43:40.065Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:44:00.074Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:44:20.020Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:44:20.024Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:45:10.970Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:45:20.017Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:45:20.042Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:46:10.971Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:46:19.985Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:46:20.007Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:46:30.978Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:46:50.977Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:47:10.979Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:47:20.016Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:47:20.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:47:30.984Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:48:20.021Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:48:20.025Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:48:41.899Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:49:20.022Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:49:20.034Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:49:41.909Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:50:01.914Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:50:20.034Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:50:20.059Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:50:21.917Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:50:41.922Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:51:01.928Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:51:20.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:51:20.026Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:52:12.992Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:52:20.027Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:52:20.030Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/chats","timestamp":"2025-06-19T08:52:32.940Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/getAllChats","timestamp":"2025-06-19T08:52:32.960Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /chats/**********","timestamp":"2025-06-19T08:52:32.963Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /getAllChats","timestamp":"2025-06-19T08:52:32.966Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/messages","timestamp":"2025-06-19T08:52:32.969Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:52:51.782Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:52:51.787Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:52:51.791Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:52:51.795Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:52:51.798Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:52:51.802Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:52:51.806Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:52:51.809Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:52:51.815Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:52:51.818Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:52:51.822Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:52:51.825Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:52:51.829Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:52:51.832Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:52:51.835Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:52:51.838Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:52:51.841Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:52:51.843Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:52:51.848Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:52:51.850Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:52:51.853Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:52:51.856Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:52:51.860Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:52:51.864Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:52:51.868Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:52:51.873Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:52:51.876Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:52:51.879Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:52:51.882Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:52:51.886Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/chats","timestamp":"2025-06-19T08:52:57.401Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/messages","timestamp":"2025-06-19T08:52:57.405Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/webhook","timestamp":"2025-06-19T08:52:57.413Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/status","timestamp":"2025-06-19T08:52:57.416Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /instances/**********/qr","timestamp":"2025-06-19T08:52:57.426Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /getAllChats","timestamp":"2025-06-19T08:52:57.429Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /getAllMessages","timestamp":"2025-06-19T08:52:57.431Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /setWebhook","timestamp":"2025-06-19T08:52:57.434Z","userAgent":"python-requests/2.25.1"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /webhook","timestamp":"2025-06-19T08:52:57.436Z","userAgent":"python-requests/2.25.1"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:53:12.998Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:53:20.012Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:53:20.015Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:53:24.913Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:53:24.920Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:53:24.923Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:53:24.926Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/webhook","timestamp":"2025-06-19T08:53:24.929Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:53:24.932Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:53:24.935Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:53:24.938Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:53:24.941Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook/**********","timestamp":"2025-06-19T08:53:24.945Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:53:24.948Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:53:24.950Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:53:24.953Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:53:24.956Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/setWebhook","timestamp":"2025-06-19T08:53:24.958Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:53:24.961Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:53:24.964Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:53:24.968Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:53:24.970Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /setWebhook","timestamp":"2025-06-19T08:53:24.973Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:53:24.976Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:53:24.983Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:53:24.987Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:53:24.992Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /instances/**********/settings","timestamp":"2025-06-19T08:53:24.996Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:53:24.999Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"webhook_url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:53:25.003Z","userAgent":"python-requests/2.25.1"}
{"body":{"events":["message","status","qr"],"url":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:53:25.007Z","userAgent":"python-requests/2.25.1"}
{"body":{"session":"**********","webhookUrl":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:53:25.012Z","userAgent":"python-requests/2.25.1"}
{"body":{"webhook":"https://arihantai.com/whatsapp/webhook/**********"},"ip":"::ffff:127.0.0.1","level":"info","message":"POST /webhook","timestamp":"2025-06-19T08:53:25.020Z","userAgent":"python-requests/2.25.1"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:53:33.001Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:53:53.005Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:54:13.013Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:54:20.019Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:54:20.020Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:54:33.020Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:55:20.009Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:55:20.015Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:55:44.179Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:56:20.033Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:56:20.046Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:************","level":"info","message":"GET /","timestamp":"2025-06-19T08:56:29.835Z","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:56:44.200Z"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:57:04.196Z"}
{"ip":"::ffff:**************","level":"info","message":"GET /health","timestamp":"2025-06-19T08:57:20.014Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:**************","level":"info","message":"GET /instances","timestamp":"2025-06-19T08:57:20.018Z","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"QR Code generated for session: test_new","timestamp":"2025-06-19T08:57:24.203Z"}
