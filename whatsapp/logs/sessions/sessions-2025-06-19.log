{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":945199,"external":4295044,"heapTotal":59887616,"heapUsed":53652416,"rss":133144576},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T00:00:00.128Z","totalSessions":2,"uptime":40902.934199065}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:01:00.227Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:02:00.196Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:02:20.193Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:02:40.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:03:00.204Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:03:20.205Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:04:31.863Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:05:31.827Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:05:51.830Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:06:11.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:06:31.850Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:06:51.844Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:08:02.744Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:09:02.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:09:22.717Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:09:42.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:10:02.731Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:10:22.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:11:33.671Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:12:33.631Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:12:53.636Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:13:13.639Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:13:33.643Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:13:53.649Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:15:05.188Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:16:05.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:16:25.146Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:16:45.153Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:17:05.151Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:17:25.154Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:18:36.451Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:19:36.427Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:19:56.431Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:20:16.431Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:20:36.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:20:56.445Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:22:07.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:23:07.367Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:23:27.375Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:23:47.378Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:24:07.377Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:24:27.378Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:25:38.390Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:26:38.354Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:26:58.357Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:27:18.354Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:27:38.358Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:27:58.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:29:09.992Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:30:09.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:30:29.978Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:30:49.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:31:09.982Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:31:29.985Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:32:40.838Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:33:40.803Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:34:00.803Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:34:20.805Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:34:40.804Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:35:00.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:36:11.991Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:37:11.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:37:31.975Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:37:51.975Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:38:11.986Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:38:31.989Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:39:43.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:40:43.043Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:41:03.055Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:41:23.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:41:43.066Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:42:03.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:43:13.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:44:13.929Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:44:33.934Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:44:53.934Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:45:13.951Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:45:33.941Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:46:45.118Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:47:45.064Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:48:05.068Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:48:25.070Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:48:45.071Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:49:05.082Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:50:15.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:51:15.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:51:35.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:51:55.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:52:15.933Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:52:35.936Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:53:46.998Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:54:46.945Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:55:06.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:55:26.956Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:55:46.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:56:06.967Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:57:17.883Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:58:17.820Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:58:37.818Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:58:57.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:59:17.821Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T00:59:37.824Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1748849,"external":5098694,"heapTotal":61198336,"heapUsed":54219880,"rss":134098944},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T01:00:00.952Z","totalSessions":2,"uptime":44503.757780774}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:00:49.085Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:01:49.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:02:09.054Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:02:29.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:02:49.060Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:03:09.067Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:04:20.253Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:05:20.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:05:40.238Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:06:00.245Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:06:20.244Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:06:40.252Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:07:51.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:08:51.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:09:11.434Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:09:31.437Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:09:51.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:10:11.438Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:11:22.561Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:12:22.513Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:12:42.522Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:13:02.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:13:22.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:13:42.522Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:14:53.695Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:15:53.621Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:16:13.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:16:33.623Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:16:53.623Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:17:13.629Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:18:24.739Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:19:24.712Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:19:44.709Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:20:04.716Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:20:24.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:20:44.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:21:55.801Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:22:55.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:23:15.730Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:23:35.731Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:23:55.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:24:15.741Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:25:26.638Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:26:26.617Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:26:46.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:27:06.610Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:27:26.610Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:27:46.610Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:28:57.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:29:57.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:30:17.683Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:30:37.686Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:30:57.691Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:31:17.688Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:32:28.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:33:28.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:33:48.616Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:34:08.623Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:34:28.628Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:34:48.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:35:59.640Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:36:59.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:37:19.597Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:37:39.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:37:59.605Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:38:19.610Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:39:30.785Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:40:30.753Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:40:50.765Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:41:10.756Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:41:30.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:41:50.769Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:43:01.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:44:01.795Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:44:21.798Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:44:41.797Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:45:01.813Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:45:21.804Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:46:33.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:47:33.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:47:53.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:48:13.013Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:48:33.013Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:48:53.021Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:50:03.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:51:03.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:51:23.920Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:51:43.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:52:03.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:52:23.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:53:34.803Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:54:34.781Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:54:54.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:55:14.778Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:55:34.783Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:55:54.782Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:57:05.852Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:58:05.796Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:58:25.800Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:58:45.812Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:59:05.810Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T01:59:25.818Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1746385,"external":5096230,"heapTotal":61198336,"heapUsed":54065232,"rss":134103040},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T02:00:00.792Z","totalSessions":2,"uptime":48103.597562166}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:00:36.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:01:36.906Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:01:56.911Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:02:16.914Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:02:36.918Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:02:56.921Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:04:07.986Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:05:08.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:05:27.969Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:05:47.964Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:06:07.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:06:27.976Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:07:38.984Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:08:38.968Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:08:58.964Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:09:18.969Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:09:38.970Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:09:59.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:11:10.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:12:10.077Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:12:30.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:12:50.089Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:13:10.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:13:30.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:14:40.934Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:15:40.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:16:00.928Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:16:20.934Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:16:40.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:17:00.992Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:18:11.818Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:19:11.814Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:19:31.824Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:19:51.820Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:20:11.824Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:20:31.858Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:21:42.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:22:42.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:23:02.957Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:23:22.956Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:23:42.966Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:24:02.996Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:25:14.253Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:26:14.239Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:26:34.244Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:26:54.270Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:27:14.252Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:27:34.265Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:28:45.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:29:45.215Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:30:05.217Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:30:25.221Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:30:45.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:31:05.265Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:32:16.787Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:33:16.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:33:36.783Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:33:56.791Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:34:16.793Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:34:36.822Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:35:47.884Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:36:47.882Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:37:07.888Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:37:27.895Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:37:47.895Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:38:07.926Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:39:18.774Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:40:18.812Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:40:38.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:40:58.795Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:41:18.833Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:41:38.794Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:42:49.686Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:43:49.688Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:44:09.691Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:44:29.694Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:44:49.731Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:45:09.701Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:46:20.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:47:20.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:47:40.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:48:00.730Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:48:20.755Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:48:40.723Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:49:51.866Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:50:51.869Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:51:11.870Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:51:31.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:51:51.913Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:52:11.882Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:53:22.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:54:22.714Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:54:42.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:55:02.749Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:55:22.758Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:55:42.735Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:56:53.907Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:57:53.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:58:13.857Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:58:33.866Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:58:53.922Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T02:59:13.886Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":2095838,"external":5445683,"heapTotal":56217600,"heapUsed":48836176,"rss":129396736},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T03:00:00.642Z","totalSessions":2,"uptime":51703.448098667}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:00:24.985Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:01:24.989Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:01:44.991Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:02:05.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:02:25.031Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:02:45.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:03:55.976Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:04:55.976Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:05:15.997Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:05:36.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:05:55.998Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:06:15.993Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:07:27.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:08:27.179Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:08:47.189Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:09:07.229Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:09:27.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:09:47.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:10:58.221Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:11:58.217Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:12:18.241Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:12:38.250Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:12:58.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:13:18.234Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:14:29.701Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:15:29.711Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:15:49.749Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:16:09.721Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:16:29.707Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:16:49.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:18:01.031Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:19:01.043Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:19:21.055Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:19:41.041Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:20:01.045Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:20:21.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:21:31.921Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:22:31.923Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:22:51.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:23:11.932Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:23:31.932Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:23:51.937Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:25:03.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:26:03.229Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:26:23.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:26:43.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:27:03.239Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:27:23.239Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:28:34.115Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:29:34.117Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:29:54.139Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:30:14.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:30:34.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:30:54.131Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:32:05.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:33:05.081Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:33:25.104Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:33:45.073Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:34:05.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:34:25.079Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:35:36.225Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:36:36.222Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:36:56.245Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:37:16.219Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:37:36.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:37:56.225Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:39:07.500Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:40:07.476Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:40:27.454Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:40:47.473Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:41:07.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:41:27.463Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:42:38.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:43:38.412Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:43:58.462Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:44:18.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:44:38.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:44:58.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:46:09.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:47:09.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:47:29.601Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:47:49.636Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:48:09.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:48:29.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:49:40.827Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:50:40.829Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:51:00.833Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:51:20.887Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:51:40.837Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:52:00.844Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:53:11.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:54:11.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:54:31.948Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:54:51.998Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:55:11.950Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:55:31.950Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:56:43.033Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:57:43.031Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:58:03.046Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:58:23.077Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:58:43.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T03:59:03.046Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1356115,"external":4705960,"heapTotal":57266176,"heapUsed":50330296,"rss":144334848},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T04:00:00.515Z","totalSessions":2,"uptime":55303.320537811}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:00:13.913Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:01:13.914Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:01:33.930Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:01:53.954Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:02:13.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:02:33.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:03:44.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:04:44.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:05:04.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:05:25.017Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:05:44.987Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:06:04.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:07:15.886Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:08:15.888Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:08:35.931Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:08:55.895Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:09:15.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:09:35.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:10:46.921Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:11:46.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:12:06.886Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:12:26.888Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:12:46.910Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:13:06.890Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:14:17.764Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:15:17.770Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:15:37.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:15:57.767Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:16:17.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:16:37.790Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:17:48.859Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:18:48.862Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:19:08.866Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:19:28.866Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:19:48.903Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:20:08.868Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:21:20.035Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:22:20.032Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:22:40.032Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:23:00.037Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:23:20.073Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:23:40.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:24:50.882Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:25:50.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:26:10.884Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:26:30.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:26:50.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:27:10.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:28:22.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:29:22.214Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:29:42.191Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:30:02.173Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:30:22.189Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:30:42.185Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:31:53.288Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:32:53.245Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:33:13.258Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:33:33.249Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:33:53.250Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:34:13.256Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:35:24.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:36:24.402Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:36:44.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:37:04.402Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:37:24.401Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:37:44.432Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:38:55.543Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:39:55.567Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:40:15.556Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:40:35.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:40:55.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:41:15.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:42:26.518Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:43:26.514Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:43:46.513Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:44:06.524Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:44:26.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:44:46.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:45:57.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:46:57.447Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:47:17.443Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:47:37.451Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:47:57.452Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:48:17.505Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:49:28.719Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:50:28.735Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:50:48.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:51:08.736Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:51:28.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:51:48.778Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:52:59.617Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:53:59.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:54:19.637Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:54:39.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:54:59.635Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:55:19.664Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:56:30.769Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:57:30.770Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:57:50.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:58:10.776Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:58:30.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T04:58:50.826Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":940393,"external":4290238,"heapTotal":58576896,"heapUsed":47256448,"rss":193671168},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T05:00:00.374Z","totalSessions":2,"uptime":58903.179636832}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:00:01.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:01:01.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:01:21.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:01:41.592Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:02:01.613Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:02:21.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:03:32.726Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:04:32.716Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:04:52.714Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:05:12.719Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:05:32.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:05:52.759Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:07:03.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:08:03.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:08:23.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:08:43.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:09:03.622Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:09:23.637Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:10:34.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:11:34.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:11:54.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:12:14.552Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:12:34.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:12:54.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:14:05.683Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:15:05.678Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:15:25.681Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:15:45.682Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:16:05.685Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:16:25.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:17:36.970Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:18:36.965Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:18:56.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:19:16.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:19:36.997Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:19:56.984Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:21:08.159Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:22:08.161Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:22:28.164Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:22:48.171Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:23:08.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:23:28.176Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:24:39.033Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:25:39.062Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:25:59.039Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:26:19.036Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:26:39.046Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:26:59.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:28:10.061Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:29:09.941Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:29:29.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:29:49.940Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:30:09.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:30:29.953Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:31:40.937Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:32:40.939Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:33:00.945Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:33:20.944Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:33:40.992Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:34:00.967Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:35:12.117Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:36:12.089Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:36:32.085Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:36:52.117Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:37:12.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:37:32.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:38:43.143Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:39:43.143Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:40:03.143Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:40:23.158Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:40:43.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:41:03.151Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:42:14.265Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:43:14.240Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:43:34.236Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:43:54.243Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:44:14.242Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:44:34.301Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:45:45.095Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:46:45.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:47:05.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:47:25.088Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:47:45.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:48:05.130Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:49:16.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:50:16.028Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:50:36.033Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:50:56.040Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:51:16.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:51:36.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:52:47.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:53:47.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:54:07.085Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:54:27.088Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:54:47.134Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:55:07.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:56:18.265Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:57:18.248Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:57:38.238Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:57:58.257Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:58:18.274Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:58:38.259Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T05:59:49.330Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1862019,"external":5211864,"heapTotal":56741888,"heapUsed":51847184,"rss":192724992},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T06:00:00.369Z","totalSessions":2,"uptime":62503.174463259}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:00:49.323Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:01:09.327Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:01:29.350Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:01:49.334Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:02:09.336Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:03:20.333Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:04:20.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:04:40.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:05:00.342Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:05:20.380Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:05:40.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:06:51.428Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:07:51.354Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:08:11.359Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:08:31.357Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:08:51.361Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:09:11.366Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:10:22.473Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:11:22.444Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:11:42.443Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:12:02.446Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:12:22.444Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:12:42.448Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:13:53.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:14:53.475Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:15:13.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:15:33.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:15:53.482Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:16:13.486Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:17:24.310Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:18:24.311Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:18:44.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:19:04.353Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:19:24.325Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:19:44.327Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:20:55.455Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:21:55.456Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:22:15.488Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:22:35.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:22:55.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:23:15.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:24:26.635Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:25:26.634Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:25:46.640Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:26:06.646Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:26:26.668Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:26:46.647Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:27:57.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:28:57.567Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:29:17.567Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:29:37.620Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:29:57.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:30:17.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:31:28.644Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:32:28.650Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:32:48.654Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:33:08.680Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:33:28.655Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:33:48.658Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:35:00.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:36:00.196Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:36:20.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:36:40.237Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:37:00.210Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:37:20.205Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:38:31.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:39:31.148Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:39:51.106Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:40:11.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:40:31.100Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:40:51.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:42:02.024Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:43:02.010Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:43:21.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:43:41.985Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:44:01.989Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:44:21.992Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:45:32.942Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:46:33.077Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:46:52.984Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:47:12.984Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:47:33.014Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:47:53.032Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:49:04.785Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:50:04.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:50:24.749Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:50:44.753Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:51:04.754Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:51:24.758Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:52:35.890Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:53:35.852Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:53:55.851Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:54:15.854Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:54:35.850Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:54:55.851Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:56:06.890Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:57:06.843Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:57:26.850Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:57:46.870Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:58:06.853Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:58:26.855Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T06:59:38.938Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":932575,"external":4282420,"heapTotal":55955456,"heapUsed":47228848,"rss":191967232},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T07:00:00.332Z","totalSessions":2,"uptime":66103.138071584}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:00:38.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:00:58.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:01:18.894Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:01:38.894Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:01:58.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:03:10.055Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:04:10.049Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:04:30.048Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:04:50.054Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:05:10.068Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:05:30.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:06:40.867Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:07:40.878Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:08:00.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:08:20.880Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:08:40.882Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:09:00.938Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:10:11.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:11:11.757Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:11:31.760Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:11:51.760Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:12:11.767Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:12:31.796Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:13:42.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:14:42.890Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:15:02.885Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:15:22.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:15:42.887Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:16:02.917Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:17:13.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:18:13.926Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:18:33.926Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:18:53.928Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:19:13.940Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:19:33.978Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:20:44.766Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:21:44.765Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:22:04.769Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:22:24.775Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:22:44.778Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:23:04.813Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:24:15.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:25:15.954Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:25:35.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:25:55.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:26:15.960Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:26:36.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:27:47.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:28:47.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:29:07.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:29:27.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:29:47.514Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:30:07.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:31:18.623Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:32:18.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:32:38.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:32:58.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:33:18.590Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:33:38.634Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:34:49.561Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:35:49.536Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:36:09.536Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:36:29.541Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:36:49.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:37:09.615Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:38:20.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:39:20.414Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:39:40.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:40:00.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:40:20.425Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:40:40.466Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:41:51.500Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:42:51.500Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:43:11.505Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:43:31.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:43:51.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:44:11.551Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:45:22.458Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:46:22.458Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:46:42.469Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:47:02.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:47:22.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:47:42.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:48:53.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:49:53.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:50:13.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:50:33.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:50:53.631Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:51:13.678Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:52:24.542Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:53:24.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:53:44.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:54:04.543Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:54:24.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:54:44.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:55:55.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:56:55.498Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:57:15.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:57:35.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:57:55.505Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:58:15.545Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T07:59:26.416Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1719308,"external":5069153,"heapTotal":61460480,"heapUsed":49064232,"rss":196452352},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T08:00:00.374Z","totalSessions":2,"uptime":69703.1793978}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:00:26.412Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:00:46.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:01:06.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:01:26.423Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:01:46.475Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:02:57.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:03:57.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:04:17.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:04:37.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:04:57.270Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:05:17.308Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:06:28.835Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:07:28.848Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:07:48.841Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:08:08.845Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:08:28.849Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:08:48.879Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:09:59.743Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:10:59.739Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:11:19.753Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:11:39.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:11:59.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:12:19.782Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:13:30.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:14:30.769Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:14:50.776Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:15:10.772Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:15:30.776Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:15:50.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:17:01.649Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:18:01.641Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:18:21.644Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:18:41.648Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:19:01.652Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:19:21.670Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:20:32.666Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:21:32.668Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:21:52.671Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:22:12.676Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:22:32.680Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:22:52.714Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:24:03.573Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:25:03.553Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:25:23.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:25:43.561Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:26:03.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:26:23.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:27:34.583Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:28:34.580Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:28:54.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:29:14.595Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:29:34.593Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:29:54.636Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:31:05.604Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:32:05.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:32:25.608Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:32:45.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:33:05.613Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:33:25.636Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:34:36.674Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:35:36.674Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:35:56.672Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:36:16.678Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:36:36.682Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:36:56.747Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:38:08.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:39:07.961Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:39:28.024Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:39:48.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:40:07.965Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:40:28.003Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:41:40.131Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:42:40.096Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:43:00.160Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:43:20.113Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:43:40.127Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:44:00.114Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:45:11.043Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:46:11.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:46:31.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:46:51.019Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:47:11.011Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:47:31.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:48:41.966Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:49:41.957Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:50:01.965Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:50:21.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:50:41.946Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:51:01.987Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:52:13.155Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:53:13.070Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:53:33.049Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:53:53.037Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:54:13.045Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:54:33.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:55:44.225Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:56:44.315Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:57:04.264Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:57:24.230Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:57:44.265Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:58:04.307Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T08:59:15.349Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1374054,"external":4723899,"heapTotal":58052608,"heapUsed":49411592,"rss":130990080},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T09:00:00.544Z","totalSessions":2,"uptime":73303.350205873}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:00:15.364Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:00:35.393Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:00:55.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:01:15.336Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:01:35.380Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:02:46.794Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:03:46.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:04:06.701Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:04:26.704Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:04:46.707Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:05:06.740Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:06:17.667Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:07:17.726Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:07:37.678Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:07:57.681Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:08:17.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:08:37.705Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:09:48.688Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:10:48.691Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:11:08.704Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:11:28.694Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:11:48.754Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:12:08.700Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:13:19.907Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:14:19.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:14:39.908Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:14:59.905Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:15:19.970Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:15:39.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:16:51.088Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:17:51.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:18:11.043Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:18:31.081Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:18:51.075Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:19:11.057Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:20:21.964Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:21:21.962Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:21:41.966Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:22:01.976Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:22:21.978Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:22:41.977Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:23:53.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:24:53.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:25:13.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:25:33.115Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:25:53.064Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:26:13.075Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:27:24.149Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:28:24.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:28:44.182Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:29:04.150Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:29:24.157Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:29:44.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:30:55.042Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:31:55.042Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:32:15.046Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:32:35.035Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:32:55.057Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:33:15.113Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:34:27.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:35:27.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:35:47.209Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:36:07.190Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:36:27.199Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:36:47.190Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:37:59.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:38:59.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:39:19.073Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:39:39.052Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:39:59.104Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:40:19.062Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:41:29.990Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:42:30.029Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:42:50.032Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:43:10.003Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:43:30.003Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:43:50.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:45:00.997Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:46:01.022Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:46:21.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:46:41.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:47:01.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:47:21.034Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:48:32.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:49:32.137Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:49:52.303Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:50:12.136Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:50:32.243Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:50:52.176Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:52:03.410Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:53:03.462Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:53:23.442Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:53:43.418Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:54:03.418Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:54:23.423Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:55:34.422Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:56:34.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:56:54.471Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:57:14.427Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:57:34.432Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:57:54.439Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T09:59:05.454Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":955342,"external":4305187,"heapTotal":60936192,"heapUsed":53702952,"rss":134045696},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-19T10:00:00.057Z","totalSessions":2,"uptime":76902.863102044}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:00:05.468Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:00:25.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:00:45.469Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:01:05.473Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:01:25.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:02:36.592Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:03:36.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:03:56.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:04:16.505Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:04:36.510Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:04:56.621Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:06:07.569Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:07:07.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:07:27.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:07:47.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:08:07.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:08:27.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:09:38.537Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:10:38.473Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:10:58.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:11:18.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:11:38.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:11:58.481Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:13:09.903Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:14:09.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:14:29.910Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:14:49.879Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:15:09.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:15:29.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:16:41.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:17:41.139Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:18:01.153Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:18:21.106Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:18:41.120Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:19:01.144Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:20:12.191Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:21:12.192Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:21:32.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:21:52.204Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:22:12.249Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:22:32.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:23:43.388Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:24:43.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:25:03.406Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:25:23.376Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:25:43.375Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:26:03.386Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:27:14.535Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:28:14.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:28:34.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:28:54.530Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:29:14.539Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:29:34.514Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:30:45.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:31:46.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:32:05.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:32:25.906Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:32:45.918Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:33:05.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:34:17.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:35:17.125Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:35:37.110Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:35:57.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:36:17.157Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-19T10:36:37.126Z"}
