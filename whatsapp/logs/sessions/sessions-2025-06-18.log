{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":536936,"external":3866032,"heapTotal":46522368,"heapUsed":41088880,"rss":105926656},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T00:00:00.512Z","totalSessions":2,"uptime":10734.956917244}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":586088,"external":3915184,"heapTotal":46522368,"heapUsed":44289400,"rss":106004480},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T01:00:00.819Z","totalSessions":2,"uptime":14335.263366755}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":528744,"external":3857840,"heapTotal":47570944,"heapUsed":39840704,"rss":107118592},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T02:00:00.996Z","totalSessions":2,"uptime":17935.440902075}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":569704,"external":3898800,"heapTotal":47570944,"heapUsed":42894160,"rss":107298816},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T03:00:00.282Z","totalSessions":2,"uptime":21534.726125212}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":504168,"external":3833264,"heapTotal":47570944,"heapUsed":37498120,"rss":107266048},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T04:00:00.461Z","totalSessions":2,"uptime":25134.90539656}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":520552,"external":3849648,"heapTotal":47833088,"heapUsed":38459608,"rss":108453888},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T05:00:00.711Z","totalSessions":2,"uptime":28735.155368487}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"8487921219","timestamp":"2025-06-18T05:14:13.512Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test123","timestamp":"2025-06-18T05:17:26.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:23:38.781Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:23:39.052Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:23:39.166Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:24:38.425Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:24:38.774Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:24:38.780Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:24:58.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:24:58.530Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:24:58.646Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:25:18.381Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:25:18.597Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:25:18.760Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:25:38.381Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:25:38.543Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:25:38.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:25:58.396Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:25:58.548Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:25:58.693Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:26:49.393Z"}
{"level":"info","message":"Instance created","phoneNumber":"919999999995","sessionId":"test_fixed","timestamp":"2025-06-18T05:26:49.408Z","webhookUrl":null}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:27:09.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:27:09.729Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:27:10.519Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:27:49.229Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:28:09.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:28:09.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:28:09.701Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:28:10.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:28:29.200Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:28:29.558Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:28:29.699Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:28:30.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:28:49.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:28:49.563Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:28:49.725Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:28:50.471Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:29:09.211Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:29:09.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:29:09.761Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:29:10.501Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"8487921219","timestamp":"2025-06-18T05:29:11.521Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test_fixed","timestamp":"2025-06-18T05:29:21.440Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test_fixed","timestamp":"2025-06-18T05:29:29.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:29:29.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:29:29.715Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test_reconnect","timestamp":"2025-06-18T05:29:39.049Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:30:04.689Z"}
{"level":"info","message":"Instance created","phoneNumber":"918487921219","sessionId":"8487921219","timestamp":"2025-06-18T05:30:04.708Z","webhookUrl":null}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:30:40.871Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:31:04.588Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:31:24.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:31:40.863Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:31:44.563Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:32:00.898Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:32:04.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:32:20.919Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:32:24.551Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:32:40.874Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:33:00.870Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:33:35.745Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:34:12.027Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:34:35.731Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:34:55.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:35:12.012Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:35:15.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:35:32.002Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:35:35.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:35:52.035Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:35:55.733Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:36:12.002Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:36:32.031Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:37:06.911Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:37:43.093Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:38:06.813Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:38:26.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:38:43.087Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:38:46.822Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:39:03.103Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:39:06.859Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:39:23.089Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:39:26.833Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:39:43.069Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:40:03.086Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:40:38.488Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:41:14.227Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:41:38.489Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:41:58.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:42:14.230Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:42:18.495Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:42:34.232Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:42:38.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:42:54.283Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:42:58.470Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:43:14.240Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:43:34.308Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:44:09.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:44:45.505Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:45:09.426Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:45:29.438Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:45:45.692Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:45:49.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:46:05.533Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:46:09.427Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:46:25.518Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:46:29.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:46:45.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:47:05.565Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:47:40.571Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:48:16.772Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:48:40.574Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:49:00.608Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:49:16.761Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:49:20.577Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:49:36.771Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:49:40.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:49:56.773Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:50:00.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:50:16.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:50:36.772Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:51:11.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:51:47.799Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:52:11.523Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:52:31.532Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:52:47.818Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:52:51.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:53:07.817Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:53:11.519Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:53:27.853Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:53:31.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:53:47.807Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:54:07.824Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:54:42.752Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:55:18.863Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:55:42.748Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:56:02.756Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:56:18.841Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:56:22.755Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:56:38.873Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:56:42.794Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:56:58.871Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:57:02.778Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:57:18.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:57:38.867Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:58:14.069Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:58:50.274Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:59:14.082Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:59:34.060Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:59:50.222Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:59:54.057Z"}
{"activeQRCodes":2,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":948667,"external":4298472,"heapTotal":51605504,"heapUsed":45442888,"rss":116310016},"message":"Session statistics","sessionStatuses":{"8487921219":"qr_ready","test_new":"qr_ready"},"timestamp":"2025-06-18T06:00:00.469Z","totalSessions":2,"uptime":2203.694963957}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:00:10.198Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:00:14.077Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:00:30.262Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:00:34.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:00:50.209Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:01:10.220Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:01:45.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:02:21.364Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:02:45.230Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:03:05.221Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:03:21.320Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:03:25.255Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:03:41.330Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:03:45.349Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:04:01.359Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:04:05.258Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:04:21.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:04:41.366Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:05:16.387Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:05:52.627Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:06:16.361Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:06:36.370Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:06:52.617Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:06:56.374Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:07:12.624Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:07:16.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:07:32.628Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:07:36.369Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:07:52.636Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:08:08.695Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:08:12.688Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:08:15.462Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:09:15.304Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:09:23.798Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:09:35.349Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:09:55.377Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:10:15.359Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:10:23.774Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:10:35.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:10:43.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:11:03.790Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:11:23.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:11:43.788Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:11:46.585Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:12:46.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:12:55.361Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:13:06.535Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:13:26.537Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:13:46.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:13:55.331Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:14:06.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:14:15.437Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:14:35.331Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:14:55.357Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:15:15.384Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:15:17.805Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:16:17.869Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:16:26.335Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:16:37.808Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:16:57.846Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:17:17.888Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:17:26.367Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:17:37.826Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:17:46.329Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:17:53.579Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T06:17:58.510Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:18:06.341Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:18:26.341Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:18:46.349Z"}
{"level":"info","message":"Session authenticated","sessionId":"8487921219","timestamp":"2025-06-18T06:19:07.599Z"}
{"clientInfo":{"pushname":"Jay Shah","wid":"<EMAIL>"},"level":"info","message":"Session ready","sessionId":"8487921219","timestamp":"2025-06-18T06:19:10.515Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:19:59.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:20:59.409Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:21:19.302Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:21:39.344Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:21:59.445Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:22:19.329Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:23:30.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:24:30.359Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:24:50.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:25:10.337Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:25:30.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:25:50.346Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:27:01.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:28:01.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:28:21.341Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:28:41.344Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:29:01.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:29:21.365Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:30:32.753Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:31:32.751Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:31:52.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:32:12.719Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:32:32.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:32:52.731Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:34:03.764Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:35:03.757Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:35:23.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:35:43.767Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:36:03.820Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:36:23.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:37:34.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:38:34.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:38:54.921Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:39:14.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:39:34.935Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:39:54.960Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:41:06.099Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:42:06.125Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:42:26.084Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:42:46.063Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:43:06.127Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:43:26.071Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:44:37.386Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:45:37.369Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:45:57.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:46:17.374Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:46:37.389Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:46:57.441Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:48:08.571Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:49:08.551Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:49:28.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:49:48.595Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:50:08.557Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:50:28.566Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:51:39.824Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:52:39.833Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:52:59.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:53:19.804Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:53:39.802Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:53:59.825Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:55:10.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:56:10.805Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:56:30.812Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:56:50.890Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:57:10.851Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:57:30.835Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:58:41.991Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T06:59:42.003Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1739819,"external":5089664,"heapTotal":57520128,"heapUsed":52458880,"rss":128888832},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T07:00:00.479Z","totalSessions":2,"uptime":5803.704178054}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:00:02.010Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:00:22.055Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:00:42.027Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:01:02.011Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:02:13.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:03:13.336Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:03:33.340Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:03:53.368Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:04:13.340Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:04:33.355Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:05:44.188Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:06:44.271Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:07:04.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:07:24.236Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:07:44.193Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:08:04.296Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:09:15.371Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:10:15.383Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:10:35.368Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:10:55.372Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:11:15.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:11:35.389Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:12:46.405Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:13:46.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:14:06.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:14:26.454Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:14:46.426Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:15:06.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:16:17.550Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:17:17.475Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:17:37.470Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:17:57.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:18:17.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:18:37.486Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:19:48.444Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:20:48.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:21:08.455Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:21:28.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:21:48.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:22:08.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:23:19.645Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:24:19.662Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:24:39.650Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:24:59.652Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:25:19.654Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:25:39.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:26:51.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:27:51.007Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:28:10.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:28:30.998Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:28:50.977Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:29:11.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:30:21.938Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:31:21.945Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:31:41.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:32:01.923Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:32:21.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:32:41.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:33:53.418Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:34:53.490Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:35:13.392Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:35:33.385Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:35:53.388Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:36:13.398Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:37:24.434Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:38:24.440Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:38:44.439Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:39:04.446Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:39:24.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:39:44.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:40:55.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:41:55.571Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:42:15.548Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:42:35.591Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:42:55.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:43:15.557Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:44:26.861Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:45:26.861Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:45:46.845Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:46:06.841Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:46:26.828Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:46:46.829Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:47:57.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:48:57.866Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:49:17.840Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:49:37.839Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:49:57.854Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:50:17.846Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:51:29.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:52:29.064Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:52:49.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:53:09.072Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:53:29.081Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:53:49.063Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:55:00.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:56:00.178Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:56:20.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:56:40.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:57:00.167Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:57:20.181Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:58:31.309Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:59:31.332Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T07:59:51.312Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1334571,"external":4684416,"heapTotal":58044416,"heapUsed":52083872,"rss":138960896},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T08:00:00.241Z","totalSessions":2,"uptime":9403.466313812}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:00:11.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:00:31.361Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:00:51.336Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:02:02.651Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:03:02.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:03:22.641Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:03:42.638Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:04:02.648Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:04:22.634Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:05:33.528Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:06:33.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:06:53.529Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:07:13.558Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:07:33.539Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:07:53.570Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:09:04.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:10:04.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:10:24.884Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:10:44.885Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:11:04.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:11:24.895Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:12:36.023Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:13:36.016Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:13:56.119Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:14:16.063Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:14:36.045Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:14:56.074Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:16:07.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:17:07.006Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:17:26.995Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:17:47.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:18:07.003Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:18:27.033Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:19:38.348Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:20:38.445Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:20:58.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:21:18.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:21:38.370Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:21:58.367Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:23:09.312Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:24:09.310Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:24:29.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:24:49.317Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:25:09.367Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:25:29.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:26:40.350Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:27:40.365Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:28:00.340Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:28:20.372Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:28:40.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:29:00.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:30:11.781Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:31:11.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:31:31.848Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:31:51.797Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:32:11.796Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:32:31.801Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:33:43.362Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:34:43.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:35:03.358Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:35:23.414Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:35:43.388Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:36:03.392Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:37:14.490Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:38:14.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:38:34.509Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:38:54.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:39:14.509Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:39:34.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:40:45.593Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:41:45.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:42:05.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:42:25.637Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:42:45.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:43:05.616Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:44:16.963Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:45:16.917Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:45:36.891Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:45:57.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:46:16.911Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:46:36.930Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:47:48.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:48:48.331Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:49:08.318Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:49:28.306Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:49:48.325Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:50:08.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:51:19.858Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:52:19.825Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:52:39.840Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:52:59.817Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:53:19.840Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:53:39.879Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:54:50.899Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:55:50.928Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:56:10.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:56:30.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:56:50.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:57:10.901Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:58:22.023Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:59:22.022Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T08:59:42.016Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":544515,"external":4685549,"heapTotal":58306560,"heapUsed":53696192,"rss":138551296},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T09:00:00.040Z","totalSessions":2,"uptime":13003.265190315}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:00:02.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:00:22.035Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:00:42.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:01:53.541Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:02:53.484Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:03:13.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:03:33.458Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:03:53.429Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:04:13.438Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:05:24.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:06:24.524Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:06:44.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:07:04.570Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:07:24.560Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:07:44.552Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:08:55.716Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:09:55.685Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:10:15.676Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:10:35.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:10:55.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:11:15.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:12:26.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:13:26.909Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:13:46.937Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:14:06.926Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:14:26.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:14:46.908Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:15:57.906Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:16:57.976Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:17:17.971Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:17:37.937Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:17:57.922Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:18:17.931Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:19:29.269Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:20:29.350Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:20:49.273Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:21:09.373Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:21:29.276Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:21:49.288Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:23:00.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:24:00.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:24:20.469Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:24:40.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:25:00.468Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:25:20.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:26:31.426Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:27:31.422Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:27:51.448Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:28:11.439Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:28:31.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:28:51.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:30:02.545Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:31:02.542Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:31:22.535Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:31:42.535Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:32:02.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:32:22.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:33:33.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:34:33.503Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:34:53.509Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:35:13.529Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:35:33.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:35:53.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:37:04.815Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:38:04.874Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:38:24.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:38:44.831Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:39:04.863Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:39:24.834Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:40:36.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:41:35.986Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:41:56.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:42:15.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:42:35.995Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:42:55.992Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:44:06.996Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:45:07.045Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:45:26.991Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:45:47.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:46:07.039Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:46:27.029Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:47:38.320Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:48:38.356Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:48:58.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:49:18.319Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:49:38.323Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:49:58.321Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:51:09.521Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:52:09.535Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:52:29.553Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:52:49.545Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:53:09.540Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:53:29.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:54:40.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:55:40.719Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:56:00.753Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:56:20.714Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:56:40.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:57:00.721Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:58:11.770Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:59:11.789Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:59:31.772Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T09:59:51.842Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1297971,"external":4647816,"heapTotal":58830848,"heapUsed":47080080,"rss":132038656},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T10:00:00.969Z","totalSessions":2,"uptime":16604.195055794}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:00:11.780Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:00:31.788Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:01:43.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:02:43.346Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:03:03.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:03:23.312Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:03:43.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:04:03.359Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:05:14.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:06:15.005Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:06:34.962Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:06:54.945Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:07:14.978Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:07:35.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:08:45.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:09:45.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:10:05.857Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:10:25.849Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:10:45.853Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:11:05.853Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:12:16.830Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:13:16.834Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:13:36.837Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:13:56.801Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:14:16.851Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:14:36.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:15:47.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:16:47.960Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:17:07.960Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:17:27.963Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:17:47.973Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:18:07.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:19:19.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:20:19.043Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:20:39.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:20:59.069Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:21:19.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:21:39.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:22:50.097Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:23:50.106Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:24:10.098Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:24:30.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:24:50.140Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:25:10.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:26:21.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:27:21.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:27:41.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:28:01.064Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:28:21.064Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:28:41.063Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:29:52.998Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:30:52.993Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:31:12.997Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:31:33.013Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:31:53.001Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:32:13.069Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:33:24.205Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:34:24.218Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:34:44.213Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:35:04.229Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:35:24.227Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:35:44.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:36:55.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:37:55.415Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:38:15.428Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:38:35.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:38:55.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:39:15.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:40:26.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:41:26.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:41:46.467Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:42:06.466Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:42:26.475Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:42:46.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:43:57.508Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:44:57.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:45:17.515Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:45:37.513Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:45:57.519Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:46:17.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:47:28.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:48:28.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:48:48.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:49:08.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:49:28.616Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:49:48.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:50:59.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:51:59.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:52:19.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:52:39.466Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:52:59.468Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:53:19.518Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:54:30.325Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:55:30.322Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:55:50.331Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:56:10.333Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:56:30.337Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:56:50.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:58:01.402Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:59:01.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:59:21.409Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T10:59:41.409Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1311617,"external":4661462,"heapTotal":55685120,"heapUsed":53135360,"rss":143253504},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T11:00:00.962Z","totalSessions":2,"uptime":20204.188077128}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:00:01.415Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:00:21.421Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:01:32.463Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:02:32.467Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:02:52.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:03:12.468Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:03:32.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:03:52.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:05:03.280Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:06:03.278Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:06:23.279Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:06:43.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:07:03.287Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:07:23.333Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:08:34.423Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:09:34.421Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:09:54.423Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:10:14.432Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:10:34.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:10:54.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:12:05.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:13:05.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:13:25.422Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:13:45.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:14:05.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:14:25.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:15:36.289Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:16:36.296Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:16:56.292Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:17:16.308Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:17:36.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:17:56.310Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:19:07.328Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:20:07.322Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:20:27.322Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:20:47.328Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:21:07.330Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:21:27.332Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:22:38.390Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:23:38.394Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:23:58.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:24:18.400Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:24:38.406Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:24:58.409Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:26:09.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:27:09.287Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:27:29.278Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:27:49.281Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:28:09.301Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:28:29.343Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:29:40.321Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:30:40.315Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:31:00.329Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:31:20.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:31:40.333Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:32:00.354Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:33:11.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:34:11.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:34:31.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:34:51.408Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:35:11.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:35:31.431Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:36:42.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:37:42.757Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:38:02.755Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:38:22.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:38:42.765Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:39:02.775Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:40:13.884Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:41:13.886Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:41:33.885Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:41:53.888Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:42:13.898Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:42:33.899Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:43:45.148Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:44:45.146Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:45:05.146Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:45:25.155Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:45:45.161Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:46:05.164Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:47:16.147Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:48:16.147Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:48:36.145Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:48:56.152Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:49:16.161Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:49:36.160Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:50:47.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:51:47.216Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:52:07.221Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:52:27.225Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:52:47.231Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:53:07.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:54:18.191Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:55:18.200Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:55:38.197Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:55:58.199Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:56:18.196Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:56:38.201Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:57:49.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:58:49.238Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:59:09.237Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:59:29.241Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T11:59:49.248Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1307257,"external":4657102,"heapTotal":58306560,"heapUsed":53743376,"rss":146145280},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T12:00:00.861Z","totalSessions":2,"uptime":23804.086957128}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:00:09.246Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:01:20.094Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:02:20.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:02:40.117Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:03:00.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:03:20.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:03:40.113Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:04:50.974Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:05:50.985Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:06:10.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:06:30.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:06:50.990Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:07:10.986Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:08:21.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:09:21.831Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:09:41.831Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:10:01.834Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:10:21.839Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:10:41.930Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:11:52.818Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:12:52.738Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:13:12.742Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:13:32.744Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:13:52.747Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:14:12.760Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:15:23.677Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:16:23.677Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:16:43.681Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:17:03.703Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:17:23.690Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:17:43.697Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:18:54.695Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:19:54.682Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:20:14.704Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:20:34.717Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:20:54.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:21:14.693Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:22:25.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:23:25.608Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:23:45.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:24:05.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:24:25.644Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:24:45.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:25:56.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:26:56.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:27:16.513Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:27:36.520Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:27:56.525Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:28:16.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:29:27.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:30:27.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:38:30.985Z"}
{"level":"info","message":"Session authenticated","sessionId":"8487921219","timestamp":"2025-06-18T12:38:33.233Z"}
{"clientInfo":{"pushname":"Jay Shah","wid":"<EMAIL>"},"level":"info","message":"Session ready","sessionId":"8487921219","timestamp":"2025-06-18T12:38:33.433Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:39:30.639Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:39:50.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:40:10.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:40:30.605Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:40:50.623Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:42:01.640Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:43:01.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:43:21.629Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:43:41.631Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:44:01.677Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:44:21.657Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:45:32.691Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:46:32.685Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:46:52.687Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:47:12.691Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:47:32.710Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:47:52.696Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:49:04.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:50:04.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:50:24.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:50:44.088Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:51:04.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:51:24.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:52:34.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:53:34.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:53:54.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:54:15.013Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:54:35.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:54:55.005Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:56:05.960Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:57:05.982Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:57:25.909Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:57:45.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:58:05.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:58:25.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T12:59:36.943Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1711270,"external":5053558,"heapTotal":56217600,"heapUsed":46590712,"rss":121962496},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T13:00:00.307Z","totalSessions":2,"uptime":1303.112961176}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:00:36.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:00:56.954Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:01:16.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:01:36.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:01:56.956Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:03:08.124Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:04:08.100Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:04:28.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:04:48.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:05:08.152Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:05:28.114Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:06:39.124Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:07:39.115Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:07:59.126Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:08:19.129Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:08:39.190Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:08:59.138Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:10:10.357Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:11:10.342Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:11:30.356Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:11:50.470Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:12:10.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:12:30.390Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:13:41.257Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:14:41.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:15:01.254Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:15:21.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:15:41.292Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:16:01.264Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:17:12.521Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:18:12.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:18:32.519Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:18:52.519Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:19:12.550Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:19:32.529Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:20:43.453Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:21:43.447Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:22:03.448Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:22:23.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:22:43.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:23:03.458Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:24:14.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:25:14.519Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:25:34.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:25:54.535Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:26:14.566Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:26:34.536Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:27:45.593Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:28:45.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:29:05.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:29:25.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:29:45.639Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:30:05.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:31:16.751Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:32:16.752Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:32:36.756Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:32:56.764Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:33:16.800Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:33:36.764Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:34:48.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:35:48.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:36:08.015Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:36:28.022Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:36:48.062Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:37:08.025Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:38:19.157Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:39:19.161Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:39:39.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:39:59.160Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:40:19.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:40:39.169Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:41:50.037Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:42:50.050Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:43:10.045Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:43:30.049Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:43:50.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:44:10.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:45:21.089Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:46:21.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:46:41.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:47:01.096Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:47:21.134Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:47:41.106Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:48:52.096Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:49:52.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:50:12.097Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:50:32.100Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:50:52.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:51:12.097Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:52:23.175Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:53:23.135Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:53:43.140Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:54:03.153Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:54:23.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:54:43.149Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:55:54.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:56:54.010Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:57:14.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:57:34.020Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:57:54.063Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:58:14.020Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T13:59:25.096Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1341368,"external":4683696,"heapTotal":54382592,"heapUsed":46632944,"rss":120266752},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T14:00:00.296Z","totalSessions":2,"uptime":4903.102267752}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:00:25.080Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:00:45.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:01:05.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:01:25.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:01:45.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:02:56.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:03:56.080Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:04:16.080Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:04:36.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:04:56.167Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:05:16.118Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:06:27.073Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:07:27.074Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:07:47.075Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:08:07.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:08:27.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:08:47.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:09:57.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:10:58.011Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:11:17.995Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:11:37.990Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:11:58.028Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:12:17.997Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:13:29.082Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:14:29.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:14:49.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:15:09.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:15:29.081Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:15:49.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:17:00.314Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:18:00.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:18:20.317Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:18:40.373Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:19:00.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:19:20.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:20:31.329Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:21:31.301Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:21:51.331Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:22:11.351Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:22:31.309Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:22:51.319Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:24:02.551Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:25:02.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:25:22.545Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:25:42.563Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:26:02.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:26:22.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:27:33.473Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:28:33.477Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:28:53.484Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:29:13.499Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:29:33.494Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:29:53.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:31:04.467Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:32:04.441Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:32:24.451Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:32:44.491Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:33:04.442Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:33:24.447Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:34:35.491Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:35:35.492Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:35:55.494Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:36:15.528Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:36:35.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:36:55.500Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:38:06.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:39:06.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:39:26.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:39:46.629Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:40:06.606Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:40:26.609Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:41:37.563Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:42:37.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:42:57.569Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:43:17.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:43:37.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:43:57.570Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:45:08.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:46:08.623Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:46:28.610Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:46:48.634Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:47:08.631Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:47:28.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:48:39.791Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:49:39.789Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:49:59.790Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:50:19.858Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:50:39.801Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:50:59.807Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:52:10.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:53:10.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:53:30.950Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:53:50.991Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:54:10.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:54:30.948Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:55:41.935Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:56:41.934Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:57:01.938Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:57:21.988Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:57:41.948Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:58:01.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T14:59:13.017Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":935281,"external":4277609,"heapTotal":56741888,"heapUsed":48486880,"rss":123432960},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T15:00:00.215Z","totalSessions":2,"uptime":8503.021227962}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:00:13.017Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:00:33.026Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:00:53.061Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:01:13.023Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:01:33.029Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:02:44.100Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:03:44.080Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:04:04.103Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:04:24.095Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:04:44.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:05:04.087Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:06:14.936Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:07:14.946Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:07:34.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:07:54.939Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:08:14.939Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:08:34.968Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:09:45.807Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:10:45.827Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:11:05.844Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:11:25.820Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:11:45.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:12:05.848Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:13:16.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:14:16.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:14:36.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:14:56.749Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:15:16.865Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:15:36.747Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:16:47.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:17:47.714Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:18:07.737Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:18:27.711Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:18:47.714Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:19:07.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:20:18.657Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:21:18.658Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:21:38.699Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:21:58.665Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:22:18.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:22:38.675Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:23:49.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:24:49.730Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:25:09.752Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:25:29.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:25:49.737Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:26:09.736Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:27:20.605Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:28:20.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:28:40.609Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:29:00.639Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:29:20.623Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:29:40.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:30:51.569Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:31:51.595Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:32:11.564Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:32:31.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:32:51.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:33:11.564Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:34:22.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:35:22.627Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:35:42.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:36:02.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:36:22.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:36:42.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:37:53.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:38:53.704Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:39:13.691Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:39:33.671Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:39:53.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:40:13.691Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:41:24.842Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:42:24.788Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:42:44.787Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:43:04.786Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:43:24.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:43:44.797Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:44:56.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:45:56.409Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:46:16.375Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:46:36.378Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:46:56.374Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:47:16.394Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:48:27.688Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:49:27.652Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:49:47.651Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:50:07.658Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:50:27.661Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:50:47.665Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:51:58.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:52:58.601Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:53:18.591Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:53:38.592Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:53:58.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:54:18.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:55:29.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:56:29.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:56:49.552Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:57:09.554Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:57:29.554Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:57:49.567Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T15:59:00.662Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":926727,"external":4269055,"heapTotal":56217600,"heapUsed":44972200,"rss":137207808},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T16:00:00.141Z","totalSessions":2,"uptime":12102.94652621}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:00:00.640Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:00:20.637Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:00:40.643Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:01:00.641Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:01:20.652Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:02:31.901Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:03:31.856Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:03:51.861Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:04:11.865Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:04:31.868Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:04:51.873Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:06:03.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:07:03.531Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:07:23.537Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:07:43.540Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:08:03.539Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:08:23.541Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:09:34.454Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:10:34.453Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:10:54.455Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:11:14.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:11:34.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:11:54.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:13:06.131Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:14:06.119Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:14:26.130Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:14:46.184Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:15:06.132Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:15:26.143Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:16:37.237Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:17:37.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:17:57.239Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:18:17.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:18:37.243Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:18:57.248Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:20:08.110Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:21:08.103Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:21:28.111Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:21:48.112Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:22:08.111Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:22:28.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:23:39.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:24:39.401Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:24:59.420Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:25:19.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:25:39.418Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:25:59.414Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:27:10.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:28:10.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:28:30.362Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:28:50.401Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:29:10.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:29:30.369Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:30:41.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:31:41.456Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:32:01.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:32:21.495Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:32:41.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:33:01.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:34:12.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:35:12.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:35:32.521Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:35:52.570Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:36:12.524Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:36:32.520Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:37:43.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:38:43.781Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:39:03.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:39:23.822Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:39:43.787Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:40:03.790Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:41:14.931Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:42:14.936Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:42:34.948Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:42:54.990Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:43:14.939Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:43:34.946Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:44:46.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:45:46.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:46:06.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:46:26.099Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:46:46.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:47:06.070Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:48:17.172Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:49:17.159Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:49:37.163Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:49:57.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:50:17.161Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:50:37.161Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:51:48.238Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:52:48.237Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:53:08.251Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:53:28.238Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:53:48.242Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:54:08.241Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:55:19.177Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:56:19.168Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:56:39.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:56:59.171Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:57:19.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:57:39.179Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:58:50.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T16:59:50.343Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":914988,"external":4257316,"heapTotal":56741888,"heapUsed":45239120,"rss":136581120},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T17:00:00.049Z","totalSessions":2,"uptime":15702.854322902}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:00:10.337Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:00:30.322Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:00:50.325Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:01:10.328Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:02:21.415Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:03:21.432Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:03:41.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:04:01.428Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:04:21.447Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:04:41.442Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:05:52.378Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:06:52.400Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:07:12.383Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:07:32.384Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:07:52.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:08:12.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:09:23.356Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:10:23.384Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:10:43.357Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:11:03.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:11:23.361Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:11:43.365Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:12:54.401Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:13:54.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:14:14.390Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:14:34.390Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:14:54.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:15:14.397Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:16:25.483Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:17:25.481Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:17:45.485Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:18:05.484Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:18:25.490Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:18:45.490Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:19:56.492Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:20:56.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:21:16.458Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:21:36.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:21:56.467Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:22:16.467Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:23:27.406Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:24:27.368Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:24:47.366Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:25:07.371Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:25:27.378Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:25:47.382Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:26:58.381Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:27:58.308Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:28:18.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:28:38.312Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:28:58.315Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:29:18.318Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:30:29.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:31:29.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:31:49.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:32:09.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:32:29.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:32:49.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:34:00.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:35:00.487Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:35:20.491Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:35:40.489Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:36:00.494Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:36:20.500Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:37:31.624Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:38:31.590Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:38:51.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:39:11.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:39:31.615Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:39:51.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:41:02.520Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:42:02.477Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:42:22.477Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:42:42.476Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:43:02.476Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:43:22.483Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:44:33.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:45:33.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:45:53.468Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:46:13.458Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:46:33.463Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:46:53.468Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:48:04.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:49:04.498Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:49:24.501Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:49:44.508Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:50:04.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:50:24.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:51:35.747Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:52:35.730Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:52:55.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:53:15.721Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:53:35.723Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:53:55.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:55:06.864Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:56:06.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:56:26.812Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:56:46.821Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:57:06.816Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:57:26.820Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:58:37.810Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:59:37.774Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T17:59:57.780Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1707447,"external":5049775,"heapTotal":54120448,"heapUsed":48341248,"rss":140771328},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T18:00:00.953Z","totalSessions":2,"uptime":19303.758542733}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:00:17.782Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:00:37.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:00:57.793Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:02:08.859Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:03:08.822Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:03:28.823Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:03:48.827Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:04:08.828Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:04:28.838Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:05:39.882Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:06:39.857Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:06:59.856Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:07:19.860Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:07:39.870Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:07:59.874Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:09:10.969Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:10:10.938Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:10:30.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:10:50.953Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:11:10.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:11:30.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:12:42.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:13:41.997Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:14:02.007Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:14:22.006Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:14:42.007Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:15:02.016Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:16:13.112Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:17:13.077Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:17:33.077Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:17:53.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:18:13.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:18:33.128Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:19:43.909Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:20:43.911Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:21:03.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:21:23.922Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:21:43.944Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:22:03.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:23:15.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:24:15.108Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:24:35.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:24:55.036Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:25:15.045Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:25:35.084Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:26:46.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:27:46.039Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:28:06.052Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:28:26.050Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:28:46.052Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:29:06.074Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:30:17.293Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:31:17.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:31:37.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:31:57.293Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:32:17.290Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:32:37.296Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:33:48.292Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:34:48.279Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:35:08.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:35:28.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:35:48.330Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:36:08.285Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:37:19.337Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:38:19.352Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:38:39.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:38:59.343Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:39:19.379Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:39:39.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:40:50.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:41:50.449Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:42:10.443Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:42:30.449Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:42:50.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:43:10.450Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:44:21.276Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:45:21.274Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:45:41.275Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:46:01.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:46:21.380Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:46:41.289Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:47:52.223Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:48:52.222Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:49:12.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:49:32.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:49:52.287Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:50:12.276Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:51:23.413Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:52:23.406Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:52:43.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:53:03.414Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:53:23.438Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:53:43.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:54:54.209Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:55:54.197Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:56:14.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:56:34.231Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:56:54.210Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:57:14.204Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:58:25.326Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:59:25.321Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T18:59:45.331Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1736207,"external":5086052,"heapTotal":57790464,"heapUsed":53202672,"rss":144855040},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T19:00:00.757Z","totalSessions":2,"uptime":22903.562653045}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:00:05.396Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:00:25.341Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:00:45.342Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:01:56.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:02:56.286Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:03:16.291Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:03:36.331Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:03:56.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:04:16.296Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:05:27.304Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:06:27.286Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:06:47.330Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:07:07.298Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:07:27.304Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:07:47.321Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:08:58.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:09:58.597Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:10:18.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:10:38.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:10:58.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:11:18.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:12:29.627Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:13:29.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:13:49.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:14:09.628Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:14:29.665Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:14:49.630Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:16:00.560Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:17:00.615Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:17:20.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:17:40.571Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:18:00.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:18:20.578Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:19:31.640Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:20:31.641Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:20:51.615Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:21:11.613Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:21:31.617Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:21:51.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:23:02.733Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:24:02.704Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:24:22.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:24:42.706Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:25:02.706Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:25:22.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:26:33.692Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:27:33.593Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:27:53.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:28:13.597Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:28:33.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:28:53.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:30:04.510Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:31:04.468Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:31:24.467Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:31:44.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:32:04.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:32:24.489Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:33:35.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:34:35.484Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:34:55.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:35:15.485Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:35:35.486Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:35:55.486Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:37:06.702Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:38:06.641Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:38:26.648Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:38:46.655Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:39:06.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:39:26.657Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:40:37.884Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:41:37.828Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:41:57.831Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:42:17.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:42:37.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:42:57.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:44:08.844Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:45:08.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:45:28.810Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:45:48.814Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:46:08.825Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:46:28.824Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:47:40.143Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:48:40.106Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:49:00.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:49:20.112Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:49:40.112Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:50:00.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:51:11.408Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:52:11.386Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:52:31.390Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:52:51.391Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:53:11.396Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:53:31.405Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:54:42.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:55:42.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:56:02.543Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:56:22.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:56:42.557Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:57:02.553Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:58:13.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:59:13.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:59:33.426Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T19:59:53.447Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1732450,"external":5082295,"heapTotal":58839040,"heapUsed":50781992,"rss":131547136},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T20:00:00.683Z","totalSessions":2,"uptime":26503.488566923}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:00:13.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:00:33.442Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:01:44.520Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:02:44.487Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:03:04.491Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:03:24.499Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:03:44.498Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:04:04.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:05:15.621Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:06:15.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:06:35.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:06:55.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:07:15.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:07:35.590Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:08:46.529Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:09:46.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:10:06.489Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:10:26.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:10:46.485Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:11:06.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:12:17.558Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:13:17.510Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:13:37.504Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:13:57.508Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:14:17.541Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:14:37.528Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:15:48.446Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:16:48.392Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:17:08.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:17:28.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:17:48.423Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:18:08.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:19:19.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:20:19.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:20:39.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:20:59.498Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:21:19.510Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:21:39.504Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:22:50.398Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:23:50.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:24:10.357Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:24:30.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:24:50.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:25:10.376Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:26:21.552Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:27:21.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:27:41.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:28:01.514Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:28:21.514Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:28:41.520Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:29:52.796Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:30:52.747Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:31:12.748Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:31:32.753Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:31:52.756Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:32:12.759Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:33:23.842Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:34:23.793Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:34:43.791Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:35:03.789Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:35:23.803Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:35:43.804Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:36:54.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:37:54.841Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:38:14.859Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:38:34.851Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:38:54.854Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:39:14.862Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:40:25.822Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:41:25.785Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:41:45.775Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:42:05.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:42:25.783Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:42:45.795Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:43:56.683Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:44:56.659Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:45:16.674Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:45:36.670Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:45:56.675Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:46:16.676Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:47:27.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:48:27.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:48:47.885Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:49:07.882Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:49:27.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:49:47.886Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:50:58.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:51:58.963Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:52:18.948Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:52:38.954Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:52:58.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:53:18.982Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:54:30.120Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:55:30.084Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:55:50.089Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:56:10.088Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:56:30.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:56:50.106Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:58:00.960Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:59:00.937Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:59:20.937Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T20:59:40.942Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1733714,"external":5083559,"heapTotal":60411904,"heapUsed":50777008,"rss":133320704},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T21:00:00.624Z","totalSessions":2,"uptime":30103.429666861}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:00:00.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:00:20.954Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:01:32.081Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:02:32.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:02:52.055Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:03:12.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:03:32.061Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:03:52.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:05:03.214Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:06:03.172Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:06:23.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:06:43.166Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:07:03.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:07:23.181Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:08:34.382Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:09:34.350Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:09:54.353Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:10:14.358Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:10:34.356Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:10:54.364Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:12:05.510Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:13:05.450Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:13:25.453Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:13:45.456Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:14:05.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:14:25.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:15:37.066Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:16:37.015Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:16:57.023Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:17:17.017Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:17:37.019Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:17:57.022Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:19:08.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:20:08.005Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:20:28.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:20:48.007Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:21:08.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:21:28.025Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:22:39.232Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:23:39.210Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:23:59.213Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:24:19.215Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:24:39.219Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:24:59.221Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:26:10.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:27:10.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:27:30.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:27:50.207Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:28:10.214Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:28:30.216Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:29:41.141Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:30:41.078Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:31:01.075Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:31:21.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:31:41.080Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:32:01.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:33:11.931Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:34:11.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:34:31.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:34:51.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:35:11.908Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:35:31.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:36:42.775Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:37:42.759Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:38:02.756Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:38:22.763Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:38:42.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:39:02.766Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:40:13.701Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:41:13.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:41:33.652Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:41:53.657Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:42:13.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:42:33.669Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:43:44.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:44:44.690Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:45:04.688Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:45:24.692Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:45:44.694Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:46:04.696Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:47:15.706Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:48:15.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:48:35.668Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:48:55.671Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:49:15.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:49:35.678Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:50:46.722Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:51:46.686Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:52:06.693Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:52:26.692Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:52:46.696Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:53:06.704Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:54:17.843Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:55:17.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:55:37.782Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:55:57.787Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:56:17.789Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:56:37.794Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:57:49.298Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:58:49.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:59:09.296Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:59:29.292Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T21:59:49.297Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1734227,"external":5084072,"heapTotal":59363328,"heapUsed":52294984,"rss":132759552},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T22:00:00.444Z","totalSessions":2,"uptime":33703.250255857}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:00:09.302Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:01:20.159Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:02:20.128Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:02:40.133Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:03:00.129Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:03:20.133Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:03:40.150Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:04:51.246Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:05:51.185Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:06:11.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:06:31.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:06:51.192Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:07:11.195Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:08:22.295Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:09:22.259Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:09:42.263Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:10:02.274Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:10:22.273Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:10:42.272Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:11:53.861Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:12:53.817Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:13:13.822Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:13:33.839Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:13:53.826Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:14:13.854Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:15:24.807Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:16:24.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:16:44.765Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:17:04.766Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:17:24.771Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:17:44.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:18:55.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:19:55.891Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:20:15.893Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:20:35.901Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:20:55.903Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:21:15.922Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:22:26.978Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:23:26.922Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:23:46.923Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:24:06.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:24:26.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:24:46.929Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:25:57.844Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:26:57.772Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:27:17.774Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:27:37.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:27:57.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:28:17.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:29:28.631Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:30:28.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:30:48.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:31:08.604Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:31:28.606Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:31:48.609Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:32:59.875Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:33:59.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:34:19.833Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:34:39.834Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:34:59.835Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:35:19.846Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:36:30.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:37:30.716Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:37:50.717Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:38:10.719Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:38:30.730Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:38:50.729Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:40:01.653Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:41:01.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:41:21.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:41:41.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:42:01.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:42:21.617Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:43:32.894Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:44:32.857Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:44:52.851Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:45:12.855Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:45:32.862Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:45:52.865Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:47:03.959Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:48:03.912Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:48:23.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:48:43.923Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:49:03.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:49:23.928Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:50:34.882Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:51:34.818Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:51:54.821Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:52:14.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:52:34.825Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:52:54.830Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:54:05.710Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:55:05.679Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:55:25.676Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:55:45.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:56:05.679Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:56:25.688Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:57:38.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:58:37.986Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:58:57.968Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:59:17.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:59:37.973Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T22:59:57.978Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1734752,"external":5084597,"heapTotal":59887616,"heapUsed":54250632,"rss":133074944},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-18T23:00:00.249Z","totalSessions":2,"uptime":37303.055085615}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:01:09.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:02:08.966Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:02:28.971Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:02:48.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:03:08.973Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:03:28.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:04:40.310Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:05:40.253Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:06:00.258Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:06:20.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:06:40.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:07:00.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:08:11.321Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:09:11.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:09:31.248Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:09:51.237Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:10:11.237Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:10:31.242Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:11:42.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:12:42.145Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:13:02.140Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:13:22.143Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:13:42.146Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:14:02.169Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:15:14.167Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:16:14.136Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:16:34.131Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:16:54.136Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:17:14.139Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:17:34.145Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:18:45.095Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:19:45.071Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:20:05.069Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:20:25.073Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:20:45.080Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:21:05.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:22:16.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:23:16.630Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:23:36.627Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:23:56.641Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:24:16.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:24:36.641Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:25:48.136Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:26:48.093Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:27:08.096Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:27:28.100Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:27:48.106Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:28:08.109Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:29:19.253Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:30:19.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:30:39.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:30:59.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:31:19.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:31:39.217Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:32:50.116Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:33:50.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:34:10.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:34:30.060Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:34:50.061Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:35:10.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:36:21.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:37:21.172Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:37:41.173Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:38:01.173Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:38:21.177Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:38:41.179Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:39:52.222Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:40:52.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:41:12.209Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:41:32.207Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:41:52.213Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:42:12.214Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:43:23.348Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:44:23.299Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:44:43.298Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:45:03.298Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:45:23.309Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:45:43.318Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:46:54.543Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:47:54.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:48:14.520Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:48:34.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:48:54.525Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:49:14.528Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:50:26.162Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:51:26.095Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:51:46.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:52:06.099Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:52:26.111Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:52:46.110Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:53:57.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:54:57.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:55:17.463Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:55:37.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:55:57.476Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:56:17.473Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:57:28.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:58:28.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:58:48.948Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:59:08.950Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:59:28.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T23:59:48.957Z"}
