{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":536936,"external":3866032,"heapTotal":46522368,"heapUsed":41088880,"rss":105926656},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T00:00:00.512Z","totalSessions":2,"uptime":10734.956917244}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":586088,"external":3915184,"heapTotal":46522368,"heapUsed":44289400,"rss":106004480},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T01:00:00.819Z","totalSessions":2,"uptime":14335.263366755}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":528744,"external":3857840,"heapTotal":47570944,"heapUsed":39840704,"rss":107118592},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T02:00:00.996Z","totalSessions":2,"uptime":17935.440902075}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":569704,"external":3898800,"heapTotal":47570944,"heapUsed":42894160,"rss":107298816},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T03:00:00.282Z","totalSessions":2,"uptime":21534.726125212}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":504168,"external":3833264,"heapTotal":47570944,"heapUsed":37498120,"rss":107266048},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T04:00:00.461Z","totalSessions":2,"uptime":25134.90539656}
{"activeQRCodes":0,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":520552,"external":3849648,"heapTotal":47833088,"heapUsed":38459608,"rss":108453888},"message":"Session statistics","sessionStatuses":{"8487921219":"error","test123":"error"},"timestamp":"2025-06-18T05:00:00.711Z","totalSessions":2,"uptime":28735.155368487}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"8487921219","timestamp":"2025-06-18T05:14:13.512Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test123","timestamp":"2025-06-18T05:17:26.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:23:38.781Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:23:39.052Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:23:39.166Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:24:38.425Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:24:38.774Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:24:38.780Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:24:58.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:24:58.530Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:24:58.646Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:25:18.381Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:25:18.597Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:25:18.760Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:25:38.381Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:25:38.543Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:25:38.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:25:58.396Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:25:58.548Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:25:58.693Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:26:49.393Z"}
{"level":"info","message":"Instance created","phoneNumber":"919999999995","sessionId":"test_fixed","timestamp":"2025-06-18T05:26:49.408Z","webhookUrl":null}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:27:09.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:27:09.729Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:27:10.519Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:27:49.229Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:28:09.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:28:09.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:28:09.701Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:28:10.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:28:29.200Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:28:29.558Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:28:29.699Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:28:30.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:28:49.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:28:49.563Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:28:49.725Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:28:50.471Z"}
{"level":"info","message":"QR code generated","sessionId":"test_fixed","timestamp":"2025-06-18T05:29:09.211Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:29:09.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:29:09.761Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:29:10.501Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"8487921219","timestamp":"2025-06-18T05:29:11.521Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test_fixed","timestamp":"2025-06-18T05:29:21.440Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test_fixed","timestamp":"2025-06-18T05:29:29.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_reconnect","timestamp":"2025-06-18T05:29:29.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:29:29.715Z"}
{"level":"info","message":"Instance disconnected and deleted","sessionId":"test_reconnect","timestamp":"2025-06-18T05:29:39.049Z"}
{"level":"info","message":"QR code generated","sessionId":"8487921219","timestamp":"2025-06-18T05:30:04.689Z"}
{"level":"info","message":"Instance created","phoneNumber":"918487921219","sessionId":"8487921219","timestamp":"2025-06-18T05:30:04.708Z","webhookUrl":null}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-18T05:30:40.871Z"}
