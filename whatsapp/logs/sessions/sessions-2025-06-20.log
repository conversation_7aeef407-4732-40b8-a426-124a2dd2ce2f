{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1113131,"external":4475908,"heapTotal":63557632,"heapUsed":57131832,"rss":225595392},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T00:00:00.737Z","totalSessions":2,"uptime":127303.542814591}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:00:25.211Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:01:25.144Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:01:45.154Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:02:05.158Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:02:25.163Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:02:45.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:03:56.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:04:56.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:05:16.440Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:05:36.445Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:05:56.439Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:06:16.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:07:27.632Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:08:27.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:08:47.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:09:07.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:09:27.561Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:09:47.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:10:58.500Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:11:58.532Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:12:18.495Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:12:38.482Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:12:58.481Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:13:18.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:14:29.829Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:15:29.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:15:49.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:16:09.726Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:16:29.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:16:49.735Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:18:01.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:19:01.231Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:19:21.217Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:19:41.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:20:01.222Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:20:21.282Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:21:32.717Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:22:32.709Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:22:52.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:23:12.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:23:32.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:23:52.768Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:25:03.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:26:03.910Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:26:23.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:26:43.901Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:27:03.951Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:27:23.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:28:34.973Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:29:34.974Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:29:54.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:30:14.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:30:34.974Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:30:55.003Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:32:06.764Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:33:06.799Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:33:26.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:33:46.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:34:06.763Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:34:26.798Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:35:37.938Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:36:37.933Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:36:57.939Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:37:17.941Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:37:37.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:37:57.978Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:39:08.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:40:08.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:40:28.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:40:48.897Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:41:08.901Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:41:28.942Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:42:40.116Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:43:40.318Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:44:00.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:44:20.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:44:40.122Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:45:00.162Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:46:11.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:47:11.645Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:47:31.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:47:51.591Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:48:11.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:48:31.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:49:43.104Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:50:43.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:51:03.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:51:23.026Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:51:43.010Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:52:03.072Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:53:14.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:54:14.422Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:54:34.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:54:54.413Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:55:14.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:55:34.456Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:56:45.637Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:57:45.609Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:58:05.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:58:25.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:58:45.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:59:05.651Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":972779,"external":4335556,"heapTotal":61722624,"heapUsed":49746416,"rss":223580160},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T01:00:00.110Z","totalSessions":2,"uptime":130902.915488917}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:00:16.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:01:16.741Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:01:36.708Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:01:56.711Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:02:16.716Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:02:36.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:03:47.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:04:47.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:05:07.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:05:27.821Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:05:47.792Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:06:07.823Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:07:19.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:08:19.100Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:08:39.019Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:08:59.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:09:19.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:09:39.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:10:50.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:11:50.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:12:10.432Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:12:30.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:12:50.431Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:13:10.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:14:21.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:15:21.515Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:15:41.410Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:16:01.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:16:21.428Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:16:41.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:17:52.491Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:18:52.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:19:12.476Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:19:32.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:19:52.489Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:20:12.531Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:21:23.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:22:23.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:22:43.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:23:03.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:23:23.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:23:43.644Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:24:54.733Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:25:54.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:26:14.617Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:26:34.630Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:26:54.627Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:27:14.650Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:28:25.605Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:29:25.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:29:45.560Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:30:05.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:30:25.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:30:45.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:31:56.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:32:56.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:33:16.776Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:33:36.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:33:56.801Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:34:16.789Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:35:27.870Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:36:27.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:36:47.873Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:37:07.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:37:27.918Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:37:47.886Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:38:59.188Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:39:59.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:40:19.191Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:40:39.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:40:59.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:41:19.211Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:42:30.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:43:30.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:43:50.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:44:10.231Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:44:30.204Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:44:50.230Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:46:01.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:47:01.243Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:47:21.241Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:47:41.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:48:01.263Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:48:21.290Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:49:32.334Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:50:32.371Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:50:52.319Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:51:12.310Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:51:32.317Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:51:52.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:53:03.267Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:54:03.304Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:54:23.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:54:43.276Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:55:03.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:55:23.344Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:56:34.282Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:57:34.253Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:57:54.248Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:58:14.257Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:58:34.256Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:58:54.291Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":987878,"external":4350655,"heapTotal":57528320,"heapUsed":49760216,"rss":223051776},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T02:00:00.206Z","totalSessions":2,"uptime":134503.024757419}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:00:05.371Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:01:05.428Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:01:25.358Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:01:45.366Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:02:05.377Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:02:25.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:03:36.236Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:04:36.254Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:04:56.236Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:05:16.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:05:36.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:05:56.244Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:07:07.635Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:08:07.583Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:08:27.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:08:47.588Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:09:07.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:09:27.593Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:10:38.880Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:11:38.885Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:11:58.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:12:18.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:12:38.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:12:58.897Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:14:10.172Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:15:10.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:15:30.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:15:50.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:16:10.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:16:30.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:17:41.289Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:18:41.352Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:19:01.296Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:19:21.332Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:19:41.299Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:20:01.303Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:21:12.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:22:12.541Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:22:32.658Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:22:52.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:23:12.553Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:23:32.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:24:44.212Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:25:44.197Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:26:04.189Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:26:24.185Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:26:44.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:27:04.190Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:28:15.503Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:29:15.409Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:29:35.379Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:29:55.365Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:30:15.374Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:30:35.389Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:31:46.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:32:46.414Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:33:06.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:33:26.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:33:46.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:34:06.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:35:18.151Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:36:17.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:36:37.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:36:57.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:37:17.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:37:37.903Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:38:49.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:39:49.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:40:09.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:40:29.053Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:40:49.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:41:09.063Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:42:20.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:43:20.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:43:40.126Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:44:00.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:44:20.151Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:44:40.127Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:45:51.384Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:46:51.329Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:47:11.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:47:31.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:47:51.336Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:48:11.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:49:22.281Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:50:22.291Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:50:42.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:51:02.246Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:51:22.240Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:51:42.251Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:52:53.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:53:53.155Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:54:13.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:54:33.157Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:54:53.196Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:55:13.158Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:56:24.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:57:24.146Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:57:44.136Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:58:04.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:58:24.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:58:44.145Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:59:55.344Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":966240,"external":4329017,"heapTotal":61984768,"heapUsed":49314168,"rss":227581952},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T03:00:00.428Z","totalSessions":2,"uptime":138103.233799682}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:00:55.292Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:01:15.307Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:01:35.291Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:01:55.351Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:02:15.301Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:03:26.354Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:04:26.300Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:04:46.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:05:06.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:05:26.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:05:46.326Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:06:57.488Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:07:57.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:08:17.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:08:37.367Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:08:57.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:09:17.396Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:10:28.591Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:11:28.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:11:48.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:12:08.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:12:28.569Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:12:48.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:13:59.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:14:59.687Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:15:19.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:15:39.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:15:59.639Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:16:19.613Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:17:31.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:18:31.089Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:18:51.095Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:19:11.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:19:31.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:19:51.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:21:02.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:22:02.327Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:22:22.252Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:22:42.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:23:02.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:23:22.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:24:33.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:25:33.285Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:25:53.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:26:13.315Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:26:33.297Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:26:53.297Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:28:04.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:29:04.583Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:29:24.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:29:44.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:30:04.674Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:30:24.597Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:31:36.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:32:36.156Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:32:56.216Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:33:16.249Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:33:36.150Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:33:56.167Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:35:07.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:36:07.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:36:27.168Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:36:47.189Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:37:07.176Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:37:27.182Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:38:38.078Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:39:38.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:39:58.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:40:18.113Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:40:38.124Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:40:58.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:42:09.353Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:43:09.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:43:29.322Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:43:49.356Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:44:09.346Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:44:29.355Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:45:40.576Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:46:40.573Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:47:00.576Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:47:20.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:47:40.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:48:00.590Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:49:11.553Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:50:11.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:50:31.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:50:51.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:51:11.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:51:31.568Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:52:43.448Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:53:43.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:54:03.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:54:23.453Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:54:43.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:55:03.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:56:14.494Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:57:14.556Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:57:34.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:57:54.498Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:58:14.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:58:34.499Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:59:45.764Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1912619,"external":5275396,"heapTotal":63295488,"heapUsed":57180392,"rss":228040704},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T04:00:00.495Z","totalSessions":2,"uptime":141703.301083103}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:00:45.677Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:01:05.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:01:25.654Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:01:45.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:02:05.666Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:03:17.132Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:04:17.067Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:04:37.052Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:04:57.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:05:17.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:05:37.060Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:06:48.158Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:07:48.190Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:08:08.163Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:08:28.164Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:08:48.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:09:08.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:10:19.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:11:19.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:11:39.386Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:11:59.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:12:19.391Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:12:39.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:13:50.573Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:14:50.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:15:10.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:15:30.567Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:15:50.578Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:16:10.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:17:21.800Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:18:21.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:18:41.817Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:19:01.808Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:19:21.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:19:41.823Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:20:53.025Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:21:53.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:22:13.028Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:22:33.026Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:22:53.029Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:23:13.035Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:24:24.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:25:24.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:25:44.398Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:26:04.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:26:24.415Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:26:44.518Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:27:55.648Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:28:55.654Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:29:15.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:29:35.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:29:55.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:30:15.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:31:26.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:32:26.529Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:32:46.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:33:06.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:33:26.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:33:46.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:34:57.647Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:35:57.652Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:36:17.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:36:37.664Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:36:57.682Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:37:17.642Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:38:29.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:39:28.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:39:48.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:40:09.041Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:40:28.993Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:40:48.993Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:42:00.353Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:43:00.311Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:43:20.297Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:43:40.288Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:44:00.290Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:44:20.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:45:31.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:46:31.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:46:51.218Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:47:11.201Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:47:31.205Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:47:51.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:49:02.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:50:02.533Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:50:22.463Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:50:42.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:51:02.513Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:51:22.482Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:52:33.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:53:33.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:53:53.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:54:13.630Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:54:33.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:54:53.560Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:56:04.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:57:04.667Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:57:24.605Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:57:44.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:58:04.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:58:24.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:59:35.826Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1638166,"external":5000943,"heapTotal":61722624,"heapUsed":55742056,"rss":227008512},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T05:00:00.472Z","totalSessions":2,"uptime":145303.277989319}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:00:35.813Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:00:55.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:01:15.830Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:01:35.841Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:01:55.828Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:03:06.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:04:06.878Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:04:26.873Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:04:46.843Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:05:06.849Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:05:26.864Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:06:38.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:07:38.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:07:58.024Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:08:18.018Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:08:38.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:08:58.021Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:10:09.775Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:11:09.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:11:29.712Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:11:49.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:12:09.717Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:12:29.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:13:40.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:14:40.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:15:00.928Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:15:20.950Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:15:40.940Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:16:00.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:17:12.275Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:18:12.244Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:18:32.219Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:18:52.270Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:19:12.232Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:19:32.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:20:43.299Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:21:43.307Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:22:03.263Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:22:23.269Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:22:43.274Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:23:03.298Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:24:14.731Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:25:14.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:25:34.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:25:54.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:26:14.781Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:26:34.736Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:27:45.719Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:28:45.769Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:29:05.791Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:29:25.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:29:45.760Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:30:05.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:31:16.751Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:32:16.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:32:37.007Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:32:56.844Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:33:16.812Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:33:36.757Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:34:47.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:35:47.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:36:07.848Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:36:27.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:36:47.842Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:37:07.853Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:38:18.835Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:39:18.845Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:39:38.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:39:58.854Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:40:18.843Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:40:38.853Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:41:49.956Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:42:49.976Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:43:09.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:43:29.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:43:49.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:44:09.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:45:20.913Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:46:20.911Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:46:40.914Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:47:00.959Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:47:20.912Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:47:40.925Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:48:51.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:49:52.017Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:50:11.976Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:50:31.984Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:50:51.995Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:51:12.025Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:52:23.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:53:23.138Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:53:43.082Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:54:03.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:54:23.094Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:54:43.108Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:55:53.948Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:56:53.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:57:13.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:57:33.954Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:57:53.957Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:58:13.962Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:59:24.833Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1106694,"external":4469471,"heapTotal":59887616,"heapUsed":55649528,"rss":224477184},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T06:00:00.731Z","totalSessions":2,"uptime":148903.53661596}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:00:24.830Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:00:44.838Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:01:04.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:01:24.837Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:01:44.879Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:02:56.578Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:03:56.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:04:16.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:04:36.576Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:04:56.590Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:05:16.588Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:06:27.671Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:07:27.668Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:07:47.700Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:08:07.667Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:08:27.700Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:08:47.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:09:58.621Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:10:58.659Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:11:18.629Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:11:38.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:11:58.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:12:18.638Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:13:29.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:14:29.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:14:49.683Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:15:09.681Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:15:29.682Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:15:49.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:17:00.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:18:00.681Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:18:20.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:18:40.569Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:19:00.608Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:19:20.566Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:20:31.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:21:31.554Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:21:51.521Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:22:11.543Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:22:31.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:22:51.548Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:24:02.433Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:25:02.477Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:25:22.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:25:42.451Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:26:02.444Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:26:22.446Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:27:33.336Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:28:33.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:28:53.341Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:29:13.337Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:29:33.386Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:29:53.349Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:31:04.452Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:32:04.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:32:24.380Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:32:44.405Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:33:04.391Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:33:24.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:34:35.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:35:35.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:35:55.479Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:36:15.491Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:36:35.496Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:36:55.488Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:38:06.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:39:06.764Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:39:26.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:39:46.615Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:40:06.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:40:26.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:41:37.753Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:42:37.857Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:42:57.754Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:43:17.757Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:43:37.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:43:57.770Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:45:09.051Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:46:09.150Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:46:28.979Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:46:48.987Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:47:08.967Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:47:28.985Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:48:40.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:49:40.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:50:00.133Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:50:20.150Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:50:40.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:51:00.161Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:52:11.185Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:53:11.199Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:53:31.185Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:53:51.232Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:54:11.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:54:31.197Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:55:42.355Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:56:42.429Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:57:02.362Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:57:22.359Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:57:42.397Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:58:02.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T06:59:13.447Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1820874,"external":5183651,"heapTotal":62771200,"heapUsed":53624392,"rss":227065856},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T07:00:00.714Z","totalSessions":2,"uptime":152503.519419442}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:00:13.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:00:33.442Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:00:53.450Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:01:13.503Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:01:33.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:02:45.281Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:03:45.278Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:04:05.281Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:04:25.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:04:45.293Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:05:05.292Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:06:16.514Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:07:16.542Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:07:36.508Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:07:56.531Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:08:16.504Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:08:36.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:09:47.697Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:10:47.604Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:11:07.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:11:27.644Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:11:47.601Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:12:07.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:13:18.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:14:18.866Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:14:38.848Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:14:58.864Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:15:18.869Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:15:38.853Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:16:50.256Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:17:50.242Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:18:10.216Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:18:30.243Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:18:50.223Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:19:10.228Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:20:21.085Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:21:21.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:21:41.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:22:01.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:22:21.107Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:22:41.139Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:23:51.991Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:24:52.010Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:25:11.980Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:25:32.006Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:25:52.033Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:26:12.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:27:23.109Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:28:23.124Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:28:43.111Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:29:03.146Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:29:23.124Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:29:43.116Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:30:54.230Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:31:54.290Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:32:14.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:32:34.272Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:32:54.245Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:33:14.243Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:34:25.375Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:35:25.400Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:35:45.340Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:36:05.348Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:36:25.378Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:36:45.364Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:37:56.467Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:38:56.495Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:39:16.450Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:39:36.454Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:39:56.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:40:16.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:41:27.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:42:27.858Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:42:47.862Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:43:07.862Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:43:27.945Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:43:47.880Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:44:58.945Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:45:58.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:46:18.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:46:38.957Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:46:58.999Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:47:18.963Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:48:29.923Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:49:29.921Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:49:49.928Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:50:09.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:50:29.934Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:50:49.932Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:52:01.025Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:53:00.995Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:53:20.998Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:53:41.037Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:54:01.011Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:54:21.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:55:32.022Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:56:32.021Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:56:52.018Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:57:12.029Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:57:32.024Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:57:52.037Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T07:59:02.991Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1433773,"external":4796550,"heapTotal":60936192,"heapUsed":56037960,"rss":225091584},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T08:00:00.589Z","totalSessions":2,"uptime":156103.39465021}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:00:03.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:00:23.025Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:00:42.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:01:02.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:01:22.999Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:02:34.247Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:03:34.247Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:03:54.303Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:04:14.258Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:04:34.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:04:54.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:06:05.290Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:07:05.309Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:07:25.309Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:07:45.302Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:08:05.303Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:08:25.304Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:09:36.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:10:36.422Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:10:56.421Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:11:16.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:11:36.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:11:56.421Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:13:07.370Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:14:07.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:14:27.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:14:47.350Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:15:07.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:15:27.344Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:16:38.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:17:38.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:17:58.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:18:18.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:18:38.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:18:58.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:20:09.406Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:21:09.444Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:21:29.412Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:21:49.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:22:09.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:22:29.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:23:40.372Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:24:40.353Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:25:00.355Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:25:20.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:25:40.348Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:26:00.367Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:27:11.327Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:28:11.295Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:28:31.318Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:28:51.302Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:29:11.308Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:29:31.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:30:42.747Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:31:42.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:32:02.655Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:32:22.644Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:32:42.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:33:02.649Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:34:13.597Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:35:13.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:35:33.604Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:35:53.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:36:13.583Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:36:33.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:37:44.888Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:38:44.856Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:39:04.837Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:39:24.854Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:39:44.841Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:40:04.846Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:41:15.846Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:42:15.838Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:42:35.813Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:42:55.812Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:43:15.837Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:43:35.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:44:46.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:45:46.792Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:46:06.789Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:46:26.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:46:46.793Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:47:06.809Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:48:18.808Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:49:18.802Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:49:38.798Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:49:58.802Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:50:18.829Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:50:38.838Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:51:49.964Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:52:49.949Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:53:09.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:53:29.965Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:53:49.986Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:54:09.970Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:55:21.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:56:21.311Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:56:41.350Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:57:01.319Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:57:21.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:57:41.313Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:58:52.394Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T08:59:52.399Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1529462,"external":4892239,"heapTotal":62771200,"heapUsed":56959024,"rss":226283520},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T09:00:00.412Z","totalSessions":2,"uptime":159703.217835444}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:00:12.410Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:00:32.398Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:00:52.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:01:12.408Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:02:23.522Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:03:23.484Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:03:43.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:04:03.504Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:04:23.498Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:04:43.513Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:05:54.680Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:06:54.737Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:07:14.721Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:07:34.695Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:07:54.692Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:08:14.711Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:09:26.042Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:10:26.079Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:10:46.043Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:11:06.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:11:26.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:11:46.060Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:12:56.999Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:13:57.024Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:14:16.993Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:14:36.992Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:14:56.991Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:15:17.035Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:16:27.973Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:17:27.966Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:17:47.942Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:18:07.946Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:18:27.941Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:18:47.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:19:59.033Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:20:59.067Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:21:19.071Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:21:39.042Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:21:59.044Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:22:19.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:23:30.113Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:24:30.214Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:24:50.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:25:10.122Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:25:30.122Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:25:50.128Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:27:01.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:28:01.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:28:21.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:28:41.279Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:29:01.269Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:29:21.273Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:30:32.857Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:31:32.863Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:31:52.836Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:32:12.840Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:32:32.841Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:32:52.850Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:34:04.297Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:35:04.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:35:24.255Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:35:44.253Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:36:04.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:36:24.258Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:37:35.495Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:38:35.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:38:55.444Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:39:15.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:39:35.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:39:55.448Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:41:06.568Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:42:06.538Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:42:26.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:42:46.528Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:43:06.531Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:43:26.552Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:44:37.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:45:37.418Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:45:57.384Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:46:17.382Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:46:37.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:46:57.400Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:48:08.327Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:49:08.327Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:49:28.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:49:48.352Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:50:08.355Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:50:28.342Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:51:39.254Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:52:39.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:52:59.265Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:53:19.263Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:53:39.307Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:53:59.288Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:55:10.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:56:10.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:56:30.518Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:56:50.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:57:10.557Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:57:30.535Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:58:41.681Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T09:59:41.700Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":647446,"external":4010223,"heapTotal":59887616,"heapUsed":53943344,"rss":223674368},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T10:00:00.312Z","totalSessions":2,"uptime":163303.118167433}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:00:01.703Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:00:21.684Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:00:41.730Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:01:01.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:02:12.942Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:03:12.977Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:03:32.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:03:52.950Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:04:12.987Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:04:32.961Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:05:44.125Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:06:44.128Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:07:04.131Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:07:24.155Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:07:44.137Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:08:04.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:09:15.310Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:10:15.332Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:10:35.276Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:10:55.306Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:11:15.286Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:11:35.317Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:12:46.501Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:13:46.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:14:06.492Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:14:26.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:14:46.501Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:15:06.515Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:16:17.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:17:17.857Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:17:37.726Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:17:57.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:18:17.738Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:18:37.755Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:19:48.853Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:20:48.867Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:21:08.891Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:21:28.834Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:21:48.844Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:22:08.843Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:23:20.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:24:20.373Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:24:40.406Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:25:00.383Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:25:20.413Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:25:40.382Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:26:51.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:27:51.696Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:28:11.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:28:31.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:28:51.615Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:29:11.630Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:30:22.657Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:31:22.678Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:31:42.662Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:32:02.651Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:32:22.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:32:42.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:33:54.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:34:54.155Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:35:14.095Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:35:34.088Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:35:54.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:36:14.083Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:37:25.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:38:25.219Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:38:45.183Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:39:05.191Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:39:25.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:39:45.184Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:40:56.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:41:56.496Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:42:16.473Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:42:36.470Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:42:56.475Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:43:16.489Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:44:27.616Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:45:27.588Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:45:47.573Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:46:07.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:46:27.580Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:46:47.595Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:47:58.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:48:58.711Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:49:18.713Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:49:38.814Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:49:58.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:50:18.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:51:29.754Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:52:29.728Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:52:49.724Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:53:09.730Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:53:29.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:53:49.742Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:55:01.096Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:56:01.073Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:56:21.029Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:56:41.035Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:57:01.041Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:57:21.050Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:58:32.244Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:59:32.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T10:59:52.232Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":2285436,"external":5648213,"heapTotal":62050304,"heapUsed":57433696,"rss":225501184},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T11:00:00.327Z","totalSessions":2,"uptime":166903.133190898}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:00:12.240Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:00:32.281Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:00:52.272Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:02:03.111Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:03:03.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:03:23.118Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:03:43.135Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:04:03.145Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:04:23.110Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:05:34.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:06:34.429Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:06:54.332Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:07:14.333Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:07:34.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:07:54.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:09:05.704Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:10:05.577Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:10:25.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:10:45.577Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:11:05.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:11:25.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:12:37.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:13:37.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:13:57.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:14:17.590Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:14:37.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:14:57.638Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:16:08.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:17:08.485Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:17:28.470Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:17:48.481Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:18:08.485Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:18:28.474Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:19:39.486Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:20:39.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:20:59.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:21:19.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:21:39.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:21:59.515Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:23:10.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:24:10.588Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:24:30.598Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:24:50.580Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:25:10.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:25:30.570Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:26:41.864Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:27:41.961Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:28:01.871Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:28:21.871Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:28:41.884Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:29:01.893Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:30:13.256Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:31:13.298Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:31:33.217Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:31:53.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:32:13.212Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:32:33.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:33:44.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:34:44.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:35:04.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:35:24.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:35:44.597Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:36:04.595Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:37:16.116Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:38:16.187Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:38:36.085Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:38:56.077Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:39:16.082Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:39:36.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:40:47.257Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:41:47.228Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:42:07.221Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:42:27.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:42:47.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:43:07.234Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:44:18.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:45:18.554Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:45:38.534Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:45:58.536Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:46:18.523Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:46:38.536Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:47:50.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:48:49.864Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:49:09.808Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:49:29.811Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:49:49.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:50:09.828Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:51:20.913Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:52:20.919Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:52:40.908Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:53:00.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:53:20.888Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:53:40.897Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:54:52.051Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:55:52.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:56:12.042Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:56:32.041Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:56:52.040Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:57:12.081Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:58:23.355Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:59:23.354Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T11:59:43.360Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1064327,"external":4427104,"heapTotal":61984768,"heapUsed":54095024,"rss":226619392},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T12:00:00.392Z","totalSessions":2,"uptime":170503.197987795}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:00:03.366Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:00:23.361Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:00:43.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:01:54.984Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:02:55.012Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:03:14.852Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:03:34.847Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:03:54.842Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:04:14.908Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:05:25.871Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:06:25.918Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:06:45.876Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:07:05.880Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:07:25.874Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:07:45.904Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:08:57.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:09:56.997Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:10:17.000Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:10:37.007Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:10:57.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:11:17.058Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:12:28.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:13:28.184Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:13:48.187Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:14:08.223Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:14:28.258Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:14:48.236Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:15:59.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:16:59.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:17:19.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:17:39.573Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:17:59.604Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:18:19.574Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:19:30.951Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:20:30.969Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:20:50.862Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:21:10.864Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:21:30.884Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:21:50.885Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:23:02.047Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:24:02.119Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:24:22.057Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:24:42.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:25:02.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:25:22.061Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:26:33.272Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:27:33.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:27:53.279Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:28:13.278Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:28:33.280Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:28:53.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:30:04.505Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:31:04.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:31:24.461Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:31:44.451Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:32:04.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:32:24.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:33:35.425Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:34:35.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:34:55.390Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:35:15.385Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:35:35.359Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:35:55.367Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:37:06.303Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:38:06.322Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:38:26.331Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:38:46.300Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:39:06.299Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:39:26.306Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:40:37.394Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:41:37.425Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:41:57.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:42:17.415Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:42:37.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:42:57.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:44:08.771Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:45:08.875Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:45:28.714Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:45:48.725Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:46:08.718Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:46:28.723Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:47:39.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:48:39.578Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:48:59.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:49:19.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:49:39.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:49:59.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:51:10.505Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:52:10.520Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:52:30.485Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:52:50.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:53:10.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:53:30.488Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:54:41.774Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:55:41.839Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:56:01.732Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:56:21.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:56:41.737Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:57:01.743Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:58:13.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:59:13.037Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:59:33.007Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T12:59:53.025Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1851226,"external":5214003,"heapTotal":63819776,"heapUsed":54427240,"rss":228548608},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T13:00:00.479Z","totalSessions":2,"uptime":174103.28439642}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:00:13.017Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:00:33.018Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:01:44.154Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:02:44.248Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:03:04.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:03:24.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:03:44.126Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:04:04.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:05:15.010Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:06:14.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:06:34.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:06:55.000Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:07:15.022Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:07:35.002Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:08:46.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:09:46.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:10:06.218Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:10:26.216Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:10:46.230Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:11:06.234Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:12:17.702Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:13:17.760Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:13:37.696Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:13:57.707Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:14:17.701Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:14:37.739Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:15:48.914Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:16:48.968Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:17:08.923Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:17:28.937Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:17:48.939Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:18:08.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:19:19.878Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:20:19.929Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:20:39.879Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:20:59.887Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:21:19.878Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:21:39.924Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:22:51.270Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:23:51.275Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:24:11.178Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:24:31.180Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:24:51.183Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:25:11.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:26:22.450Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:27:22.418Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:27:42.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:28:02.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:28:22.445Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:28:42.444Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:29:53.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:30:53.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:31:13.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:31:33.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:31:53.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:32:13.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:33:24.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:34:24.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:34:44.515Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:35:04.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:35:24.526Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:35:44.533Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:36:55.783Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:37:55.902Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:38:15.812Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:38:35.798Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:38:55.823Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T13:39:15.818Z"}
