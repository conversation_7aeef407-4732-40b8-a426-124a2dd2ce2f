{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1113131,"external":4475908,"heapTotal":63557632,"heapUsed":57131832,"rss":225595392},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T00:00:00.737Z","totalSessions":2,"uptime":127303.542814591}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:00:25.211Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:01:25.144Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:01:45.154Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:02:05.158Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:02:25.163Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:02:45.165Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:03:56.480Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:04:56.478Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:05:16.440Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:05:36.445Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:05:56.439Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:06:16.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:07:27.632Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:08:27.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:08:47.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:09:07.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:09:27.561Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:09:47.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:10:58.500Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:11:58.532Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:12:18.495Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:12:38.482Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:12:58.481Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:13:18.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:14:29.829Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:15:29.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:15:49.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:16:09.726Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:16:29.727Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:16:49.735Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:18:01.233Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:19:01.231Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:19:21.217Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:19:41.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:20:01.222Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:20:21.282Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:21:32.717Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:22:32.709Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:22:52.761Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:23:12.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:23:32.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:23:52.768Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:25:03.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:26:03.910Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:26:23.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:26:43.901Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:27:03.951Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:27:23.927Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:28:34.973Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:29:34.974Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:29:54.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:30:14.972Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:30:34.974Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:30:55.003Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:32:06.764Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:33:06.799Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:33:26.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:33:46.762Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:34:06.763Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:34:26.798Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:35:37.938Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:36:37.933Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:36:57.939Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:37:17.941Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:37:37.943Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:37:57.978Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:39:08.916Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:40:08.955Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:40:28.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:40:48.897Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:41:08.901Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:41:28.942Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:42:40.116Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:43:40.318Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:44:00.123Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:44:20.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:44:40.122Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:45:00.162Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:46:11.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:47:11.645Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:47:31.572Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:47:51.591Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:48:11.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:48:31.656Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:49:43.104Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:50:43.076Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:51:03.009Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:51:23.026Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:51:43.010Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:52:03.072Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:53:14.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:54:14.422Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:54:34.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:54:54.413Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:55:14.424Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:55:34.456Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:56:45.637Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:57:45.609Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:58:05.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:58:25.619Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:58:45.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T00:59:05.651Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":972779,"external":4335556,"heapTotal":61722624,"heapUsed":49746416,"rss":223580160},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T01:00:00.110Z","totalSessions":2,"uptime":130902.915488917}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:00:16.715Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:01:16.741Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:01:36.708Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:01:56.711Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:02:16.716Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:02:36.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:03:47.779Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:04:47.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:05:07.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:05:27.821Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:05:47.792Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:06:07.823Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:07:19.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:08:19.100Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:08:39.019Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:08:59.004Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:09:19.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:09:39.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:10:50.546Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:11:50.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:12:10.432Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:12:30.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:12:50.431Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:13:10.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:14:21.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:15:21.515Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:15:41.410Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:16:01.419Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:16:21.428Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:16:41.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:17:52.491Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:18:52.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:19:12.476Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:19:32.517Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:19:52.489Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:20:12.531Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:21:23.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:22:23.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:22:43.603Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:23:03.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:23:23.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:23:43.644Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:24:54.733Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:25:54.618Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:26:14.617Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:26:34.630Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:26:54.627Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:27:14.650Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:28:25.605Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:29:25.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:29:45.560Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:30:05.585Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:30:25.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:30:45.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:31:56.773Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:32:56.881Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:33:16.776Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:33:36.784Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:33:56.801Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:34:16.789Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:35:27.870Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:36:27.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:36:47.873Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:37:07.877Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:37:27.918Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:37:47.886Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:38:59.188Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:39:59.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:40:19.191Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:40:39.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:40:59.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:41:19.211Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:42:30.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:43:30.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:43:50.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:44:10.231Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:44:30.204Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:44:50.230Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:46:01.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:47:01.243Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:47:21.241Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:47:41.277Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:48:01.263Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:48:21.290Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:49:32.334Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:50:32.371Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:50:52.319Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:51:12.310Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:51:32.317Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:51:52.399Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:53:03.267Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:54:03.304Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:54:23.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:54:43.276Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:55:03.284Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:55:23.344Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:56:34.282Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:57:34.253Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:57:54.248Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:58:14.257Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:58:34.256Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T01:58:54.291Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":987878,"external":4350655,"heapTotal":57528320,"heapUsed":49760216,"rss":223051776},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T02:00:00.206Z","totalSessions":2,"uptime":134503.024757419}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:00:05.371Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:01:05.428Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:01:25.358Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:01:45.366Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:02:05.377Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:02:25.430Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:03:36.236Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:04:36.254Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:04:56.236Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:05:16.224Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:05:36.260Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:05:56.244Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:07:07.635Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:08:07.583Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:08:27.582Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:08:47.588Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:09:07.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:09:27.593Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:10:38.880Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:11:38.885Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:11:58.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:12:18.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:12:38.892Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:12:58.897Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:14:10.172Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:15:10.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:15:30.186Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:15:50.226Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:16:10.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:16:30.198Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:17:41.289Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:18:41.352Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:19:01.296Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:19:21.332Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:19:41.299Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:20:01.303Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:21:12.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:22:12.541Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:22:32.658Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:22:52.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:23:12.553Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:23:32.549Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:24:44.212Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:25:44.197Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:26:04.189Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:26:24.185Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:26:44.194Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:27:04.190Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:28:15.503Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:29:15.409Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:29:35.379Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:29:55.365Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:30:15.374Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:30:35.389Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:31:46.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:32:46.414Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:33:06.417Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:33:26.507Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:33:46.436Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:34:06.435Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:35:18.151Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:36:17.952Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:36:37.915Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:36:57.896Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:37:17.889Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:37:37.903Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:38:49.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:39:49.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:40:09.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:40:29.053Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:40:49.059Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:41:09.063Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:42:20.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:43:20.324Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:43:40.126Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:44:00.121Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:44:20.151Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:44:40.127Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:45:51.384Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:46:51.329Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:47:11.347Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:47:31.335Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:47:51.336Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:48:11.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:49:22.281Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:50:22.291Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:50:42.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:51:02.246Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:51:22.240Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:51:42.251Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:52:53.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:53:53.155Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:54:13.170Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:54:33.157Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:54:53.196Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:55:13.158Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:56:24.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:57:24.146Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:57:44.136Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:58:04.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:58:24.142Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:58:44.145Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T02:59:55.344Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":966240,"external":4329017,"heapTotal":61984768,"heapUsed":49314168,"rss":227581952},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T03:00:00.428Z","totalSessions":2,"uptime":138103.233799682}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:00:55.292Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:01:15.307Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:01:35.291Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:01:55.351Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:02:15.301Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:03:26.354Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:04:26.300Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:04:46.305Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:05:06.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:05:26.345Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:05:46.326Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:06:57.488Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:07:57.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:08:17.360Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:08:37.367Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:08:57.363Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:09:17.396Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:10:28.591Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:11:28.586Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:11:48.565Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:12:08.587Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:12:28.569Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:12:48.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:13:59.626Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:14:59.687Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:15:19.599Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:15:39.607Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:15:59.639Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:16:19.613Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:17:31.091Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:18:31.089Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:18:51.095Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:19:11.092Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:19:31.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:19:51.101Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:21:02.235Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:22:02.327Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:22:22.252Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:22:42.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:23:02.261Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:23:22.339Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:24:33.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:25:33.285Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:25:53.283Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:26:13.315Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:26:33.297Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:26:53.297Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:28:04.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:29:04.583Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:29:24.584Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:29:44.625Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:30:04.674Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:30:24.597Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:31:36.174Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:32:36.156Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:32:56.216Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:33:16.249Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:33:36.150Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:33:56.167Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:35:07.202Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:36:07.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:36:27.168Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:36:47.189Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:37:07.176Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:37:27.182Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:38:38.078Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:39:38.090Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:39:58.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:40:18.113Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:40:38.124Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:40:58.102Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:42:09.353Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:43:09.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:43:29.322Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:43:49.356Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:44:09.346Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:44:29.355Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:45:40.576Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:46:40.573Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:47:00.576Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:47:20.579Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:47:40.581Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:48:00.590Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:49:11.553Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:50:11.562Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:50:31.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:50:51.602Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:51:11.589Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:51:31.568Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:52:43.448Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:53:43.464Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:54:03.459Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:54:23.453Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:54:43.460Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:55:03.465Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:56:14.494Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:57:14.556Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:57:34.516Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:57:54.498Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:58:14.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:58:34.499Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T03:59:45.764Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1912619,"external":5275396,"heapTotal":63295488,"heapUsed":57180392,"rss":228040704},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T04:00:00.495Z","totalSessions":2,"uptime":141703.301083103}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:00:45.677Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:01:05.663Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:01:25.654Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:01:45.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:02:05.666Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:03:17.132Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:04:17.067Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:04:37.052Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:04:57.065Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:05:17.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:05:37.060Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:06:48.158Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:07:48.190Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:08:08.163Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:08:28.164Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:08:48.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:09:08.208Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:10:19.416Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:11:19.472Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:11:39.386Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:11:59.395Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:12:19.391Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:12:39.403Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:13:50.573Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:14:50.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:15:10.559Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:15:30.567Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:15:50.578Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:16:10.544Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:17:21.800Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:18:21.900Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:18:41.817Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:19:01.808Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:19:21.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:19:41.823Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:20:53.025Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:21:53.038Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:22:13.028Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:22:33.026Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:22:53.029Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:23:13.035Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:24:24.407Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:25:24.411Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:25:44.398Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:26:04.404Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:26:24.415Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:26:44.518Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:27:55.648Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:28:55.654Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:29:15.596Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:29:35.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:29:55.594Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:30:15.611Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:31:26.512Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:32:26.529Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:32:46.493Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:33:06.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:33:26.497Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:33:46.502Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:34:57.647Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:35:57.652Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:36:17.633Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:36:37.664Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:36:57.682Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:37:17.642Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:38:29.008Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:39:28.983Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:39:48.994Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:40:09.041Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:40:28.993Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:40:48.993Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:42:00.353Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:43:00.311Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:43:20.297Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:43:40.288Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:44:00.290Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:44:20.338Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:45:31.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:46:31.206Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:46:51.218Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:47:11.201Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:47:31.205Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:47:51.203Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:49:02.457Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:50:02.533Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:50:22.463Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:50:42.506Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:51:02.513Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:51:22.482Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:52:33.555Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:53:33.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:53:53.547Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:54:13.630Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:54:33.600Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:54:53.560Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:56:04.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:57:04.667Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:57:24.605Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:57:44.673Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:58:04.614Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:58:24.612Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T04:59:35.826Z"}
{"activeQRCodes":1,"level":"info","maxSessions":5,"memoryUsage":{"arrayBuffers":1638166,"external":5000943,"heapTotal":61722624,"heapUsed":55742056,"rss":227008512},"message":"Session statistics","sessionStatuses":{"8487921219":"connected","test_new":"qr_ready"},"timestamp":"2025-06-20T05:00:00.472Z","totalSessions":2,"uptime":145303.277989319}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:00:35.813Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:00:55.819Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:01:15.830Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:01:35.841Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:01:55.828Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:03:06.832Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:04:06.878Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:04:26.873Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:04:46.843Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:05:06.849Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:05:26.864Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:06:38.056Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:07:38.086Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:07:58.024Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:08:18.018Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:08:38.030Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:08:58.021Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:10:09.775Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:11:09.746Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:11:29.712Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:11:49.720Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:12:09.717Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:12:29.734Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:13:40.981Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:14:40.958Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:15:00.928Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:15:20.950Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:15:40.940Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:16:00.947Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:17:12.275Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:18:12.244Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:18:32.219Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:18:52.270Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:19:12.232Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:19:32.266Z"}
{"level":"info","message":"QR code generated","sessionId":"test_new","timestamp":"2025-06-20T05:20:43.299Z"}
