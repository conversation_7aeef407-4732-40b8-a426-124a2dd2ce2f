nohup: ignoring input
🚀 Starting Enhanced WhatsApp Service...
📋 Configuration:
   - Port: 3001
   - Max Sessions: 5
   - Save Media: true
   - Log Level: info

🔧 Enhanced Tool Manager initialized with per-number configuration
🧠 Context Engine initialized
📚 Knowledge Base Manager initialized
🎤 Voice Processor initialized with multiple TTS providers
⚙️ Response Configuration Manager initialized
🔧 Odoo Configuration initialized
🤖 Initializing WhatsApp Chatbot...
🔧 Tool Manager set for chatbot
🤖 Chatbot initialized successfully
🚀 Initializing Enhanced WhatsApp Service...
🚀 Enhanced WhatsApp Service started successfully
📡 Server running on port 3001
📁 Sessions directory: /mnt/whatsapp/sessions
📝 Logs directory: /mnt/whatsapp/logs
📱 Max sessions: 5
🔗 Health check: http://localhost:3001/health
📚 Dashboard: http://localhost:3001/

🎯 Ready for WhatsApp QR authentication!
1. Open the dashboard in your browser
2. Create a new WhatsApp instance
3. Scan the QR code with your phone
4. Start sending messages!
✅ Database connected successfully
🔊 eSpeak TTS: ✅ Available
📞 Contact Manager directories initialized
📁 Chatbot directories created
📅 Loaded scheduled tasks
✅ Database tables created/verified successfully
📁 Directories created
📝 Logging system configured
🔧 Express middleware configured
🛣️  Enhanced API routes configured for n8n integration
2025-06-18 05:23:18 [[32minfo[39m] ✅ Cleanup tasks scheduled 
🔄 Loading existing WhatsApp instances...
📚 Loaded global knowledge base from 2 files
🔊 Festival TTS: ✅ Available
📱 Found 3 existing instances, restoring...
🔄 Restoring instance: test_reconnect (919999999996)
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
📚 Loaded instance-specific knowledge bases for 3 instances
✅ Chatbot initialized successfully
✅ Instance test_reconnect restoration initiated
🔄 Restoring instance: test_new (919999999997)
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
✅ Instance test_new restoration initiated
🔄 Restoring instance: ********** (91**********)
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
✅ Instance ********** restoration initiated
✅ Instance restoration completed
✅ Service initialized successfully
2025-06-18 05:23:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:23:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
🔊 Edge TTS: ❌ Not installed
🔊 gTTS: ✅ Available
🔑 OpenAI TTS: ❌ Not configured
🔑 ElevenLabs TTS: ❌ Not configured
🔑 Google Cloud TTS: ❌ Not configured
🔑 Azure TTS: ❌ Not configured
2025-06-18 05:23:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:38 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:23:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:23:39 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:23:40 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:40 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:50 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:50 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:24:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:24:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:38 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:24:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:24:38 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:24:40 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:40 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:50 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:50 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:58 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:24:58 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:24:58 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:25:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:18 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:25:18 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:25:18 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:25:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:25:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:25:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:38 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:25:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:25:38 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:25:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:58 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:25:58 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:25:58 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:26:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:11 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:16 [[32minfo[39m] POST /auth/login {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0","body":{"mobileNumber":"919999999999","password":"admin123"}}
2025-06-18 05:26:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:26:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:26:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:40 [[32minfo[39m] POST /instances {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0","body":{"sessionId":"test_fixed","phoneNumber":"919999999995"}}
2025-06-18 05:26:40 [[32minfo[39m] Creating WhatsApp instance: test_fixed 
2025-06-18 05:26:40 [[32minfo[39m] Instance test_fixed saved to database for user 1 
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
2025-06-18 05:26:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:49 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:26:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:09 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:27:09 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:27:10 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:27:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:11 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:14 [[32minfo[39m] GET /instances/test_fixed/qr {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0"}
2025-06-18 05:27:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:27:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:27:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:49 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:27:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:09 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:28:09 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:28:09 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:28:10 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:28:11 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:14 [[32minfo[39m] POST /auth/logout {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{}}
2025-06-18 05:28:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:28:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:28:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:22 [[32minfo[39m] POST /auth/login {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{"mobileNumber":"919999999999","password":"admin123"}}
2025-06-18 05:28:23 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:29 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:28:29 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:28:29 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:28:30 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:28:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:49 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:28:49 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:28:49 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:28:50 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:28:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:09 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:29:09 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:29:09 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:29:10 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:29:10 [[32minfo[39m] DELETE /instances/********** {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: **********
Client logged out for session: **********
Client destroyed for session: **********
✅ Instance ********** deleted from database
✅ Session files deleted for **********
✅ Instance ********** completely removed
2025-06-18 05:29:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:12 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:12 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:29:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:29:21 [[32minfo[39m] DELETE /instances/test_fixed {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: test_fixed
Client logged out for session: test_fixed
Client destroyed for session: test_fixed
✅ Instance test_fixed deleted from database
✅ Session files deleted for test_fixed
✅ Instance test_fixed completely removed
2025-06-18 05:29:22 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:22 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:29 [[32minfo[39m] DELETE /instances/test_fixed {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: test_fixed
No active client found for session: test_fixed
✅ Instance test_fixed deleted from database
✅ Instance test_fixed completely removed
2025-06-18 05:29:29 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:29:29 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:29:32 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:38 [[32minfo[39m] DELETE /instances/test_reconnect {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: test_reconnect
Client logged out for session: test_reconnect
Client destroyed for session: test_reconnect
✅ Instance test_reconnect deleted from database
✅ Session files deleted for test_reconnect
✅ Instance test_reconnect completely removed
2025-06-18 05:29:42 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:52 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:52 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:57 [[32minfo[39m] POST /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{"sessionId":"**********","phoneNumber":"91**********"}}
2025-06-18 05:29:57 [[32minfo[39m] Creating WhatsApp instance: ********** 
2025-06-18 05:29:57 [[32minfo[39m] Instance ********** saved to database for user 1 
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
2025-06-18 05:30:02 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:02 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:04 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:30:05 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:12 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:12 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:30:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:30:22 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:22 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:32 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:40 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:30:43 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:43 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:53 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:53 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:03 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:03 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:04 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:31:13 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:13 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:31:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:31:23 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:23 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:24 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:31:27 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:33 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:40 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:31:44 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:44 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:44 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:31:55 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:31:55 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:00 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:32:04 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:32:05 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:05 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:32:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:32:20 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:32:24 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:32:26 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:26 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:36 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:36 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:40 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:32:46 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:46 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:56 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:32:56 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:00 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:33:06 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:06 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:33:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:33:27 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:27 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:35 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:33:37 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:37 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:47 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:47 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:57 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:33:57 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:07 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:07 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:11 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:34:17 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:17 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:34:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:34:27 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:27 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:35 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:34:37 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:37 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:47 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:47 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:55 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:34:57 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:34:57 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:08 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:08 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:11 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:35:15 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:35:18 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:18 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:35:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:35:29 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:29 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:31 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:35:35 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:35:39 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:39 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:49 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:49 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:35:51 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:35:55 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:36:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:11 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:36:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:36:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:36:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:31 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:36:40 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:40 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:48 [[32minfo[39m] POST /auth/login {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0","body":{"mobileNumber":"919999999999","password":"admin123"}}
2025-06-18 05:36:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:36:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:06 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:37:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:11 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:14 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0"}
2025-06-18 05:37:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:37:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:37:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:42 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:43 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:37:52 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:37:52 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:02 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:02 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:06 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:38:13 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:13 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:38:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:38:23 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:23 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:26 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:38:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:33 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:43 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:38:43 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:43 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:46 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:38:53 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:38:53 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:03 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:39:04 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:04 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:06 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:39:14 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:14 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:39:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:39:23 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:39:24 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:24 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:26 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:39:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:34 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:34 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:43 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:39:45 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:45 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:55 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:39:55 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:03 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:40:05 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:05 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:40:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:40:26 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:26 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:37 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:37 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:38 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:40:47 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:47 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:57 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:40:57 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:08 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:08 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:14 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:41:18 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:18 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:41:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:41:28 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:28 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:38 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:41:38 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:38 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:49 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:49 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:58 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:41:59 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:41:59 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:09 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:09 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:14 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:42:18 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:42:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:42:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:42:29 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:29 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:34 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:42:38 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:42:40 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:40 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:50 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:50 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:42:54 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:42:58 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:43:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:14 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:43:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:43:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:43:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:34 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:43:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:43:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:02 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:02 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:09 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:44:12 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:12 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:44:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:44:23 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:23 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:33 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:43 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:43 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:45 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:44:53 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:44:53 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:04 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:04 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:09 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:45:14 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:14 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:45:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:45:25 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:25 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:29 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:45:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:35 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:45 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:45:45 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:45 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:49 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:45:55 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:45:55 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:05 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:46:06 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:06 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:09 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:46:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:46:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:46:25 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:46:26 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:26 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:29 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:46:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:36 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:36 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:45 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:46:46 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:46 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:56 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:46:56 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:05 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:47:06 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:06 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:47:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:47:26 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:26 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:36 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:36 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:40 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:47:46 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:46 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:56 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:47:56 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:07 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:07 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:16 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:48:17 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:17 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:48:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:48:27 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:27 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:37 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:37 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:40 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:48:47 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:47 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:58 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:48:58 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:00 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:49:08 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:08 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:16 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:49:18 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:18 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:49:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:49:20 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:49:28 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:28 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:36 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:49:38 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:38 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:40 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:49:48 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:48 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:56 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:49:58 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:49:58 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:00 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:50:09 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:09 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:16 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:50:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:50:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:50:29 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:29 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:36 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:50:39 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:39 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:49 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:49 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:59 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:50:59 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:09 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:09 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:11 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:51:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:51:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:51:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:40 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:40 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:47 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:51:50 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:51:50 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:11 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:52:12 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:12 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:52:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:52:22 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:22 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:31 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:52:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:32 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:42 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:47 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:52:51 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:52:52 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:52:52 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:02 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:02 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:07 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:53:11 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:53:12 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:12 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:53:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:53:22 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:22 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:27 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:53:31 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:53:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:32 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:42 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:47 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:53:53 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:53:53 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:04 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:04 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:07 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:54:14 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:14 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:54:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:54:24 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:24 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:34 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:34 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:42 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:54:44 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:44 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:55 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:54:55 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:05 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:05 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:15 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:15 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:18 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:55:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:55:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:55:25 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:25 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:35 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:42 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:55:46 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:46 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:56 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:55:56 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:02 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:56:06 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:06 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:18 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:56:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:56:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:56:22 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:56:26 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:26 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:36 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:36 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:56:42 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:56:46 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:46 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:56 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:56 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:56:58 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:57:02 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:57:06 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:06 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:18 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:57:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:57:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:57:26 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:26 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:36 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:36 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:57:46 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:46 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:56 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:57:56 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:07 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:07 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:14 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:58:17 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:17 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:58:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:58:27 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:27 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:37 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:37 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:47 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:47 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:50 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:58:57 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:58:57 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:07 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:07 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:14 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:59:17 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:17 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:59:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:59:27 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:27 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:34 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:59:37 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:37 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:48 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:48 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:50 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:59:54 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:59:58 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:59:58 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:00 [[32minfo[39m] Session stats - Active: 2/5, QR Codes: 2 
2025-06-18 06:00:08 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:08 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:10 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:00:14 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:00:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:00:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:00:29 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:29 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:30 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:00:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:34 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:00:39 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:39 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:50 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:00:50 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:00:50 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:10 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:01:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:01:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:01:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:45 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:01:52 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:01:52 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:02 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:02 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:13 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:13 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:02:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:02:21 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:02:23 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:23 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:33 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:43 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:43 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:45 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:02:53 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:02:53 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:04 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:04 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:05 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:03:14 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:14 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:03:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:03:21 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:03:24 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:24 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:25 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:03:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:34 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:34 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:41 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:03:44 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:44 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:45 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:03:54 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:03:54 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:01 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:04:05 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:05 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:05 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:04:15 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:15 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:04:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:04:21 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:04:25 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:25 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:35 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:41 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:04:45 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:45 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:55 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:04:55 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:05 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:05 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:15 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:15 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:16 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:05:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:05:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:05:25 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:25 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:35 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:45 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:45 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:52 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:05:56 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:05:56 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:06 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:06 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:16 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:06:16 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:16 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:17 [[32minfo[39m] POST /auth/login {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{"mobileNumber":"919999999999","password":"admin123"}}
2025-06-18 06:06:18 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:06:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:06:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:25 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:27 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:27 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:36 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:36 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:06:37 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:37 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:47 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:47 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:52 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:06:56 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:06:57 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:06:57 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:07 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:07 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:12 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:07:15 [[32minfo[39m] POST /auth/logout {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{}}
2025-06-18 06:07:16 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:07:17 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:17 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:07:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:07:27 [[32minfo[39m] POST /auth/login {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{"mobileNumber":"919999999999","password":"admin123"}}
2025-06-18 06:07:28 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:28 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:32 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:07:36 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:07:38 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:38 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:44 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:44 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:47 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:48 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:48 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:50 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:52 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:07:53 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:57 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:58 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:07:58 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:00 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:03 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:06 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:08 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:08:08 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:09 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:09 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:12 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:12 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 06:08:15 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:15 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:08:18 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:08:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 06:08:21 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:24 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:27 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:29 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:29 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:30 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:33 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:36 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:39 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:39 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:39 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:42 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:45 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:48 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:49 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:49 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:51 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:54 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:08:57 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:00 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:04 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:06 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:10 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:13 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 06:09:15 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 06:09:16 [[32minfo[39m] GET /instances/**********/qr {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
