nohup: ignoring input
🚀 Starting Enhanced WhatsApp Service...
📋 Configuration:
   - Port: 3001
   - Max Sessions: 5
   - Save Media: true
   - Log Level: info

🔧 Enhanced Tool Manager initialized with per-number configuration
🧠 Context Engine initialized
📚 Knowledge Base Manager initialized
🎤 Voice Processor initialized with multiple TTS providers
⚙️ Response Configuration Manager initialized
🔧 Odoo Configuration initialized
🤖 Initializing WhatsApp Chatbot...
🔧 Tool Manager set for chatbot
🤖 Chatbot initialized successfully
🚀 Initializing Enhanced WhatsApp Service...
🚀 Enhanced WhatsApp Service started successfully
📡 Server running on port 3001
📁 Sessions directory: /mnt/whatsapp/sessions
📝 Logs directory: /mnt/whatsapp/logs
📱 Max sessions: 5
🔗 Health check: http://localhost:3001/health
📚 Dashboard: http://localhost:3001/

🎯 Ready for WhatsApp QR authentication!
1. Open the dashboard in your browser
2. Create a new WhatsApp instance
3. Scan the QR code with your phone
4. Start sending messages!
✅ Database connected successfully
🔊 eSpeak TTS: ✅ Available
📞 Contact Manager directories initialized
📁 Chatbot directories created
📅 Loaded scheduled tasks
✅ Database tables created/verified successfully
📁 Directories created
📝 Logging system configured
🔧 Express middleware configured
🛣️  Enhanced API routes configured for n8n integration
2025-06-18 05:23:18 [[32minfo[39m] ✅ Cleanup tasks scheduled 
🔄 Loading existing WhatsApp instances...
📚 Loaded global knowledge base from 2 files
🔊 Festival TTS: ✅ Available
📱 Found 3 existing instances, restoring...
🔄 Restoring instance: test_reconnect (919999999996)
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
📚 Loaded instance-specific knowledge bases for 3 instances
✅ Chatbot initialized successfully
✅ Instance test_reconnect restoration initiated
🔄 Restoring instance: test_new (919999999997)
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
✅ Instance test_new restoration initiated
🔄 Restoring instance: ********** (91**********)
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
✅ Instance ********** restoration initiated
✅ Instance restoration completed
✅ Service initialized successfully
2025-06-18 05:23:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:23:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
🔊 Edge TTS: ❌ Not installed
🔊 gTTS: ✅ Available
🔑 OpenAI TTS: ❌ Not configured
🔑 ElevenLabs TTS: ❌ Not configured
🔑 Google Cloud TTS: ❌ Not configured
🔑 Azure TTS: ❌ Not configured
2025-06-18 05:23:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:38 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:23:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:23:39 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:23:40 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:40 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:50 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:23:50 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:24:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:24:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:38 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:24:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:24:38 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:24:40 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:40 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:50 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:50 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:24:58 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:24:58 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:24:58 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:25:00 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:00 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:10 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:10 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:18 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:25:18 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:25:18 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:25:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:25:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:25:20 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:20 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:30 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:30 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:38 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:25:38 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:25:38 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:25:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:25:58 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:25:58 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:25:58 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:26:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:11 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:16 [[32minfo[39m] POST /auth/login {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0","body":{"mobileNumber":"919999999999","password":"admin123"}}
2025-06-18 05:26:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:26:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:26:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:40 [[32minfo[39m] POST /instances {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0","body":{"sessionId":"test_fixed","phoneNumber":"919999999995"}}
2025-06-18 05:26:40 [[32minfo[39m] Creating WhatsApp instance: test_fixed 
2025-06-18 05:26:40 [[32minfo[39m] Instance test_fixed saved to database for user 1 
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
2025-06-18 05:26:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:49 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:26:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:26:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:09 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:27:09 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:27:10 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:27:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:11 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:14 [[32minfo[39m] GET /instances/test_fixed/qr {"ip":"::ffff:127.0.0.1","userAgent":"curl/7.81.0"}
2025-06-18 05:27:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:27:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:27:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:49 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:27:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:27:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:09 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:28:09 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:28:09 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:28:10 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:28:11 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:14 [[32minfo[39m] POST /auth/logout {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{}}
2025-06-18 05:28:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:28:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:28:21 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:21 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:22 [[32minfo[39m] POST /auth/login {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{"mobileNumber":"919999999999","password":"admin123"}}
2025-06-18 05:28:23 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:29 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:28:29 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:28:29 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:28:30 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:28:31 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:31 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:41 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:41 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:49 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:28:49 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:28:49 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:28:50 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:28:51 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:28:51 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:01 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:01 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:09 [[32minfo[39m] QR Code generated for session: test_fixed 
2025-06-18 05:29:09 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:29:09 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:29:10 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:29:10 [[32minfo[39m] DELETE /instances/********** {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: **********
Client logged out for session: **********
Client destroyed for session: **********
✅ Instance ********** deleted from database
✅ Session files deleted for **********
✅ Instance ********** completely removed
2025-06-18 05:29:11 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:12 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:12 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:29:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:29:21 [[32minfo[39m] DELETE /instances/test_fixed {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: test_fixed
Client logged out for session: test_fixed
Client destroyed for session: test_fixed
✅ Instance test_fixed deleted from database
✅ Session files deleted for test_fixed
✅ Instance test_fixed completely removed
2025-06-18 05:29:22 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:22 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:29 [[32minfo[39m] DELETE /instances/test_fixed {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: test_fixed
No active client found for session: test_fixed
✅ Instance test_fixed deleted from database
✅ Instance test_fixed completely removed
2025-06-18 05:29:29 [[32minfo[39m] QR Code generated for session: test_reconnect 
2025-06-18 05:29:29 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:29:32 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:33 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:38 [[32minfo[39m] DELETE /instances/test_reconnect {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
Disconnecting WhatsApp instance: test_reconnect
Client logged out for session: test_reconnect
Client destroyed for session: test_reconnect
✅ Instance test_reconnect deleted from database
✅ Session files deleted for test_reconnect
✅ Instance test_reconnect completely removed
2025-06-18 05:29:42 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:42 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:52 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:52 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:29:57 [[32minfo[39m] POST /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0","body":{"sessionId":"**********","phoneNumber":"91**********"}}
2025-06-18 05:29:57 [[32minfo[39m] Creating WhatsApp instance: ********** 
2025-06-18 05:29:57 [[32minfo[39m] Instance ********** saved to database for user 1 
🔍 Searching for Chrome/Chromium executable...
❌ Not found: /usr/bin/google-chrome
❌ Not found: /usr/bin/google-chrome-stable
❌ Not found: /usr/bin/google-chrome
✅ Found Chrome at: /usr/bin/chromium-browser
2025-06-18 05:30:02 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:02 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:04 [[32minfo[39m] QR Code generated for session: ********** 
2025-06-18 05:30:05 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:12 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:12 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:19 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:30:19 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
2025-06-18 05:30:22 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:22 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:32 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:32 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:35 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:40 [[32minfo[39m] QR Code generated for session: test_new 
2025-06-18 05:30:43 [[32minfo[39m] GET /health {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
2025-06-18 05:30:43 [[32minfo[39m] GET /instances {"ip":"::ffff:**************","userAgent":"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:139.0) Gecko/20100101 Firefox/139.0"}
