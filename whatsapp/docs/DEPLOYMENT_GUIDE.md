# 🚀 WhatsApp Business Automation Platform - Deployment Guide

## 📋 Overview

This guide will help you deploy the enhanced WhatsApp Business Automation Platform with n8n integration capabilities.

## 🎯 What You Get

### ✅ **Core Features**
- **Multiple WhatsApp Instances**: Manage multiple WhatsApp Business accounts
- **AI Chatbot Integration**: Groq-powered AI with tool calling capabilities
- **Enhanced Tool Management**: Global tools + per-number tool configuration
- **Context Engine**: Individual conversation memory for each phone number
- **Voice Processing**: Transcription and TTS with multiple providers
- **PDF Generation**: HTML-to-PDF conversion with rich formatting
- **Media Handling**: Images, documents, voice messages, and more

### ✅ **n8n Integration Ready**
- **RESTful API**: Comprehensive endpoints for workflow automation
- **Webhook Support**: Single-request message sending with tool configuration
- **OpenAPI Documentation**: Complete API specification
- **Postman Collection**: Ready-to-use API testing collection

### ✅ **Enterprise Features**
- **Database Persistence**: SQLite with automatic backups
- **Authentication System**: Multi-user support with role-based access
- **Logging & Monitoring**: Comprehensive logging with rotation
- **Rate Limiting**: Intelligent rate limiting for security
- **Session Management**: Automatic session restoration

## 🛠️ Installation Requirements

### **System Requirements**
- **OS**: Ubuntu 20.04+ (recommended) or similar Linux distribution
- **RAM**: Minimum 2GB, recommended 4GB+
- **Storage**: Minimum 10GB free space
- **Node.js**: Version 18+ required
- **Chrome/Chromium**: For WhatsApp Web automation

### **Dependencies**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Chrome/Chromium
sudo apt install -y chromium-browser

# Install additional dependencies
sudo apt install -y git sqlite3 poppler-utils wkhtmltopdf

# Install TTS dependencies (optional)
sudo apt install -y espeak festival
```

## 📦 Installation Steps

### **1. Clone and Setup**
```bash
# Clone the repository (or copy files)
cd /mnt
git clone <your-repo> whatsapp
cd whatsapp

# Install Node.js dependencies
npm install

# Set up environment variables
cp .env.example .env
nano .env
```

### **2. Environment Configuration**
```bash
# .env file configuration
PORT=3001
MAX_SESSIONS=5
SAVE_MEDIA=true
LOG_LEVEL=info

# Groq API for AI Chatbot
GROQ_API_KEY=your_groq_api_key_here

# Optional TTS providers
OPENAI_API_KEY=your_openai_key
ELEVENLABS_API_KEY=your_elevenlabs_key

# Security
CORS_ORIGIN=*
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW=900

# Database
DATABASE_PATH=./database/whatsapp.db
```

### **3. Database Setup**
```bash
# Create database directory
mkdir -p database

# Initialize database (automatic on first run)
node whatsapp_service.js
```

### **4. Create Admin User**
```bash
# Register first admin user
curl -X POST "http://localhost:3001/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "**********",
    "password": "your_secure_password",
    "name": "Admin User",
    "email": "<EMAIL>"
  }'
```

## 🚀 Starting the Service

### **Development Mode**
```bash
cd /mnt/whatsapp
node whatsapp_service.js
```

### **Production Mode with PM2**
```bash
# Install PM2
npm install -g pm2

# Start service
pm2 start whatsapp_service.js --name "whatsapp-automation"

# Save PM2 configuration
pm2 save
pm2 startup

# Monitor logs
pm2 logs whatsapp-automation
```

### **Service Management**
```bash
# Check status
pm2 status

# Restart service
pm2 restart whatsapp-automation

# Stop service
pm2 stop whatsapp-automation

# View logs
pm2 logs whatsapp-automation --lines 100
```

## 🔧 Configuration

### **1. WhatsApp Instance Setup**
1. Open dashboard: `http://your-server:3001`
2. Login with admin credentials
3. Create new WhatsApp instance
4. Scan QR code with WhatsApp mobile app
5. Wait for connection confirmation

### **2. AI Chatbot Configuration**
```bash
# Configure chatbot for instance
curl -X POST "http://localhost:3001/instances/**********/chatbot/configure" \
  -H "Content-Type: application/json" \
  -d '{
    "enabled": true,
    "model": "llama-3.1-70b-versatile",
    "systemPrompt": "You are a helpful WhatsApp assistant.",
    "temperature": 0.7
  }'
```

### **3. Tool Configuration**
```bash
# Configure tools for specific number
curl -X POST "http://localhost:3001/api/instances/**********/tools/configure" \
  -H "Content-Type: application/json" \
  -d '{
    "phoneNumber": "<EMAIL>",
    "enabledTools": ["generate_pdf", "schedule_later"],
    "toolConfigs": {
      "generate_pdf": {
        "maxFileSize": "10MB"
      }
    }
  }'
```

## 🔗 n8n Integration Setup

### **1. Install n8n**
```bash
# Install n8n globally
npm install -g n8n

# Start n8n
n8n start
```

### **2. Import Workflow Templates**
1. Open n8n dashboard: `http://localhost:5678`
2. Import the provided workflow templates from `/docs/n8n_workflows/`
3. Configure webhook URLs to point to your WhatsApp API

### **3. Example n8n Workflow**
```json
{
  "nodes": [
    {
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "parameters": {
        "path": "whatsapp-trigger"
      }
    },
    {
      "name": "Send WhatsApp Message",
      "type": "n8n-nodes-base.httpRequest",
      "parameters": {
        "url": "http://localhost:3001/api/webhook/message",
        "method": "POST",
        "body": {
          "sessionId": "**********",
          "to": "{{$json.phoneNumber}}",
          "message": "Hello! Your order has been processed.",
          "triggerTools": true,
          "toolsConfig": {
            "enabledTools": ["generate_pdf"]
          }
        }
      }
    }
  ]
}
```

## 📊 Monitoring & Maintenance

### **1. Health Checks**
```bash
# Service health
curl http://localhost:3001/health

# Instance status
curl http://localhost:3001/api/instances

# System stats
curl http://localhost:3001/stats
```

### **2. Log Management**
```bash
# View service logs
tail -f /mnt/whatsapp/logs/service-$(date +%Y-%m-%d).log

# View message logs
tail -f /mnt/whatsapp/logs/messages/messages-$(date +%Y-%m-%d).log

# View error logs
tail -f /mnt/whatsapp/logs/errors/errors-$(date +%Y-%m-%d).log
```

### **3. Database Maintenance**
```bash
# Backup database
cp /mnt/whatsapp/database/whatsapp.db /backup/whatsapp-$(date +%Y%m%d).db

# Check database size
du -h /mnt/whatsapp/database/whatsapp.db

# Vacuum database (optimize)
sqlite3 /mnt/whatsapp/database/whatsapp.db "VACUUM;"
```

## 🔒 Security Considerations

### **1. Firewall Configuration**
```bash
# Allow only necessary ports
sudo ufw allow 3001/tcp  # WhatsApp API
sudo ufw allow 5678/tcp  # n8n (if needed)
sudo ufw enable
```

### **2. SSL/TLS Setup (Production)**
```bash
# Install Nginx
sudo apt install nginx

# Configure reverse proxy
sudo nano /etc/nginx/sites-available/whatsapp-api

# Example Nginx configuration:
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### **3. Environment Security**
```bash
# Secure environment file
chmod 600 .env

# Regular security updates
sudo apt update && sudo apt upgrade -y
```

## 🆘 Troubleshooting

### **Common Issues**

1. **Chrome/Chromium not found**
   ```bash
   sudo apt install chromium-browser
   ```

2. **Port already in use**
   ```bash
   sudo lsof -i :3001
   sudo kill -9 <PID>
   ```

3. **Database locked**
   ```bash
   sudo service whatsapp-automation stop
   sudo service whatsapp-automation start
   ```

4. **QR code not generating**
   - Check Chrome/Chromium installation
   - Verify session directory permissions
   - Restart the instance

### **Support**
- **Email**: <EMAIL>
- **Phone**: +91-**********
- **Documentation**: Check `/docs/` directory

## 🎉 Success!

Your WhatsApp Business Automation Platform is now ready for production use with full n8n integration capabilities!
