# WhatsApp Business Automation Platform API

## 🚀 Overview

The WhatsApp Business Automation Platform API provides comprehensive endpoints for managing WhatsApp instances, AI chatbot interactions, tool management, and conversation context. This API is specifically designed for seamless integration with **n8n workflow automation** and other business automation tools.

## 📋 Key Features

### 🔧 **Enhanced Tool Management System**
- **Global Tools**: Available across all instances
- **Per-Number Tool Configuration**: Enable specific tools for individual phone numbers
- **Dynamic Tool Loading**: Tools can be enabled/disabled on-demand
- **Tool Categories**: Organized by functionality (document, communication, scheduling)

### 🧠 **Context Engine**
- **Per-Number Context**: Individual conversation history for each phone number
- **User Profile Management**: Store and update user information
- **Conversation Topics**: Track and manage conversation themes
- **Context Statistics**: Monitor conversation metrics

### 🤖 **AI Chatbot Integration**
- **Tool Calling**: AI can execute tools based on user requests
- **Context-Aware Responses**: Uses conversation history for better responses
- **Multi-Language Support**: Voice transcription and text processing
- **Media Processing**: Handle images, documents, and voice messages

### 🔗 **n8n Integration Ready**
- **Webhook Endpoints**: Purpose-built for n8n workflows
- **Single-Request Operations**: Send messages and configure tools together
- **Comprehensive Response Data**: Detailed success/error information

## 🛠️ Available Tools

### 📄 **Document Tools**
- `generate_pdf`: Create PDF documents from HTML or text content
- Support for rich formatting, tables, lists, and styling

### 📞 **Communication Tools**
- `send_message_to_number`: Send messages to other WhatsApp numbers
- Cross-instance messaging capabilities

### ⏰ **Scheduling Tools**
- `schedule_later`: Schedule tasks and reminders
- `get_scheduled_tasks`: Retrieve scheduled tasks for a number

## 🔐 Authentication

Most API endpoints require authentication using Bearer token:

```bash
Authorization: Bearer YOUR_TOKEN_HERE
```

### Getting Authentication Token

```bash
POST /auth/login
Content-Type: application/json

{
  "mobileNumber": "**********",
  "password": "your_password"
}
```

## 📚 API Documentation

### 📖 **OpenAPI Specification**
- **File**: `openapi.yaml`
- **Format**: OpenAPI 3.0.3
- **Interactive Documentation**: Import into Swagger UI or similar tools

### 📮 **Postman Collection**
- **File**: `WhatsApp_Automation_API.postman_collection.json`
- **Ready-to-Use**: Pre-configured requests with examples
- **Environment Variables**: Easy configuration for different environments

## 🚀 Quick Start Guide

### 1. **Import Postman Collection**
```bash
# Import the collection file into Postman
WhatsApp_Automation_API.postman_collection.json
```

### 2. **Set Environment Variables**
```bash
base_url: http://localhost:3001
session_id: **********
phone_number: <EMAIL>
auth_token: YOUR_TOKEN_HERE
```

### 3. **Test Basic Functionality**
```bash
# 1. Health Check
GET {{base_url}}/health

# 2. Get Instances
GET {{base_url}}/api/instances

# 3. Send Test Message
POST {{base_url}}/api/instances/{{session_id}}/send
{
  "to": "{{phone_number}}",
  "message": "Hello from API!",
  "type": "text"
}
```

## 🔧 Tool Configuration Examples

### **Enable PDF Generation for a Number**
```bash
POST /api/instances/**********/tools/configure
{
  "phoneNumber": "<EMAIL>",
  "enabledTools": ["generate_pdf"],
  "toolConfigs": {
    "generate_pdf": {
      "maxFileSize": "10MB",
      "allowedFormats": ["html", "text"]
    }
  }
}
```

### **Execute PDF Generation**
```bash
POST /api/instances/**********/tools/execute
{
  "toolName": "generate_pdf",
  "parameters": {
    "content": "<h1>Sample PDF</h1><p>This is a test document.</p>",
    "title": "Test Document",
    "format_type": "html"
  },
  "phoneNumber": "<EMAIL>"
}
```

## 🧠 Context Management Examples

### **Get Conversation Context**
```bash
GET /api/instances/**********/context/<EMAIL>
```

### **Add Message to Context**
```bash
POST /api/instances/**********/context/<EMAIL>/message
{
  "message": "I need help with my order",
  "isFromUser": true
}
```

### **Update User Profile**
```bash
PUT /api/instances/**********/context/<EMAIL>/profile
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "preferences": {
    "language": "en",
    "timezone": "Asia/Kolkata"
  }
}
```

## 🔗 n8n Integration Examples

### **Simple Message Automation**
```bash
POST /api/webhook/message
{
  "sessionId": "**********",
  "to": "<EMAIL>",
  "message": "Your order #12345 has been confirmed!",
  "type": "text"
}
```

### **Message with Tool Configuration**
```bash
POST /api/webhook/message
{
  "sessionId": "**********",
  "to": "<EMAIL>",
  "message": "Welcome! You now have access to premium features.",
  "type": "text",
  "triggerTools": true,
  "toolsConfig": {
    "enabledTools": ["generate_pdf", "schedule_later"],
    "toolConfigs": {
      "generate_pdf": {
        "maxFileSize": "10MB"
      }
    }
  }
}
```

## 📊 Response Formats

### **Success Response**
```json
{
  "success": true,
  "data": {
    "messageId": "3EB0C767D82A1E90D212",
    "timestamp": "2025-06-10T10:30:00Z"
  }
}
```

### **Error Response**
```json
{
  "success": false,
  "error": "Session not found or not connected"
}
```

## 🔍 Monitoring and Debugging

### **Health Check**
```bash
GET /health
```

### **Instance Status**
```bash
GET /api/instances
```

### **Context Statistics**
```bash
GET /api/instances/**********/context/<EMAIL>/stats
```

## 📞 Support

- **Email**: <EMAIL>
- **Phone**: +91-**********
- **Company**: Arihant AI Solutions

## 🏷️ Version Information

- **API Version**: 1.0.0
- **OpenAPI Version**: 3.0.3
- **Last Updated**: June 2025

---

**Ready for n8n Integration** ✅  
**Production Ready** ✅  
**Comprehensive Documentation** ✅
