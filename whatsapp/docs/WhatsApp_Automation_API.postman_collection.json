{"info": {"name": "WhatsApp Business Automation Platform API", "description": "Comprehensive API collection for WhatsApp Business automation with AI chatbot integration, tool management, and context engine. Perfect for n8n workflow automation.\n\nContact: <EMAIL>\nPhone: +91-**********", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "session_id", "value": "**********", "type": "string"}, {"key": "phone_number", "value": "<EMAIL>", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Instance Management", "item": [{"name": "Get All Instances", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/instances", "host": ["{{base_url}}"], "path": ["api", "instances"]}, "description": "Retrieve list of all WhatsApp instances with their status and information"}, "response": []}, {"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{phone_number}}\",\n  \"message\": \"Hello! This is a test message from the API.\",\n  \"type\": \"text\"\n}"}, "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/send", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "send"]}, "description": "Send a text message through a specific WhatsApp instance"}, "response": []}, {"name": "Send Media Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{phone_number}}\",\n  \"message\": \"Here's your document!\",\n  \"type\": \"media\",\n  \"media\": {\n    \"mimetype\": \"application/pdf\",\n    \"data\": \"base64_encoded_data_here\",\n    \"filename\": \"document.pdf\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/send", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "send"]}, "description": "Send a media message (PDF, image, etc.) through a WhatsApp instance"}, "response": []}]}, {"name": "Tool Management", "item": [{"name": "Get Available Tools", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/tools", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "tools"]}, "description": "Get list of available tools for an instance"}, "response": []}, {"name": "Get Tools for Specific Number", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/tools?phoneNumber={{phone_number}}", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "tools"], "query": [{"key": "phoneNumber", "value": "{{phone_number}}"}]}, "description": "Get tools available for a specific phone number"}, "response": []}, {"name": "Configure Tools for Number", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phoneNumber\": \"{{phone_number}}\",\n  \"enabledTools\": [\n    \"generate_pdf\",\n    \"send_message_to_number\",\n    \"schedule_later\"\n  ],\n  \"toolConfigs\": {\n    \"generate_pdf\": {\n      \"maxFileSize\": \"10MB\",\n      \"allowedFormats\": [\"html\", \"text\"]\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/tools/configure", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "tools", "configure"]}, "description": "Configure which tools are enabled for a specific phone number"}, "response": []}, {"name": "Execute PDF Generation Tool", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"toolName\": \"generate_pdf\",\n  \"parameters\": {\n    \"content\": \"<h1>Sample PDF Document</h1><h2>Introduction</h2><p>This is a sample PDF document generated through the API. It demonstrates the PDF generation capabilities of the WhatsApp automation platform.</p><h2>Features</h2><ul><li>HTML to PDF conversion</li><li>Rich formatting support</li><li>Automatic file delivery</li><li>Integration with WhatsApp</li></ul><h2>Contact Information</h2><p>For more information, contact Arihant AI Solutions:<br>Email: <EMAIL><br>Phone: +91-**********</p>\",\n    \"title\": \"Sample PDF Document\",\n    \"format_type\": \"html\"\n  },\n  \"phoneNumber\": \"{{phone_number}}\"\n}"}, "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/tools/execute", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "tools", "execute"]}, "description": "Execute the PDF generation tool with sample content"}, "response": []}, {"name": "Get Global Tools", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/tools/global", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "tools", "global"]}, "description": "Get list of all global tools available across instances"}, "response": []}]}, {"name": "Context Management", "item": [{"name": "Get Conversation Context", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/context/{{phone_number}}", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "context", "{{phone_number}}"]}, "description": "Get conversation context for a specific phone number"}, "response": []}, {"name": "Get Formatted Context", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/context/{{phone_number}}?formatted=true", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "context", "{{phone_number}}"], "query": [{"key": "formatted", "value": "true"}]}, "description": "Get formatted conversation context suitable for AI model input"}, "response": []}, {"name": "Add Message to Context", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"Hello, I need help with my order status\",\n  \"isFromUser\": true\n}"}, "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/context/{{phone_number}}/message", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "context", "{{phone_number}}", "message"]}, "description": "Add a message to the conversation context"}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"preferences\": {\n    \"language\": \"en\",\n    \"timezone\": \"Asia/Kolkata\",\n    \"notifications\": true\n  },\n  \"metadata\": {\n    \"customerType\": \"premium\",\n    \"lastOrderDate\": \"2025-06-01\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/context/{{phone_number}}/profile", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "context", "{{phone_number}}", "profile"]}, "description": "Update user profile information in the conversation context"}, "response": []}, {"name": "Get Context Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/context/{{phone_number}}/stats", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "context", "{{phone_number}}", "stats"]}, "description": "Get statistics about the conversation context"}, "response": []}, {"name": "Clear Context", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/instances/{{session_id}}/context/{{phone_number}}", "host": ["{{base_url}}"], "path": ["api", "instances", "{{session_id}}", "context", "{{phone_number}}"]}, "description": "Clear all conversation context for a phone number"}, "response": []}]}, {"name": "n8n Webhook Integration", "item": [{"name": "Send Message with Tool Configuration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{session_id}}\",\n  \"to\": \"{{phone_number}}\",\n  \"message\": \"Hello! Your account has been activated. You now have access to our premium features including PDF generation and scheduling tools.\",\n  \"type\": \"text\",\n  \"triggerTools\": true,\n  \"toolsConfig\": {\n    \"enabledTools\": [\n      \"generate_pdf\",\n      \"send_message_to_number\",\n      \"schedule_later\",\n      \"get_scheduled_tasks\"\n    ],\n    \"toolConfigs\": {\n      \"generate_pdf\": {\n        \"maxFileSize\": \"10MB\",\n        \"allowedFormats\": [\"html\", \"text\"]\n      },\n      \"schedule_later\": {\n        \"maxScheduleDays\": 30\n      }\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/api/webhook/message", "host": ["{{base_url}}"], "path": ["api", "webhook", "message"]}, "description": "Primary webhook endpoint for n8n. Sends a message and configures tools for the recipient in a single request."}, "response": []}, {"name": "Simple Message Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{session_id}}\",\n  \"to\": \"{{phone_number}}\",\n  \"message\": \"Thank you for your order! Your order #12345 has been confirmed and will be processed shortly.\",\n  \"type\": \"text\"\n}"}, "url": {"raw": "{{base_url}}/api/webhook/message", "host": ["{{base_url}}"], "path": ["api", "webhook", "message"]}, "description": "Simple message sending through webhook endpoint - perfect for n8n order confirmations, notifications, etc."}, "response": []}, {"name": "Media Message Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{session_id}}\",\n  \"to\": \"{{phone_number}}\",\n  \"message\": \"Here's your invoice for order #12345\",\n  \"type\": \"media\",\n  \"media\": {\n    \"mimetype\": \"application/pdf\",\n    \"data\": \"base64_encoded_pdf_data_here\",\n    \"filename\": \"invoice_12345.pdf\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/webhook/message", "host": ["{{base_url}}"], "path": ["api", "webhook", "message"]}, "description": "Send media files (PDFs, images, documents) through webhook - ideal for automated invoice delivery, reports, etc."}, "response": []}]}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"mobileNumber\": \"**********\",\n  \"password\": \"your_password_here\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "description": "Login to get authentication token. Update the auth_token variable with the returned token."}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the API service is running and healthy"}, "response": []}]}]}