openapi: 3.0.3
info:
  title: WhatsApp Business Automation Platform API
  description: |
    Comprehensive API for WhatsApp Business automation with AI chatbot integration, 
    tool management, and context engine. Perfect for n8n workflow automation.
    
    ## Features
    - Multiple WhatsApp instance management
    - Per-number tool configuration
    - Conversation context engine
    - AI chatbot with tool calling
    - Media handling and processing
    - Voice message transcription
    - PDF generation and document tools
    
    ## Authentication
    Most endpoints require authentication using Bearer token or session cookies.
    
    ## Rate Limiting
    API endpoints are rate-limited to prevent abuse. Authenticated requests have higher limits.
    
    Contact: <EMAIL>
  version: 1.0.0
  contact:
    name: Arihant AI Solutions
    email: <EMAIL>
    url: https://arihantai.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3001
    description: Local development server
  - url: https://your-domain.com
    description: Production server

tags:
  - name: Instances
    description: WhatsApp instance management
  - name: Messages
    description: Message sending and receiving
  - name: Tools
    description: Tool management and configuration
  - name: Context
    description: Conversation context management
  - name: Webhooks
    description: Webhook endpoints for n8n integration

paths:
  # ============================================================================
  # INSTANCE MANAGEMENT
  # ============================================================================
  
  /api/instances:
    get:
      tags: [Instances]
      summary: Get all WhatsApp instances
      description: Retrieve list of all WhatsApp instances with their status and information
      responses:
        '200':
          description: List of instances retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/WhatsAppInstance'
                  count:
                    type: integer
                    example: 3

  /api/instances/{sessionId}/send:
    post:
      tags: [Messages]
      summary: Send message through specific instance
      description: Send text, media, or location message through a WhatsApp instance
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          description: WhatsApp instance session ID
          example: "8487921219"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [to, message]
              properties:
                to:
                  type: string
                  description: Recipient phone number with country code
                  example: "<EMAIL>"
                message:
                  type: string
                  description: Message content
                  example: "Hello! How can I help you today?"
                type:
                  type: string
                  enum: [text, media, location]
                  default: text
                  description: Message type
                media:
                  type: object
                  description: Media data for media messages
                  properties:
                    mimetype:
                      type: string
                      example: "image/jpeg"
                    data:
                      type: string
                      format: base64
                      description: Base64 encoded media data
                    filename:
                      type: string
                      example: "image.jpg"
      responses:
        '200':
          description: Message sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageResponse'

  # ============================================================================
  # TOOL MANAGEMENT
  # ============================================================================
  
  /api/instances/{sessionId}/tools:
    get:
      tags: [Tools]
      summary: Get available tools for instance
      description: Get list of available tools for an instance or specific phone number
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
        - name: phoneNumber
          in: query
          schema:
            type: string
          description: Get tools for specific phone number
          example: "<EMAIL>"
      responses:
        '200':
          description: Tools retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      sessionId:
                        type: string
                        example: "8487921219"
                      phoneNumber:
                        type: string
                        nullable: true
                        example: "<EMAIL>"
                      tools:
                        type: array
                        items:
                          $ref: '#/components/schemas/Tool'
                      count:
                        type: integer
                        example: 5

  /api/instances/{sessionId}/tools/configure:
    post:
      tags: [Tools]
      summary: Configure tools for specific phone number
      description: Enable/disable specific tools for a phone number in an instance
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [phoneNumber, enabledTools]
              properties:
                phoneNumber:
                  type: string
                  description: Phone number to configure tools for
                  example: "<EMAIL>"
                enabledTools:
                  type: array
                  items:
                    type: string
                  description: List of tool names to enable
                  example: ["generate_pdf", "send_message_to_number"]
                toolConfigs:
                  type: object
                  description: Tool-specific configurations
                  additionalProperties:
                    type: object
      responses:
        '200':
          description: Tools configured successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /api/instances/{sessionId}/tools/execute:
    post:
      tags: [Tools]
      summary: Execute a tool
      description: Execute a specific tool with parameters
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [toolName, parameters]
              properties:
                toolName:
                  type: string
                  description: Name of the tool to execute
                  example: "generate_pdf"
                parameters:
                  type: object
                  description: Tool parameters
                  example:
                    content: "<h1>Sample PDF</h1><p>This is a sample PDF document.</p>"
                    title: "Sample Document"
                    format_type: "html"
                phoneNumber:
                  type: string
                  description: Phone number context for tool execution
                  example: "<EMAIL>"
      responses:
        '200':
          description: Tool executed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ToolExecutionResponse'

  /api/instances/{sessionId}/tools/global:
    get:
      tags: [Tools]
      summary: Get all available global tools
      description: Get list of all global tools available across instances
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
      responses:
        '200':
          description: Global tools retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      globalTools:
                        type: array
                        items:
                          $ref: '#/components/schemas/GlobalTool'
                      count:
                        type: integer
                        example: 8

  # ============================================================================
  # CONTEXT MANAGEMENT
  # ============================================================================

  /api/instances/{sessionId}/context/{phoneNumber}:
    get:
      tags: [Context]
      summary: Get conversation context for a phone number
      description: Retrieve conversation context and history for a specific phone number
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
        - name: phoneNumber
          in: path
          required: true
          schema:
            type: string
          example: "<EMAIL>"
        - name: formatted
          in: query
          schema:
            type: boolean
            default: false
          description: Return formatted context for AI model
      responses:
        '200':
          description: Context retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      sessionId:
                        type: string
                        example: "8487921219"
                      phoneNumber:
                        type: string
                        example: "<EMAIL>"
                      context:
                        $ref: '#/components/schemas/ConversationContext'

    delete:
      tags: [Context]
      summary: Clear context for a phone number
      description: Clear all conversation context for a specific phone number
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
        - name: phoneNumber
          in: path
          required: true
          schema:
            type: string
          example: "<EMAIL>"
      responses:
        '200':
          description: Context cleared successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      cleared:
                        type: boolean
                        example: true

  /api/instances/{sessionId}/context/{phoneNumber}/message:
    post:
      tags: [Context]
      summary: Add message to context
      description: Add a message to the conversation context for a phone number
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
        - name: phoneNumber
          in: path
          required: true
          schema:
            type: string
          example: "<EMAIL>"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [message]
              properties:
                message:
                  type: string
                  description: Message content to add to context
                  example: "Hello, I need help with my order"
                isFromUser:
                  type: boolean
                  default: true
                  description: Whether the message is from user (true) or bot (false)
      responses:
        '200':
          description: Message added to context successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      added:
                        type: boolean
                        example: true

  /api/instances/{sessionId}/context/{phoneNumber}/profile:
    put:
      tags: [Context]
      summary: Update user profile in context
      description: Update user profile information in the conversation context
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
        - name: phoneNumber
          in: path
          required: true
          schema:
            type: string
          example: "<EMAIL>"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: "John Doe"
                email:
                  type: string
                  example: "<EMAIL>"
                preferences:
                  type: object
                  description: User preferences
                metadata:
                  type: object
                  description: Additional metadata
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      updated:
                        type: boolean
                        example: true

  /api/instances/{sessionId}/context/{phoneNumber}/stats:
    get:
      tags: [Context]
      summary: Get context statistics
      description: Get statistics about the conversation context for a phone number
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
          example: "8487921219"
        - name: phoneNumber
          in: path
          required: true
          schema:
            type: string
          example: "<EMAIL>"
      responses:
        '200':
          description: Context statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      sessionId:
                        type: string
                        example: "8487921219"
                      phoneNumber:
                        type: string
                        example: "<EMAIL>"
                      stats:
                        $ref: '#/components/schemas/ContextStats'

  # ============================================================================
  # WEBHOOK ENDPOINTS FOR N8N INTEGRATION
  # ============================================================================

  /api/webhook/message:
    post:
      tags: [Webhooks]
      summary: Webhook endpoint for n8n message automation
      description: |
        Primary webhook endpoint for n8n workflow automation.
        Allows sending messages and configuring tools in a single request.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [sessionId, to, message]
              properties:
                sessionId:
                  type: string
                  description: WhatsApp instance session ID
                  example: "8487921219"
                to:
                  type: string
                  description: Recipient phone number
                  example: "<EMAIL>"
                message:
                  type: string
                  description: Message content
                  example: "Hello! Your order has been processed."
                type:
                  type: string
                  enum: [text, media, location]
                  default: text
                  description: Message type
                triggerTools:
                  type: boolean
                  default: false
                  description: Whether to configure tools for this number
                toolsConfig:
                  type: object
                  description: Tool configuration if triggerTools is true
                  properties:
                    enabledTools:
                      type: array
                      items:
                        type: string
                      example: ["generate_pdf", "send_message_to_number"]
                    toolConfigs:
                      type: object
                      description: Tool-specific configurations
      responses:
        '200':
          description: Webhook processed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      message:
                        $ref: '#/components/schemas/MessageResponse'
                      tools:
                        type: object
                        nullable: true
                        description: Tool configuration result if triggerTools was true

components:
  schemas:
    WhatsAppInstance:
      type: object
      properties:
        sessionId:
          type: string
          example: "8487921219"
        phoneNumber:
          type: string
          nullable: true
          example: "+91 84879 21219"
        status:
          type: string
          enum: [connected, disconnected, qr_ready, initializing, error]
          example: "connected"
        hasQR:
          type: boolean
          example: false
        messageCount:
          type: integer
          example: 1250
        lastActivity:
          type: string
          format: date-time
          nullable: true
          example: "2025-06-10T10:30:00Z"

    MessageResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            messageId:
              type: string
              example: "3EB0C767D82A1E90D212"
            timestamp:
              type: string
              format: date-time
              example: "2025-06-10T10:30:00Z"

    Tool:
      type: object
      properties:
        type:
          type: string
          example: "function"
        function:
          type: object
          properties:
            name:
              type: string
              example: "generate_pdf"
            description:
              type: string
              example: "Generate PDF from text or HTML content"
            parameters:
              type: object
              description: JSON Schema for tool parameters

    GlobalTool:
      type: object
      properties:
        name:
          type: string
          example: "generate_pdf"
        description:
          type: string
          example: "Generate PDF from text or HTML content"
        category:
          type: string
          example: "document"
        scope:
          type: string
          example: "global"
        parameters:
          type: object
          description: JSON Schema for tool parameters

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object

    ToolExecutionResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: object
          properties:
            success:
              type: boolean
              example: true
            result:
              type: object
              description: Tool execution result

    ConversationContext:
      type: object
      properties:
        sessionId:
          type: string
          example: "8487921219"
        phoneNumber:
          type: string
          example: "<EMAIL>"
        messages:
          type: array
          items:
            type: object
            properties:
              role:
                type: string
                enum: [user, assistant, system]
                example: "user"
              content:
                type: string
                example: "Hello, I need help with my order"
              timestamp:
                type: string
                format: date-time
                example: "2025-06-10T10:30:00Z"
              messageId:
                type: string
                example: "1749547369525_abc123def"
        userProfile:
          type: object
          properties:
            name:
              type: string
              example: "John Doe"
            pushname:
              type: string
              example: "John"
            number:
              type: string
              example: "919428131025"
            isMyContact:
              type: boolean
              example: true
            profilePicUrl:
              type: string
              nullable: true
              example: "https://example.com/profile.jpg"
            lastSeen:
              type: string
              format: date-time
              example: "2025-06-10T10:30:00Z"
        currentTopic:
          type: object
          nullable: true
          properties:
            topic:
              type: string
              example: "order_inquiry"
            metadata:
              type: object
            startedAt:
              type: string
              format: date-time
              example: "2025-06-10T10:25:00Z"
        createdAt:
          type: string
          format: date-time
          example: "2025-06-10T09:00:00Z"
        lastUpdated:
          type: string
          format: date-time
          example: "2025-06-10T10:30:00Z"
        metadata:
          type: object

    ContextStats:
      type: object
      properties:
        messageCount:
          type: integer
          example: 15
        hasUserProfile:
          type: boolean
          example: true
        currentTopic:
          type: string
          nullable: true
          example: "order_inquiry"
        lastUpdated:
          type: string
          format: date-time
          example: "2025-06-10T10:30:00Z"
        createdAt:
          type: string
          format: date-time
          example: "2025-06-10T09:00:00Z"
        isExpired:
          type: boolean
          example: false

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: "Error message"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []
