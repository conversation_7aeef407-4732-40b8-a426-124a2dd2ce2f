{"name": "WhatsApp Order Confirmation Workflow", "nodes": [{"parameters": {"path": "order-confirmation", "options": {}}, "id": "webhook-trigger", "name": "Order Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "order-confirmation-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.orderStatus }}", "rightValue": "confirmed", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-order-status", "name": "Check Order Status", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"url": "http://localhost:3001/api/webhook/message", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "sessionId", "value": "8487921219"}, {"name": "to", "value": "={{ $json.customerPhone }}"}, {"name": "message", "value": "🎉 Great news! Your order #{{ $json.orderId }} has been confirmed!\n\n📦 Order Details:\n• Product: {{ $json.productName }}\n• Quantity: {{ $json.quantity }}\n• Total: ${{ $json.totalAmount }}\n• Estimated Delivery: {{ $json.deliveryDate }}\n\n📱 You now have access to order tracking and PDF invoice generation. Just ask me for updates!"}, {"name": "type", "value": "text"}, {"name": "triggerTools", "value": true}, {"name": "toolsConfig", "value": {"enabledTools": ["generate_pdf", "schedule_later"], "toolConfigs": {"generate_pdf": {"maxFileSize": "10MB", "allowedFormats": ["html", "text"]}, "schedule_later": {"maxScheduleDays": 30}}}}]}, "options": {}}, "id": "send-confirmation", "name": "Send Order Confirmation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 200]}, {"parameters": {"url": "http://localhost:3001/api/instances/8487921219/tools/execute", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "toolName", "value": "generate_pdf"}, {"name": "parameters", "value": {"content": "<h1>Order Invoice</h1><h2>Order #{{ $json.orderId }}</h2><div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'><h3>Customer Information</h3><p><strong>Name:</strong> {{ $json.customerName }}<br><strong>Phone:</strong> {{ $json.customerPhone }}<br><strong>Email:</strong> {{ $json.customerEmail }}</p></div><div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'><h3>Order Details</h3><table style='width: 100%; border-collapse: collapse;'><tr style='background: #1976d2; color: white;'><th style='padding: 10px; text-align: left;'>Product</th><th style='padding: 10px; text-align: center;'>Quantity</th><th style='padding: 10px; text-align: right;'>Price</th><th style='padding: 10px; text-align: right;'>Total</th></tr><tr><td style='padding: 10px; border-bottom: 1px solid #ddd;'>{{ $json.productName }}</td><td style='padding: 10px; text-align: center; border-bottom: 1px solid #ddd;'>{{ $json.quantity }}</td><td style='padding: 10px; text-align: right; border-bottom: 1px solid #ddd;'>${{ $json.unitPrice }}</td><td style='padding: 10px; text-align: right; border-bottom: 1px solid #ddd;'>${{ $json.totalAmount }}</td></tr></table></div><div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;'><h3>Delivery Information</h3><p><strong>Address:</strong> {{ $json.deliveryAddress }}<br><strong>Estimated Delivery:</strong> {{ $json.deliveryDate }}<br><strong>Tracking Number:</strong> {{ $json.trackingNumber }}</p></div><div style='text-align: center; margin-top: 30px; padding: 20px; background: #fff3e0; border-radius: 8px;'><h3>Thank you for your order!</h3><p>For support, contact us at:<br><strong>Email:</strong> <EMAIL><br><strong>Phone:</strong> +91-8487921219</p></div>", "title": "Order Invoice #{{ $json.orderId }}", "format_type": "html"}}, {"name": "phoneNumber", "value": "={{ $json.customerPhone }}"}]}, "options": {}}, "id": "generate-invoice", "name": "Generate Invoice PDF", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 200]}, {"parameters": {"url": "http://localhost:3001/api/instances/8487921219/tools/execute", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "toolName", "value": "schedule_later"}, {"name": "parameters", "value": {"task_description": "Send delivery reminder for order #{{ $json.orderId }}", "schedule_time": "{{ $json.deliveryDate }} 09:00", "phone_number": "{{ $json.customerPhone }}", "context": "Order #{{ $json.orderId }} - {{ $json.productName }} - Delivery scheduled"}}, {"name": "phoneNumber", "value": "={{ $json.customerPhone }}"}]}, "options": {}}, "id": "schedule-reminder", "name": "Schedule Delivery Reminder", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, 200]}, {"parameters": {"url": "http://localhost:3001/api/webhook/message", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "sessionId", "value": "8487921219"}, {"name": "to", "value": "={{ $json.customerPhone }}"}, {"name": "message", "value": "❌ We're sorry, but there was an issue with your order #{{ $json.orderId }}.\n\n📞 Please contact our support team for assistance:\n• Email: <EMAIL>\n• Phone: +91-8487921219\n\nWe'll resolve this as quickly as possible!"}, {"name": "type", "value": "text"}]}, "options": {}}, "id": "send-error-message", "name": "Send Error Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 400]}], "connections": {"Order Webhook": {"main": [[{"node": "Check Order Status", "type": "main", "index": 0}]]}, "Check Order Status": {"main": [[{"node": "Send Order Confirmation", "type": "main", "index": 0}], [{"node": "Send Error Message", "type": "main", "index": 0}]]}, "Send Order Confirmation": {"main": [[{"node": "Generate Invoice PDF", "type": "main", "index": 0}]]}, "Generate Invoice PDF": {"main": [[{"node": "Schedule Delivery Reminder", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-06-10T10:00:00.000Z", "updatedAt": "2025-06-10T10:00:00.000Z", "id": "whatsapp-automation", "name": "WhatsApp Automation"}], "triggerCount": 1, "updatedAt": "2025-06-10T10:00:00.000Z", "versionId": "1"}