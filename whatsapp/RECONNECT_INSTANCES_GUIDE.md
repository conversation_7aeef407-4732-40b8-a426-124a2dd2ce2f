# 🔄 How to Reconnect WhatsApp Instances from Admin Panel

## 🎯 **When Instances Show "Error" Status**

When WhatsApp instances show "Error" or "Connection failed" status, it means they've lost connection to WhatsApp servers. Here's how to reconnect them:

---

## 🔑 **Step 1: Login to Admin Panel**

1. **Open Dashboard**: http://140.238.231.90:3002
2. **Login with Admin Credentials**:
   - Mobile Number: `919999999999`
   - Password: `admin123`

---

## 🔄 **Step 2: Reconnection Methods**

### **Method A: Generate New QR Code (Recommended)**

1. **Click on the Instance Card** showing "Error" status
2. **Click "QR Code" button** in the instance details
3. **New QR Code will be generated** automatically
4. **Scan with WhatsApp Mobile App**:
   - Open WhatsApp on your phone
   - Go to Settings → Linked Devices
   - Tap "Link a Device"
   - Scan the QR code displayed
5. **Instance Status will change** to "Connected"

### **Method B: Delete and Recreate Instance**

If QR method doesn't work:

1. **Click "Delete" button** on the error instance
2. **Confirm deletion** (this removes the session)
3. **Click "Create New Instance"** button
4. **Fill in the same details**:
   - Session ID: (use same or new ID)
   - Phone Number: (same number)
   - Webhook URL: (optional)
5. **New QR code will be generated**
6. **Scan with WhatsApp mobile app**

---

## 🛠️ **Step 3: API Method (Advanced)**

For developers or automated reconnection:

### **Check Instance Status**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3001/instances/SESSION_ID/status
```

### **Get New QR Code**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3001/instances/SESSION_ID/qr
```

### **Delete and Recreate Instance**
```bash
# Delete instance
curl -X DELETE -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3001/instances/SESSION_ID

# Create new instance
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"sessionId":"SESSION_ID","phoneNumber":"PHONE_NUMBER"}' \
  http://localhost:3001/instances
```

---

## 📋 **Current Instance Status**

Based on your system, here are the instances that need reconnection:

| Session ID | Phone Number | Current Status | Action Needed |
|------------|-------------|----------------|---------------|
| `8487921219` | +91 8487921219 | ❌ Error | Generate new QR |
| `test123` | +91 9999999998 | ❌ Error | Generate new QR |

---

## 🔍 **Understanding Instance Statuses**

| Status | Meaning | Action Required |
|--------|---------|-----------------|
| ✅ **Connected** | WhatsApp is connected and ready | None |
| 🟡 **QR Ready** | QR code generated, waiting for scan | Scan QR with phone |
| 🔄 **Initializing** | Starting up connection | Wait |
| ❌ **Error** | Connection lost or failed | Reconnect using methods above |
| ⚪ **Not Found** | Instance doesn't exist | Create new instance |

---

## 🚨 **Common Reconnection Issues & Solutions**

### **Issue 1: QR Code Expired**
- **Solution**: Generate a new QR code (they expire after 20 seconds)
- **How**: Click "QR Code" button again

### **Issue 2: Phone Already Linked**
- **Solution**: Unlink device from WhatsApp mobile app first
- **How**: WhatsApp → Settings → Linked Devices → Remove device

### **Issue 3: Session Files Corrupted**
- **Solution**: Delete instance completely and recreate
- **How**: Use Method B above

### **Issue 4: Network Issues**
- **Solution**: Check server connectivity
- **How**: Restart WhatsApp service if needed

---

## 🔧 **Troubleshooting Commands**

### **Check Service Status**
```bash
curl http://localhost:3001/health
```

### **View Service Logs**
```bash
tail -f /mnt/whatsapp/logs/service-$(date +%Y-%m-%d).log
```

### **Check Session Files**
```bash
ls -la /mnt/whatsapp/sessions/
```

### **Restart WhatsApp Service** (if needed)
```bash
cd /mnt/whatsapp
pm2 restart whatsapp-service
```

---

## 🎯 **Best Practices**

1. **Regular Monitoring**: Check instance status daily
2. **Quick Reconnection**: Reconnect immediately when errors occur
3. **Session Backup**: Keep session files backed up
4. **Phone Availability**: Ensure phone is available for QR scanning
5. **Network Stability**: Maintain stable internet connection

---

## 📱 **Mobile App Steps for QR Scanning**

1. **Open WhatsApp** on your mobile device
2. **Go to Settings** (three dots menu)
3. **Select "Linked Devices"**
4. **Tap "Link a Device"**
5. **Point camera at QR code** on dashboard
6. **Wait for confirmation** message
7. **Check dashboard** - status should change to "Connected"

---

## ✅ **Success Indicators**

After successful reconnection:
- ✅ Instance status shows "Connected"
- ✅ Green indicator in dashboard
- ✅ Can send/receive messages
- ✅ No error messages in logs

---

## 🆘 **Need Help?**

If reconnection still fails:

1. **Check logs** for specific error messages
2. **Verify phone number** format is correct
3. **Ensure WhatsApp mobile app** is updated
4. **Try different session ID** if issues persist
5. **Restart the entire service** as last resort

---

## 🎉 **Quick Summary**

**For Error Status Instances:**
1. Login to dashboard: http://140.238.231.90:3002
2. Click on error instance
3. Click "QR Code" button
4. Scan with WhatsApp mobile app
5. ✅ Instance reconnected!

**The dashboard provides the easiest way to reconnect instances!** 🚀
