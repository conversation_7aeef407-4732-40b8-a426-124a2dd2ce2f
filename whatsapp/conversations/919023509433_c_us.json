{"contactId": "<EMAIL>", "messages": [{"role": "user", "content": "Hy", "timestamp": "2025-06-10T04:44:19.362Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "Hello! 👋 How can I assist you today?", "timestamp": "2025-06-10T04:44:20.180Z"}, {"role": "user", "content": "<PERSON><PERSON><PERSON> jod mare nu chhe", "timestamp": "2025-06-10T04:44:48.851Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "<PERSON><PERSON><PERSON> <PERSON>hed hai, main aapki baat samajh nahi pa raha. Kya aap apni baat phir se kehna chahte hain? 🤔", "timestamp": "2025-06-10T04:44:49.486Z"}, {"role": "user", "content": "?", "timestamp": "2025-06-10T04:44:49.959Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "It seems like you're still having trouble communicating. Don't worry, I'm here to help! 😊 Can you please try again or provide more context about what you're trying to say? I'll do my best to understand and assist you. 🤗", "timestamp": "2025-06-10T04:44:50.271Z"}, {"role": "user", "content": "A<PERSON> su kar vanu aave chhe ?", "timestamp": "2025-06-10T04:45:40.906Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "<PERSON><PERSON><PERSON> <PERSON>hed hai, main aapki baat abhi bhi samajh nahi pa raha. Kya aap apni baat thoda dheere-dheere ya alag tarah se kehna chahte hain? Main aapki madad karne ki koshish karunga. 😊", "timestamp": "2025-06-10T04:45:41.583Z"}, {"role": "user", "content": "<PERSON><PERSON>", "timestamp": "2025-06-10T04:59:19.296Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "Theek hai! Ab main aapki madad karne ki koshish karunga. Kya aap mujhe bata sakte hain ki aap kis cheez ke baare mein jaanna chahte hain ya kya aapko kisi cheez ki zaroorat hai? Main yahan hoon aur aapki madad karne ke liye taiyaar hoon! 😊", "timestamp": "2025-06-10T04:59:19.977Z"}, {"role": "user", "content": "Ha", "timestamp": "2025-06-10T04:59:28.011Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "Theek hai! Ab aap kis cheez ke baare mein baat karna chahte hain? Kya aapko kisi cheez ki zaroorat hai ya aap kisi cheez ke baare mein jaanna chahte hain? Main yahan hoon aur aapki madad karne ke liye taiyaar hoon! 😊\n\nAap Arihant AI Solutions ke baare mein jaanna chahte hain? 🤔", "timestamp": "2025-06-10T04:59:28.493Z"}, {"role": "user", "content": "<PERSON> botad rav chhu", "timestamp": "2025-06-10T05:01:26.690Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "<PERSON><PERSON><PERSON> <PERSON>hed hai, main aapki baat abhi bhi samajh nahi pa raha. Kya aap apni baat thoda dheere-dheere ya alag tarah se kehna chahte hain? Main aapki madad karne ki koshish karunga. 😊\n\nKya aap Gujarati mein baat kar rahe hain? 🤔 <PERSON><PERSON> haan, to main aapki madad karne ki koshish karunga.", "timestamp": "2025-06-10T05:01:27.879Z"}, {"role": "user", "content": "hallo", "timestamp": "2025-06-10T05:05:29.642Z", "contactName": "Kano_2112"}, {"role": "assistant", "content": "Hello! 😊 How are you today? Is there something I can help you with or would you like to chat? 🤗 \n\nBy the way, I'm here to assist you with any questions or concerns you may have about Arihant AI Solutions or our services. Feel free to ask me anything! 😊", "timestamp": "2025-06-10T05:05:30.386Z"}], "createdAt": "2025-06-10T04:44:19.362Z", "lastActivity": "2025-06-10T05:05:29.642Z"}