{"contactId": "<EMAIL>", "messages": [{"role": "user", "content": "<PERSON><PERSON>", "timestamp": "2025-06-18T19:11:58.502Z", "contactName": "Test Tapasvi"}, {"role": "assistant", "content": "Hello! 😊 It's nice to meet you. I'm here to help with any questions or tasks you have. How can I assist you today? 🤗", "timestamp": "2025-06-18T19:11:58.980Z"}, {"role": "user", "content": "Okk", "timestamp": "2025-06-19T05:28:50.935Z", "contactName": "Test Tapasvi"}, {"role": "assistant", "content": "I'm glad you're excited! 😊 Is there something specific you'd like to chat about or ask? I'm here to help with any questions or topics you'd like to discuss. 🤗 \n\nIf you're looking for information about Arihant AI Solutions, I'd be happy to share details about our company, services, or answer any specific questions you may have. 📝 \n\nLet me know how I can assist you! 😊", "timestamp": "2025-06-19T05:28:51.410Z"}, {"role": "user", "content": "<PERSON><PERSON>", "timestamp": "2025-06-19T10:31:05.445Z", "contactName": "Test Tapasvi"}, {"role": "assistant", "content": "Hello again! 😊 It seems like you're interested in chatting, but I don't see a specific question or topic. Feel free to share what's on your mind, and I'll do my best to help or provide information. 🤗 \n\nIf you're looking for ideas, I can share some conversation starters or provide details about Arihant AI Solutions. Just let me know! 📝 \n\nHow can I assist you today?", "timestamp": "2025-06-19T10:31:06.025Z"}], "createdAt": "2025-06-18T19:11:58.502Z", "lastActivity": "2025-06-19T10:31:05.445Z"}