{"contactId": "<EMAIL>", "messages": [{"role": "user", "content": "Good morning sir", "timestamp": "2025-06-06T04:53:49.292Z", "contactName": "Shruti Ahir React Native"}, {"role": "assistant", "content": "Good morning! How can I assist you today with your accounting needs?", "timestamp": "2025-06-06T04:53:49.838Z"}, {"role": "user", "content": "Tame office kyare avavana chho manali maam puchhe chhe", "timestamp": "2025-06-06T04:54:28.920Z", "contactName": "Shruti Ahir React Native"}, {"role": "assistant", "content": "I don’t understand Gujarati yet, but I’m working on it. I will send you a message when we can talk in Gujarati.", "timestamp": "2025-06-06T04:54:29.419Z"}, {"role": "user", "content": "4 to 5", "timestamp": "2025-06-06T04:55:05.609Z", "contactName": "Shruti Ahir React Native"}, {"role": "assistant", "content": "It seems like you're mentioning numbers. Could you tell me a bit about what you're trying to accomplish or discuss regarding these numbers? Are they related to your business or something else?", "timestamp": "2025-06-06T04:55:06.165Z"}, {"role": "user", "content": "Ohk", "timestamp": "2025-06-06T04:55:52.614Z", "contactName": "Shruti Ahir React Native"}, {"role": "assistant", "content": "It seems like you're okay with that. Let's try to dive a bit deeper. Are you looking for help with accounting or financial services for your business? If so, what specific areas are you struggling with or need assistance in?", "timestamp": "2025-06-06T04:55:53.263Z"}], "createdAt": "2025-06-06T04:53:49.292Z", "lastActivity": "2025-06-06T04:55:52.614Z"}