{"contactId": "<EMAIL>", "messages": [{"role": "assistant", "content": "I've got you down for an interview on June10,2025, from 11:30 AM to12:30 PM. I'll make sure to update our schedule accordingly. Looking forward to meeting you then! 📅👋\n\nAlso, just to confirm, here are the interview details:\n\n* Date: June10,2025\n* Time: 11:30 AM -12:30 PM\n* Location: Arihant AI Office, Prahladnagar, Ahmedabad\n* Duration:45 minutes\n* Process: Technical + HR Round\n\nPlease let me know if there's anything else I can help with! 🤗", "timestamp": "2025-06-05T14:43:25.167Z"}, {"role": "user", "content": "Job description", "timestamp": "2025-06-05T14:43:47.297Z", "contactName": "<PERSON><PERSON>"}, {"role": "user", "content": "Or will u provide me for at least 1 yr of internship at your work place .", "timestamp": "2025-06-05T14:44:16.150Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "We offer a 1-year internship program with a stipend based on interview performance (no fixed stipend). For 6-month internships, there is no stipend. We also have a fully refundable ₹15,000 bond agreement, which doesn't require any upfront payment. Would you like more details about our internship programs?", "timestamp": "2025-06-05T14:44:17.176Z"}, {"role": "user", "content": "Okay", "timestamp": "2025-06-05T14:46:02.357Z", "contactName": "<PERSON><PERSON>"}, {"role": "user", "content": "I'll be there 10 june 2025", "timestamp": "2025-06-05T14:46:02.981Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "I'm glad you're interested. As discussed, I've scheduled your interview for June 10, 2025, from 11:30 AM to 12:30 PM. Please prepare to discuss your qualifications and experience. If you have any questions or need further clarification, feel free to ask. Looking forward to meeting you then! 📅👋", "timestamp": "2025-06-05T14:46:03.043Z"}, {"role": "assistant", "content": "Great! I'm looking forward to meeting you on June10,2025. Don't forget to bring:\n\n* Updated Resume\n* Photo ID Proof\n* Educational Certificates\n* Experience Letters (if applicable)\n\nSee you at11:30 AM sharp! 📅👋\n\nAlso, just a heads up, the interview will be for45 minutes, and we'll be covering technical and HR rounds. If you have any questions or concerns, feel free to reach out to me directly. 📞 +91-8487921219 or <EMAIL>", "timestamp": "2025-06-05T14:46:04.646Z"}, {"role": "user", "content": "Yes lit more details ?", "timestamp": "2025-06-05T14:52:40.880Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "Here's more information about the internship:\n\n## Internship Details\n\n* **Duration**: 1 year (Python Developer Internship)\n* **Stipend**: Based on interview performance (no fixed stipend)\n* **Bond Agreement**: ₹15,000 (fully refundable)\n\n## Interview Details\n\n* **Date**: June 10, 2025\n* **Time**: 11:30 AM - 12:30 PM\n* **Location**: Arihant AI Office, Prahladnagar, Ahmedabad\n* **Duration**: 45 minutes\n* **Process**: Technical + HR Round\n\n## Required Documents\n\n* Updated Resume\n* Photo ID Proof\n* Educational Certificates\n* Experience Letters (if applicable)\n\n## Job Description\n\n* **Role**: Python Developer Intern\n* **Responsibilities**: Assist in Python development projects, learn and grow with our team\n\nIf you have any questions or concerns, feel free to ask!", "timestamp": "2025-06-05T14:52:43.450Z"}, {"role": "user", "content": "Where to come ?", "timestamp": "2025-06-05T14:54:16.780Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "Our office is located at:\n\nArihant AI Office\nPrahladnagar, Ahmedabad\n\nYou can use Google Maps or any other navigation service to find the exact address. If you need help with directions or have any questions, feel free to ask! 📍👋\n\nAlso, don't forget to bring the required documents:\n\n* Updated Resume\n* Photo ID Proof\n* Educational Certificates\n* Experience Letters (if applicable)\n\nSee you on June10,2025, at11:30 AM!", "timestamp": "2025-06-05T14:54:18.865Z"}, {"role": "user", "content": "Okay thanks for this much help", "timestamp": "2025-06-05T14:59:10.503Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "You're welcome! 😊 It was my pleasure to assist you. I'm looking forward to meeting you on June10,2025. If you have any more questions or need further clarification, feel free to reach out.\n\nHave a great day, and I'll see you soon! 👋", "timestamp": "2025-06-05T14:59:12.213Z"}, {"role": "user", "content": "I'll be very thankfull for this .", "timestamp": "2025-06-05T14:59:19.300Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "😊 You're welcome! I'm glad I could help. Don't worry, just be yourself and be prepared to discuss your qualifications and experience. You got this! 💪\n\nIf you need any more assistance or have questions, feel free to ask. I'm here to help.\n\nSee you on June10,2025! 👋", "timestamp": "2025-06-05T14:59:19.892Z"}], "createdAt": "2025-06-05T14:41:35.385Z", "lastActivity": "2025-06-05T14:59:19.300Z"}