{"contactId": "<EMAIL>", "messages": [{"role": "user", "content": "Harshil_Resume.pdf", "timestamp": "2025-06-05T15:49:13.616Z", "contactName": "Drashtu🖤"}, {"role": "assistant", "content": "It seems <PERSON><PERSON><PERSON>'s resume wasn't found in our database. 📄 Can you please share more details about <PERSON><PERSON><PERSON>, such as the position he's applying for? I'll be happy to assist further! 🤝", "timestamp": "2025-06-05T15:49:15.616Z"}, {"role": "user", "content": "Python", "timestamp": "2025-06-05T15:49:30.889Z", "contactName": "Drashtu🖤"}, {"role": "assistant", "content": "It appears that <PERSON><PERSON><PERSON> isn't in our candidate database. 📊 However, if you'd like to schedule an interview for <PERSON><PERSON><PERSON> for the Python Developer Internship, I can guide you through the process. Please provide his resume or details, and I'll assist further! 🤝", "timestamp": "2025-06-05T15:49:32.108Z"}, {"role": "user", "content": "Harshil_Resume.pdf", "timestamp": "2025-06-05T15:49:52.509Z", "contactName": "Drashtu🖤"}, {"role": "assistant", "content": "It seems <PERSON><PERSON><PERSON>'s resume wasn't found in our database. 📄 Would you like to share more details about <PERSON><PERSON><PERSON>, such as his skills or experience, so I can assist with scheduling an interview or provide more information about the Python Developer Internship?", "timestamp": "2025-06-05T15:49:55.318Z"}, {"role": "user", "content": "Yes", "timestamp": "2025-06-05T15:50:11.853Z", "contactName": "Drashtu🖤"}, {"role": "assistant", "content": "It seems <PERSON><PERSON><PERSON> isn't in our candidate database. 📊 Would you like to share his resume or details, and I'll assist with scheduling an interview for the Python Developer Internship? 📝 The internship details are:\n- Stipend based on interview performance (no fixed stipend)\n- 1-year program\n- In-office internship\n- ₹15,000 refundable bond agreement", "timestamp": "2025-06-05T15:50:12.852Z"}, {"role": "user", "content": "Schedule interview", "timestamp": "2025-06-05T15:50:32.507Z", "contactName": "Drashtu🖤"}, {"role": "assistant", "content": "To schedule an interview for <PERSON><PERSON><PERSON>, I'll need some details. 📝 Please provide:\n- <PERSON><PERSON><PERSON>'s availability (date & time)\n- His resume or key skills/experience\n- Preferred interview slot (10 AM - 6 PM, 45 minutes)\n\nI'll send a confirmation message once scheduled! 📅 \n\nAlso, would you like to add any specific notes or requirements for the interview?", "timestamp": "2025-06-05T15:50:33.128Z"}, {"role": "user", "content": "Tomorrow 11AM", "timestamp": "2025-06-05T15:51:08.064Z", "contactName": "Drashtu🖤"}, {"role": "assistant", "content": "<function=schedule_interview>{\"candidate_id\": \"<PERSON><PERSON><PERSON>\", \"interview_date\": \"2024-06-07\", \"interview_time\": \"11:00 AM\", \"notes\": \"Python Developer Internship interview\"}", "timestamp": "2025-06-05T15:51:09.545Z"}], "createdAt": "2025-06-05T14:45:23.724Z", "lastActivity": "2025-06-05T15:51:08.064Z"}