{"contactId": "<EMAIL>", "messages": [{"role": "user", "content": "i will Come tomorrow morning after 9 Am morning .", "timestamp": "2025-06-05T13:33:04.584Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "Unfortunately, I don't have the ability to schedule or update interviews. However, I can provide you with our HR contact details so you can directly connect with them. 📲 You can reach out to +91-8487921219 or <EMAIL>. They'll be happy to help you with your interview schedule. 😊", "timestamp": "2025-06-05T13:33:06.210Z"}, {"role": "user", "content": "Ok So Which Time you May available So I will Come Over there that time .", "timestamp": "2025-06-05T13:35:36.143Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "Our HR team will contact you to schedule a suitable time slot. Would you like to know more about our open positions? 🤔 We have opportunities in Python Development, React Native, ERP Support, Manual Testing, and UI/UX Development. 💻📊", "timestamp": "2025-06-05T13:35:38.011Z"}, {"role": "user", "content": "Done …", "timestamp": "2025-06-05T14:15:08.031Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "😊 Our HR team will reach out to finalize the interview details. If you have any questions or need assistance, feel free to contact them directly at +91-8487921219 or <EMAIL>. Have a great day! 👋", "timestamp": "2025-06-05T14:15:08.632Z"}], "createdAt": "2025-06-05T13:33:04.584Z", "lastActivity": "2025-06-05T14:15:08.031Z"}