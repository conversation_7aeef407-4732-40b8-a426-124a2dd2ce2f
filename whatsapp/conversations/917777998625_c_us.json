{"contactId": "<EMAIL>", "messages": [{"role": "user", "content": "Hello sir", "timestamp": "2025-06-14T06:54:32.463Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "Hello! 😊 It's nice to meet you. Is there something I can help you with or would you like to know more about Arihant AI Solutions? 🤔", "timestamp": "2025-06-14T06:54:33.116Z"}, {"role": "user", "content": "Good afternoon", "timestamp": "2025-06-14T06:54:36.050Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "Good afternoon! 😊 How can I assist you today? Do you have any questions or need information about our services?", "timestamp": "2025-06-14T06:54:36.358Z"}, {"role": "user", "content": "Good afternoon sir", "timestamp": "2025-06-14T07:11:47.127Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "Good afternoon! 😊 How are you doing today? Is there something I can help you with or would you like to know more about Arihant AI Solutions? 🤔 \n\nBy the way, we have a few openings for internships and full-time positions. Would you like to know more about them? \n\n(Python Developer Internship, React Native Internship, ERP Support & Manual Tester, UI/UX Developer Internship)", "timestamp": "2025-06-14T07:11:47.773Z"}, {"role": "user", "content": "I hope sir its a good time to speak to you.", "timestamp": "2025-06-14T07:13:19.912Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "It's great to speak with you too! 😊 I'm here to help and provide any information you need. Please feel free to ask me anything, whether it's about our company, services, or job openings. I'm all ears! \n\nBy the way, would you like to schedule an interview for one of our open positions? We have a few slots available this Friday. 📅", "timestamp": "2025-06-14T07:13:20.397Z"}, {"role": "user", "content": "Sir u told me to remind you about scheduling an interview for python Developer internship role on Tuesday", "timestamp": "2025-06-14T07:15:44.684Z", "contactName": "<PERSON><PERSON>"}, {"role": "assistant", "content": "I'm glad you reminded me! 📅 Unfortunately, I don't have any record of our previous conversation about scheduling an interview on Tuesday. Could you please provide more details like the candidate's name, position, and preferred interview time? \n\nAlso, please confirm if the interview is still scheduled for this Tuesday. I'll make sure to follow up and assist you further. 📝 \n\n(As a friendly chatbot, I don't have personal memories, so I rely on your reminders to help me stay on track!)", "timestamp": "2025-06-14T07:15:45.229Z"}], "createdAt": "2025-06-14T06:54:32.463Z", "lastActivity": "2025-06-14T07:15:44.684Z"}