{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 08:54:12"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 08:54:12"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 08:54:12"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"localhost:27017":{"$clusterTime":null,"address":"localhost:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":16245494901,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"errorLabelSet":{},"level":"error","message":"MongoDB connection error: connect ECONNREFUSED 127.0.0.1:27017","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"localhost:27017":{"$clusterTime":null,"address":"localhost:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":16245494901,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"service":"whatsapp-saas","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/connection.js:1096:11)","timestamp":"2025-06-12 08:54:42"}
{"level":"info","message":"Running in demo mode without MongoDB","service":"whatsapp-saas","timestamp":"2025-06-12 09:02:01"}
{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 09:02:01"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 09:02:01"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 09:02:01"}
{"level":"info","message":"Running in demo mode without MongoDB","service":"whatsapp-saas","timestamp":"2025-06-12 09:08:07"}
{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 09:08:07"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 09:08:07"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 09:08:07"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:09:09 +0000] \"GET /api/health HTTP/1.1\" 200 104 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:09:09"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:09:38 +0000] \"GET /api/packages HTTP/1.1\" 200 2188 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:09:38"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:12:31 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:12:31"}
{"level":"info","message":"::ffff:*************** - - [12/Jun/2025:09:12:33 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:12:33"}
{"level":"info","message":"::ffff:*********** - - [12/Jun/2025:09:18:00 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Mobile Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:18:00"}
{"level":"info","message":"::ffff:*********** - - [12/Jun/2025:09:18:14 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:18:14"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:19:05 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:19:05"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:19:05 +0000] \"GET /api/placeholder/32/32 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:19:05"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:19:05 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:19:05"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:20:34 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:20:34"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:20:34 +0000] \"GET /api/placeholder/32/32 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:20:34"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:29:41 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:29:41"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:29:41 +0000] \"GET /api/placeholder/32/32 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:29:41"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:33:38 +0000] \"GET /api/placeholder/32/32 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:33:38"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:33:38 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:33:38"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:53:38 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:53:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:09:55:20 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 09:55:20"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 10:00:00"}
{"level":"info","message":"Sending payment reminders...","service":"whatsapp-saas","timestamp":"2025-06-12 10:00:00"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:10:02 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:10:02"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:16:13 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:16:13"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:16:49 +0000] \"GET /main.430d2114425f1be78f93.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:16:49"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:16:50 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:16:50"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:16:51 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:16:51"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:16:55 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:16:55"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:16:57 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:16:57"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:18:14 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:18:14"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:18:27 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:18:27"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:18:49 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:18:49"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:19:24 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:19:24"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:19:25 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:19:25"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:23:36 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:23:36"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:23:39 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:23:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:23:40 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:23:40"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:23:44 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:23:44"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:24:07 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:24:07"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:33:01 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:33:01"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:33:03 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:33:03"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:34:39 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:34:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:34:39 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:34:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:34:39 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:34:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:37:03 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:37:03"}
{"level":"info","message":"::ffff:************* - - [12/Jun/2025:10:38:47 +0000] \"GET /admin HTTP/1.1\" 404 144 \"-\" \"Mozilla/5.0 zgrab/0.x\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:38:47"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:39:06 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:39:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:39:52 +0000] \"OPTIONS /api/auth/register HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:39:52"}
{"level":"error","message":"Registration error: Operation `users.findOne()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:40:02"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:40:02 +0000] \"POST /api/auth/register HTTP/1.1\" 500 49 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:40:02"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:42:18 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:42:18"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:42:29 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:42:29"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:53:40 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:53:40"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:54:00 +0000] \"OPTIONS /api/auth/register HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:54:00"}
{"level":"error","message":"Registration error: Operation `users.findOne()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:54:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:54:10 +0000] \"POST /api/auth/register HTTP/1.1\" 500 49 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:54:10"}
{"level":"info","message":"Running in demo mode without MongoDB","service":"whatsapp-saas","timestamp":"2025-06-12 10:59:07"}
{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 10:59:07"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 10:59:07"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 10:59:07"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:10:59:58 +0000] \"GET /api/health HTTP/1.1\" 200 101 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 10:59:58"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 11:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 11:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:00:35 +0000] \"GET /api/packages HTTP/1.1\" 200 2188 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:00:35"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:01:01 +0000] \"POST /api/auth/register HTTP/1.1\" 201 563 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:01:01"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:02:06 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://**************:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:02:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:02:07 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"http://**************:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:02:07"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:06:46 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:06:46"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:08:53 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:08:53"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:31 +0000] \"GET /main.427812f0c8b745382670.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:31"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:32 +0000] \"GET /main.427812f0c8b745382670.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:32"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:34 +0000] \"GET /main.427812f0c8b745382670.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:34"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:34 +0000] \"GET /main.427812f0c8b745382670.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:34"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:36 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:36"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:37 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:37"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:38 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:38"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:09:39 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:09:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:10:06 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:10:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:10:32 +0000] \"OPTIONS /api/auth/register HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:10:32"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:10:32 +0000] \"POST /api/auth/register HTTP/1.1\" 201 890 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:10:32"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:10:37 +0000] \"OPTIONS /api/payments/create-order HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:10:37"}
{"kind":"ObjectId","level":"error","message":"Auth middleware error: Cast to ObjectId failed for value \"demo_user_1749726632884\" (type string) at path \"_id\" for model \"User\"","path":"_id","reason":{},"service":"whatsapp-saas","stack":"CastError: Cast to ObjectId failed for value \"demo_user_1749726632884\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:19:22)","stringValue":"\"demo_user_1749726632884\"","timestamp":"2025-06-12 11:10:37","value":"demo_user_1749726632884","valueType":"string"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:10:37 +0000] \"POST /api/payments/create-order HTTP/1.1\" 401 44 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:10:37"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:10:38 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:10:38"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:17:06 +0000] \"OPTIONS /api/auth/register HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:17:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:17:06 +0000] \"POST /api/auth/register HTTP/1.1\" 201 894 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:17:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:17:14 +0000] \"OPTIONS /api/payments/create-order HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:17:14"}
{"kind":"ObjectId","level":"error","message":"Auth middleware error: Cast to ObjectId failed for value \"demo_user_1749727026837\" (type string) at path \"_id\" for model \"User\"","path":"_id","reason":{},"service":"whatsapp-saas","stack":"CastError: Cast to ObjectId failed for value \"demo_user_1749727026837\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:19:22)","stringValue":"\"demo_user_1749727026837\"","timestamp":"2025-06-12 11:17:14","value":"demo_user_1749727026837","valueType":"string"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:17:14 +0000] \"POST /api/payments/create-order HTTP/1.1\" 401 44 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:17:14"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:17:15 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:17:15"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:11:26:32 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 11:26:32"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 12:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 12:00:10"}
{"level":"info","message":"::ffff:************** - - [12/Jun/2025:12:15:47 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.157 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:15:47"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:46:41 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:46:41"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:50:39 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:50:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:50:47 +0000] \"OPTIONS /api/auth/register HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:50:47"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:50:47 +0000] \"POST /api/auth/register HTTP/1.1\" 201 890 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:50:47"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:50:53 +0000] \"OPTIONS /api/payments/create-order HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:50:53"}
{"kind":"ObjectId","level":"error","message":"Auth middleware error: Cast to ObjectId failed for value \"demo_user_1749732647232\" (type string) at path \"_id\" for model \"User\"","path":"_id","reason":{},"service":"whatsapp-saas","stack":"CastError: Cast to ObjectId failed for value \"demo_user_1749732647232\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:19:22)","stringValue":"\"demo_user_1749732647232\"","timestamp":"2025-06-12 12:50:53","value":"demo_user_1749732647232","valueType":"string"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:50:53 +0000] \"POST /api/payments/create-order HTTP/1.1\" 401 44 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:50:53"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:50:54 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:50:54"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:55:16 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:55:16"}
{"level":"info","message":"Running in demo mode without MongoDB","service":"whatsapp-saas","timestamp":"2025-06-12 12:57:01"}
{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 12:57:01"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 12:57:01"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 12:57:01"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:58:39 +0000] \"GET /main.0d1e6dd288e608453948.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:58:39"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:58:40 +0000] \"GET /main.0d1e6dd288e608453948.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:58:40"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:58:40 +0000] \"GET /main.0d1e6dd288e608453948.hot-update.json HTTP/1.1\" 404 180 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:58:40"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:58:41 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:58:41"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:58:42 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:58:42"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:12:58:42 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 12:58:42"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 13:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 13:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:00:52 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:00:52"}
{"level":"info","message":"::ffff:************** - - [12/Jun/2025:13:02:23 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (X11; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:02:23"}
{"level":"info","message":"::ffff:************** - - [12/Jun/2025:13:02:23 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"http://**************:5000/\" \"Mozilla/5.0 (X11; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:02:23"}
{"level":"error","message":"Auth middleware error: jwt malformed","name":"JsonWebTokenError","service":"whatsapp-saas","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (/mnt/whatsapp/whatsapp-saas/node_modules/jsonwebtoken/verify.js:70:17)\n    at authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:18:29)\n    at Layer.handle [as handle_request] (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:328:13)\n    at /mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:346:12)\n    at next (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:280:10)\n    at /mnt/whatsapp/whatsapp-saas/node_modules/express-flash/lib/express-flash.js:31:7\n    at /mnt/whatsapp/whatsapp-saas/node_modules/connect-flash/lib/flash.js:21:5\n    at /mnt/whatsapp/whatsapp-saas/node_modules/express-flash/lib/express-flash.js:22:5","timestamp":"2025-06-12 13:03:13"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:03:13 +0000] \"POST /api/payments/create-order HTTP/1.1\" 401 44 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:03:13"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:04:03 +0000] \"POST /api/auth/register HTTP/1.1\" 201 563 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:04:03"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:04:26 +0000] \"POST /api/payments/create-order HTTP/1.1\" 404 47 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:04:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:04:49 +0000] \"GET /api/packages HTTP/1.1\" 200 2188 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:04:49"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:05:21 +0000] \"POST /api/payments/create-order HTTP/1.1\" 200 310 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:05:21"}
{"level":"info","message":"Running in demo mode without MongoDB","service":"whatsapp-saas","timestamp":"2025-06-12 13:07:45"}
{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 13:07:45"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 13:07:45"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 13:07:45"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:08:40 +0000] \"POST /api/auth/register HTTP/1.1\" 201 566 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:08:40"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:09:00 +0000] \"POST /api/payments/create-order HTTP/1.1\" 200 321 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:09:00"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:13:33:49 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/109.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 13:33:49"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 14:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 14:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 14:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 15:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 15:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:15:54:18 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 15:54:18"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:15:54:18 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 15:54:18"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:15:54:30 +0000] \"OPTIONS /api/auth/register HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 15:54:30"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:15:54:30 +0000] \"POST /api/auth/register HTTP/1.1\" 201 894 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 15:54:30"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:15:54:32 +0000] \"OPTIONS /api/payments/create-order HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 15:54:32"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:15:54:32 +0000] \"POST /api/payments/create-order HTTP/1.1\" 404 47 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 15:54:32"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 16:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 16:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:16:00:35 +0000] \"GET /api/packages HTTP/1.1\" 200 2188 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 16:00:35"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:16:02:00 +0000] \"POST /api/auth/register HTTP/1.1\" 201 566 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 16:02:00"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:16:02:28 +0000] \"POST /api/payments/create-order HTTP/1.1\" 200 321 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 16:02:28"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 17:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 17:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 18:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 18:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 19:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 19:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:19:20:50 +0000] \"OPTIONS /api/payments/create-order HTTP/1.1\" 204 0 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 19:20:50"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:19:20:50 +0000] \"POST /api/payments/create-order HTTP/1.1\" 200 330 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 19:20:50"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:19:54:30 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-12 19:54:30"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 20:00:00"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:20:00:01 +0000] \"POST /api/auth/register HTTP/1.1\" 201 583 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 20:00:01"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 20:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:20:00:29 +0000] \"POST /api/payments/create-order HTTP/1.1\" 200 332 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 20:00:29"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 21:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 21:00:10"}
{"level":"info","message":"::ffff:************** - - [12/Jun/2025:21:54:35 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:134.0) Gecko/20100101 Firefox/134.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 21:54:35"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 22:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 22:00:10"}
{"level":"info","message":"::ffff:************* - - [12/Jun/2025:22:08:06 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:134.0) Gecko/20100101 Firefox/134.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 22:08:06"}
{"level":"info","message":"::ffff:************* - - [12/Jun/2025:22:52:16 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-12 22:52:16"}
{"level":"info","message":"::ffff:************* - - [12/Jun/2025:22:52:16 +0000] \"OPTIONS / HTTP/1.0\" 204 0 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-12 22:52:16"}
{"level":"info","message":"::ffff:************* - - [12/Jun/2025:22:52:17 +0000] \"OPTIONS / HTTP/1.0\" 204 0 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-12 22:52:17"}
{"level":"info","message":"Running in demo mode without MongoDB","service":"whatsapp-saas","timestamp":"2025-06-12 22:54:31"}
{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 22:54:31"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 22:54:31"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 22:54:31"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:22:55:35 +0000] \"GET /api/packages HTTP/1.1\" 200 2743 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 22:55:35"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:22:56:03 +0000] \"POST /api/auth/register HTTP/1.1\" 201 573 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 22:56:03"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:22:56:24 +0000] \"POST /api/payments/create-order HTTP/1.1\" 404 47 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 22:56:24"}
{"level":"info","message":"Running in demo mode without MongoDB","service":"whatsapp-saas","timestamp":"2025-06-12 22:59:04"}
{"level":"info","message":"🚀 WhatsApp SAAS Platform server running on port 5000","service":"whatsapp-saas","timestamp":"2025-06-12 22:59:04"}
{"level":"info","message":"📱 Environment: development","service":"whatsapp-saas","timestamp":"2025-06-12 22:59:04"}
{"level":"info","message":"🌐 Client URL: http://localhost:3000","service":"whatsapp-saas","timestamp":"2025-06-12 22:59:04"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-12 23:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 23:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [12/Jun/2025:23:00:17 +0000] \"POST /api/payments/create-order HTTP/1.1\" 200 322 \"-\" \"curl/7.81.0\"","service":"whatsapp-saas","timestamp":"2025-06-12 23:00:17"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 00:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 00:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 01:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 01:00:10"}
{"level":"info","message":"::ffff:*********** - - [13/Jun/2025:01:17:42 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Windows NT 10.0; rv:91.0) Gecko/20100101 Firefox/91.0\"","service":"whatsapp-saas","timestamp":"2025-06-13 01:17:42"}
{"level":"info","message":"::ffff:*********** - - [13/Jun/2025:01:17:57 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-13 01:17:57"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 02:00:00"}
{"level":"info","message":"Running instance cleanup...","service":"whatsapp-saas","timestamp":"2025-06-13 02:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:02:31:02 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 02:31:02"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:02:35:55 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-13 02:35:55"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:02:35:59 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 02:35:59"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:02:36:04 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 02:36:04"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:02:36:08 +0000] \"GET /.well-known/security.txt HTTP/1.1\" 404 163 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 02:36:08"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:02:36:09 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 02:36:09"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 03:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 03:00:10"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:03:44:28 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0\"","service":"whatsapp-saas","timestamp":"2025-06-13 03:44:28"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:03:44:34 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_0) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.56 Safari/535.11\"","service":"whatsapp-saas","timestamp":"2025-06-13 03:44:34"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:03:44:34 +0000] \"GET /robots.txt HTTP/1.1\" 404 149 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_0) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.56 Safari/535.11\"","service":"whatsapp-saas","timestamp":"2025-06-13 03:44:34"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:03:44:35 +0000] \"GET /sitemap.xml HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_7_0) AppleWebKit/535.11 (KHTML, like Gecko) Chrome/17.0.963.56 Safari/535.11\"","service":"whatsapp-saas","timestamp":"2025-06-13 03:44:35"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 04:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 04:00:10"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:04:22:04 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 04:22:04"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 05:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 05:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 05:00:10"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:05:00:30 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-13 05:00:30"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:05:00:32 +0000] \"GET / HTTP/1.1\" 404 139 \"http://*************:4567\" \"Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-13 05:00:32"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:05:00:33 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"http://**************:5000\" \"Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-13 05:00:33"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:05:35:07 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 05:35:07"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:05:47:15 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 05:47:15"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 06:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 06:00:10"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:06:34:07 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 06:34:07"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 07:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 07:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [13/Jun/2025:07:45:26 +0000] \"GET /api/placeholder/64/64 HTTP/1.1\" 404 160 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-13 07:45:26"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:07:55:54 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-13 07:55:54"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:07:55:59 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 07:55:59"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:07:56:04 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 07:56:04"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:07:56:11 +0000] \"GET /robots.txt HTTP/1.1\" 404 149 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 07:56:11"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:07:56:11 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-13 07:56:11"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 08:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 08:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 11:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 11:00:10"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:11:01:26 +0000] \"GET /v2/ HTTP/1.1\" 404 142 \"-\" \"Mozilla/5.0 zgrab/0.x\"","service":"whatsapp-saas","timestamp":"2025-06-13 11:01:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [13/Jun/2025:11:25:16 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-13 11:25:16"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 12:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 12:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 13:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 13:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 14:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 14:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 14:00:10"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:14:48:28 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 14:48:28"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 15:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 15:00:10"}
{"level":"info","message":"::ffff:************ - - [13/Jun/2025:15:17:12 +0000] \"GET http://************:80/pscan HTTP/1.1\" 404 144 \"-\" \"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-13 15:17:12"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:15:23:24 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 15:23:24"}
{"level":"info","message":"::ffff:************** - - [13/Jun/2025:15:43:37 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 15:43:37"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 16:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 16:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 17:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 17:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 18:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 18:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 19:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 19:00:10"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:19:50:53 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 19:50:53"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 20:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 20:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 21:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 21:00:10"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:21:14:36 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-13 21:14:36"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:21:14:37 +0000] \"OPTIONS / HTTP/1.0\" 204 0 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-13 21:14:37"}
{"level":"info","message":"::ffff:*************** - - [13/Jun/2025:21:14:38 +0000] \"OPTIONS / HTTP/1.0\" 204 0 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-13 21:14:38"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 22:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 22:00:10"}
{"level":"info","message":"::ffff:************* - - [13/Jun/2025:22:53:05 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-13 22:53:05"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-13 23:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 23:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 00:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 00:00:10"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:00:17:47 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-14 00:17:47"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 01:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 01:00:10"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:01:28:13 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-14 01:28:13"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 02:00:00"}
{"level":"info","message":"Running instance cleanup...","service":"whatsapp-saas","timestamp":"2025-06-14 02:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 03:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 03:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 04:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 04:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 05:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 05:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 05:00:10"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:05:34:53 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-14 05:34:53"}
{"level":"info","message":"::ffff:************ - - [14/Jun/2025:05:46:07 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0\"","service":"whatsapp-saas","timestamp":"2025-06-14 05:46:07"}
{"level":"info","message":"::ffff:************ - - [14/Jun/2025:05:46:07 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Go-http-client/1.1\"","service":"whatsapp-saas","timestamp":"2025-06-14 05:46:07"}
{"level":"info","message":"::ffff:************ - - [14/Jun/2025:05:46:08 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Go-http-client/1.1\"","service":"whatsapp-saas","timestamp":"2025-06-14 05:46:08"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 06:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 06:00:10"}
{"level":"info","message":"::ffff:********** - - [14/Jun/2025:06:43:25 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/23.1.2.987 Yowser/2.5 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-14 06:43:25"}
{"level":"info","message":"::ffff:********** - - [14/Jun/2025:06:43:38 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:126.0) Gecko/20100101 Firefox/126.0\"","service":"whatsapp-saas","timestamp":"2025-06-14 06:43:38"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 07:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 07:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:07:17:55 +0000] \"GET /.well-known/security.txt HTTP/1.1\" 404 163 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 07:17:55"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:07:19:37 +0000] \"OPTIONS / HTTP/1.1\" 204 0 \"-\" \"WanScannerBot/1.0\"","service":"whatsapp-saas","timestamp":"2025-06-14 07:19:37"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 08:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 08:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:08:26:34 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 YaBrowser/******** Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-14 08:26:34"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 09:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 09:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 09:00:10"}
{"level":"info","message":"Sending payment reminders...","service":"whatsapp-saas","timestamp":"2025-06-14 10:00:00"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 10:00:00"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 10:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 10:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 10:00:10"}
{"level":"info","message":"::ffff:*************** - - [14/Jun/2025:10:30:30 +0000] \"GET / HTTP/1.1\" 404 139 \"https://google.com\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-14 10:30:30"}
{"level":"info","message":"::ffff:*************** - - [14/Jun/2025:10:30:30 +0000] \"GET /.env HTTP/1.1\" 404 143 \"https://google.com\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-14 10:30:30"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 11:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 11:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 12:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 12:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 13:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 13:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:13:03:51 +0000] \"GET /robots.txt HTTP/1.1\" 404 149 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 13:03:51"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:13:07:21 +0000] \"GET /sse HTTP/1.1\" 404 142 \"-\" \"curl/8.5.0\"","service":"whatsapp-saas","timestamp":"2025-06-14 13:07:21"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 14:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 14:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 14:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 15:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 15:00:10"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:15:19:14 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 15:19:14"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:15:20:03 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 15:20:03"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:15:20:33 +0000] \"GET /wiki HTTP/1.1\" 404 143 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 15:20:33"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:15:20:55 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 15:20:55"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:15:41:31 +0000] \"GET /sse HTTP/1.1\" 404 142 \"-\" \"curl/8.5.0\"","service":"whatsapp-saas","timestamp":"2025-06-14 15:41:31"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 16:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 16:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:16:13:45 +0000] \"GET /security.txt HTTP/1.1\" 404 151 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 16:13:45"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 17:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 17:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 18:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 18:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 19:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 19:00:10"}
{"level":"info","message":"::ffff:************** - - [14/Jun/2025:19:00:47 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-14 19:00:47"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:19:15:17 +0000] \"GET /wiki HTTP/1.1\" 404 143 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 19:15:17"}
{"level":"info","message":"::ffff:************* - - [14/Jun/2025:19:43:35 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-14 19:43:35"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 20:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 20:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 21:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 21:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [14/Jun/2025:21:51:53 +0000] \"GET /security.txt HTTP/1.1\" 404 151 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-14 21:51:53"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 22:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 22:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-14 23:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 23:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 00:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 00:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 01:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 01:00:10"}
{"level":"info","message":"::ffff:************** - - [15/Jun/2025:01:22:59 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-15 01:22:59"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 02:00:00"}
{"level":"info","message":"Running instance cleanup...","service":"whatsapp-saas","timestamp":"2025-06-15 02:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 03:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 03:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 04:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 04:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 05:00:01"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 05:00:11"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 05:00:11"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 06:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 06:00:10"}
{"level":"info","message":"::ffff:*********** - - [15/Jun/2025:06:39:19 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:134.0) Gecko/20100101 Firefox/134.0\"","service":"whatsapp-saas","timestamp":"2025-06-15 06:39:19"}
{"level":"info","message":"::ffff:************** - - [15/Jun/2025:06:42:16 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-15 06:42:16"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:06:48:18 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:134.0) Gecko/20100101 Firefox/134.0\"","service":"whatsapp-saas","timestamp":"2025-06-15 06:48:18"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 07:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 07:00:10"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:07:03:59 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-15 07:03:59"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:07:04:00 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/74.0.3729.169 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-15 07:04:00"}
{"level":"info","message":"::ffff:127.0.0.1 - - [15/Jun/2025:07:04:03 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.111 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-15 07:04:03"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 08:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 08:00:10"}
{"level":"info","message":"::ffff:************** - - [15/Jun/2025:08:27:22 +0000] \"GET /sse HTTP/1.1\" 404 142 \"-\" \"curl/8.5.0\"","service":"whatsapp-saas","timestamp":"2025-06-15 08:27:22"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:08:43:19 +0000] \"GET /v2/_catalog HTTP/1.1\" 404 150 \"-\" \"Go-http-client/1.1\"","service":"whatsapp-saas","timestamp":"2025-06-15 08:43:19"}
{"level":"info","message":"::ffff:************** - - [15/Jun/2025:08:56:46 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Windows NT 6.2;en-US) AppleWebKit/537.32.36 (KHTML, live Gecko) Chrome/54.0.3005.94 Safari/537.32\"","service":"whatsapp-saas","timestamp":"2025-06-15 08:56:46"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 09:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 09:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 09:00:10"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:09:26:21 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (X11; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0\"","service":"whatsapp-saas","timestamp":"2025-06-15 09:26:21"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:09:26:21 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"http://**************:5000/\" \"Mozilla/5.0 (X11; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0\"","service":"whatsapp-saas","timestamp":"2025-06-15 09:26:21"}
{"level":"info","message":"Sending payment reminders...","service":"whatsapp-saas","timestamp":"2025-06-15 10:00:00"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 10:00:00"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 10:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 10:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 10:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 11:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 11:00:10"}
{"level":"info","message":"::ffff:************** - - [15/Jun/2025:11:17:09 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (X11; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0\"","service":"whatsapp-saas","timestamp":"2025-06-15 11:17:09"}
{"level":"info","message":"::ffff:************** - - [15/Jun/2025:11:17:09 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"http://**************:5000/\" \"Mozilla/5.0 (X11; Linux x86_64; rv:136.0) Gecko/20100101 Firefox/136.0\"","service":"whatsapp-saas","timestamp":"2025-06-15 11:17:09"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 12:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 12:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 13:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 13:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [15/Jun/2025:13:16:06 +0000] \"GET /pdown HTTP/1.1\" 404 144 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-15 13:16:06"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:13:21:41 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-15 13:21:41"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:13:21:41 +0000] \"OPTIONS / HTTP/1.0\" 204 0 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-15 13:21:41"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:13:21:42 +0000] \"OPTIONS / HTTP/1.0\" 204 0 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-15 13:21:42"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:13:35:53 +0000] \"GET /v2/_catalog HTTP/1.1\" 404 150 \"-\" \"Go-http-client/1.1\"","service":"whatsapp-saas","timestamp":"2025-06-15 13:35:53"}
{"level":"info","message":"::ffff:************ - - [15/Jun/2025:13:36:23 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"fasthttp\"","service":"whatsapp-saas","timestamp":"2025-06-15 13:36:23"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 14:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 14:00:11"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 14:00:11"}
{"level":"info","message":"::ffff:127.0.0.1 - - [15/Jun/2025:14:48:48 +0000] \"GET /wiki HTTP/1.1\" 404 143 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-15 14:48:48"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 15:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 15:00:10"}
{"level":"info","message":"::ffff:************ - - [15/Jun/2025:15:55:40 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-15 15:55:40"}
{"level":"info","message":"::ffff:************ - - [15/Jun/2025:15:55:52 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-15 15:55:52"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 16:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 16:00:10"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:16:39:39 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-15 16:39:39"}
{"level":"info","message":"::ffff:************* - - [15/Jun/2025:16:39:40 +0000] \"OPTIONS / HTTP/1.0\" 204 0 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-15 16:39:40"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 17:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 17:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 18:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 18:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 19:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 19:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 20:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 20:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 21:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 21:00:10"}
{"level":"info","message":"::ffff:************** - - [15/Jun/2025:21:38:31 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-15 21:38:31"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 22:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 22:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-15 23:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 23:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [15/Jun/2025:23:11:44 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-15 23:11:44"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 00:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 00:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 01:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 01:00:10"}
{"level":"info","message":"::ffff:************ - - [16/Jun/2025:01:11:29 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-16 01:11:29"}
{"level":"info","message":"::ffff:************** - - [16/Jun/2025:01:28:47 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-16 01:28:47"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jun/2025:01:40:16 +0000] \"GET /.well-known/security.txt HTTP/1.1\" 404 163 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-16 01:40:16"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 02:00:00"}
{"level":"info","message":"Running instance cleanup...","service":"whatsapp-saas","timestamp":"2025-06-16 02:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"info","message":"::ffff:************* - - [16/Jun/2025:02:33:23 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-16 02:33:23"}
{"level":"info","message":"::ffff:************* - - [16/Jun/2025:02:34:03 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-16 02:34:03"}
{"level":"info","message":"::ffff:************* - - [16/Jun/2025:02:34:42 +0000] \"GET /robots.txt HTTP/1.1\" 404 149 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-16 02:34:42"}
{"level":"info","message":"::ffff:************* - - [16/Jun/2025:02:34:52 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-16 02:34:52"}
{"level":"info","message":"::ffff:*************6 - - [16/Jun/2025:02:35:51 +0000] \"GET / HTTP/1.0\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-16 02:35:51"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jun/2025:02:38:09 +0000] \"GET /robots.txt HTTP/1.1\" 404 149 \"-\" \"Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)\"","service":"whatsapp-saas","timestamp":"2025-06-16 02:38:09"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 03:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 03:00:10"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jun/2025:03:16:59 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.57\"","service":"whatsapp-saas","timestamp":"2025-06-16 03:16:59"}
{"level":"info","message":"::ffff:************** - - [16/Jun/2025:03:42:07 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Expanse, a Palo Alto Networks company, searches across the global IPv4 space multiple times per day to identify customers&#39; presences on the Internet. If you would like to be excluded from our scans, please send IP addresses/domains to: <EMAIL>\"","service":"whatsapp-saas","timestamp":"2025-06-16 03:42:07"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 04:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 04:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 05:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 05:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 05:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 06:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 06:00:10"}
{"level":"info","message":"::ffff:*********** - - [16/Jun/2025:06:32:22 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-16 06:32:22"}
{"level":"info","message":"::ffff:*********** - - [16/Jun/2025:06:36:00 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Chrome/********* Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-16 06:36:00"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 07:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 07:00:10"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 08:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 08:00:10"}
{"level":"info","message":"::ffff:************* - - [16/Jun/2025:08:15:28 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (iPad; CPU OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6,2 Mobile/15E148 Safari/604.1\"","service":"whatsapp-saas","timestamp":"2025-06-16 08:15:28"}
{"level":"info","message":"::ffff:************* - - [16/Jun/2025:08:15:41 +0000] \"GET /favicon.ico HTTP/1.1\" 404 150 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_3) AppleWebKit/537.75.14 (KHTML, like Gecko) Version/7.0.3 Safari/7046A194A\"","service":"whatsapp-saas","timestamp":"2025-06-16 08:15:41"}
{"level":"info","message":"Running subscription renewal check...","service":"whatsapp-saas","timestamp":"2025-06-16 09:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 09:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 09:00:10"}
{"level":"info","message":"::ffff:************** - - [16/Jun/2025:09:23:37 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"-\"","service":"whatsapp-saas","timestamp":"2025-06-16 09:23:37"}
{"level":"info","message":"::ffff:************** - - [16/Jun/2025:09:24:29 +0000] \"GET / HTTP/1.1\" 404 139 \"-\" \"Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.112 Safari/537.36\"","service":"whatsapp-saas","timestamp":"2025-06-16 09:24:29"}
{"level":"info","message":"Sending payment reminders...","service":"whatsapp-saas","timestamp":"2025-06-16 10:00:00"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 10:00:01"}
