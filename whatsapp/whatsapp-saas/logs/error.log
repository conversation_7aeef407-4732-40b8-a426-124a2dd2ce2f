{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"localhost:27017":{"$clusterTime":null,"address":"localhost:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":16245494901,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"errorLabelSet":{},"level":"error","message":"MongoDB connection error: connect ECONNREFUSED 127.0.0.1:27017","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"localhost:27017":{"$clusterTime":null,"address":"localhost:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":16245494901,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"service":"whatsapp-saas","stack":"MongooseServerSelectionError: connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/connection.js:1096:11)","timestamp":"2025-06-12 08:54:42"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:00:10"}
{"level":"error","message":"Registration error: Operation `users.findOne()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:40:02"}
{"level":"error","message":"Registration error: Operation `users.findOne()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 10:54:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 11:00:10"}
{"kind":"ObjectId","level":"error","message":"Auth middleware error: Cast to ObjectId failed for value \"demo_user_1749726632884\" (type string) at path \"_id\" for model \"User\"","path":"_id","reason":{},"service":"whatsapp-saas","stack":"CastError: Cast to ObjectId failed for value \"demo_user_1749726632884\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:19:22)","stringValue":"\"demo_user_1749726632884\"","timestamp":"2025-06-12 11:10:37","value":"demo_user_1749726632884","valueType":"string"}
{"kind":"ObjectId","level":"error","message":"Auth middleware error: Cast to ObjectId failed for value \"demo_user_1749727026837\" (type string) at path \"_id\" for model \"User\"","path":"_id","reason":{},"service":"whatsapp-saas","stack":"CastError: Cast to ObjectId failed for value \"demo_user_1749727026837\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:19:22)","stringValue":"\"demo_user_1749727026837\"","timestamp":"2025-06-12 11:17:14","value":"demo_user_1749727026837","valueType":"string"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 12:00:10"}
{"kind":"ObjectId","level":"error","message":"Auth middleware error: Cast to ObjectId failed for value \"demo_user_1749732647232\" (type string) at path \"_id\" for model \"User\"","path":"_id","reason":{},"service":"whatsapp-saas","stack":"CastError: Cast to ObjectId failed for value \"demo_user_1749732647232\" (type string) at path \"_id\" for model \"User\"\n    at SchemaObjectId.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schema/objectId.js:251:11)\n    at SchemaType.applySetters (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1255:12)\n    at SchemaType.castForQuery (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/schemaType.js:1673:17)\n    at cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/cast.js:390:32)\n    at Query.cast (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:5055:12)\n    at Query._castConditions (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2351:10)\n    at model.Query._findOne (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:2674:8)\n    at model.Query.exec (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:19:22)","stringValue":"\"demo_user_1749732647232\"","timestamp":"2025-06-12 12:50:53","value":"demo_user_1749732647232","valueType":"string"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 13:00:10"}
{"level":"error","message":"Auth middleware error: jwt malformed","name":"JsonWebTokenError","service":"whatsapp-saas","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (/mnt/whatsapp/whatsapp-saas/node_modules/jsonwebtoken/verify.js:70:17)\n    at authMiddleware (/mnt/whatsapp/whatsapp-saas/middleware/auth.js:18:29)\n    at Layer.handle [as handle_request] (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:328:13)\n    at /mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:346:12)\n    at next (/mnt/whatsapp/whatsapp-saas/node_modules/express/lib/router/index.js:280:10)\n    at /mnt/whatsapp/whatsapp-saas/node_modules/express-flash/lib/express-flash.js:31:7\n    at /mnt/whatsapp/whatsapp-saas/node_modules/connect-flash/lib/flash.js:21:5\n    at /mnt/whatsapp/whatsapp-saas/node_modules/express-flash/lib/express-flash.js:22:5","timestamp":"2025-06-12 13:03:13"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 14:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 14:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 15:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 16:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 17:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 18:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 19:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 20:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 21:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 22:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-12 23:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 00:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 01:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 02:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 03:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 04:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 05:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 05:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 06:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 07:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 08:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 11:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 12:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 13:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 14:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 14:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 15:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 16:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 17:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 18:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 19:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 20:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 21:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 22:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-13 23:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 00:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 01:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 02:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 03:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 04:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 05:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 05:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 06:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 07:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 08:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 09:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 09:00:10"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 10:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 10:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 10:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 11:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 12:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 13:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 14:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 14:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 15:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 16:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 17:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 18:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 19:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 20:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 21:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 22:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-14 23:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 00:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 01:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 02:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 03:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 04:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 05:00:11"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 05:00:11"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 06:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 07:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 08:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 09:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 09:00:10"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 10:00:00"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 10:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 10:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 11:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 11:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 12:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 12:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 13:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 13:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 14:00:11"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 14:00:11"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 15:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 15:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 16:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 16:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 17:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 17:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 18:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 18:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 19:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 19:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 20:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 20:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 21:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 21:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 22:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 22:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 23:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-15 23:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 00:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 00:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 01:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 01:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"error","message":"Error cleaning up instances: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"error","message":"Instance cleanup failed: Operation `whatsappinstances.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `whatsappinstances.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 02:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 03:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 03:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 04:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 04:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 05:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 05:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 06:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 06:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 07:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 07:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 08:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 08:00:10"}
{"level":"error","message":"Error checking renewals: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 09:00:10"}
{"level":"error","message":"Subscription renewal check failed: Operation `subscriptions.find()` buffering timed out after 10000ms","service":"whatsapp-saas","stack":"MongooseError: Operation `subscriptions.find()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 09:00:10"}
{"level":"error","message":"Payment reminder sending failed: NotificationService.sendPaymentReminders is not a function","service":"whatsapp-saas","stack":"TypeError: NotificationService.sendPaymentReminders is not a function\n    at Task._execution (/mnt/whatsapp/whatsapp-saas/server.js:198:35)\n    at Task.execute (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/task.js:17:25)\n    at ScheduledTask.now (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:38:33)\n    at Scheduler.<anonymous> (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduled-task.js:25:18)\n    at Scheduler.emit (node:events:519:28)\n    at Timeout.matchTime [as _onTimeout] (/mnt/whatsapp/whatsapp-saas/node_modules/node-cron/src/scheduler.js:30:26)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","timestamp":"2025-06-16 10:00:01"}
