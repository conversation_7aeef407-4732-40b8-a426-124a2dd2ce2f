#!/usr/bin/env python3
"""
Simple password reset for Odoo users
"""

import xmlrpc.client
import sys

# Configuration
ODOO_URL = "http://localhost:8069"
ODOO_DB = "arihantai.com"

def test_authentication(username, password):
    """Test authentication with given credentials"""
    try:
        common = xmlrpc.client.ServerProxy(f'{ODOO_URL}/xmlrpc/2/common')
        uid = common.authenticate(ODOO_DB, username, password, {})
        if uid:
            print(f"✅ Authentication successful for {username}! User ID: {uid}")
            return uid
        else:
            print(f"❌ Authentication failed for {username}")
            return None
    except Exception as e:
        print(f"❌ Authentication error for {username}: {e}")
        return None

def main():
    """Test authentication with known users"""
    print("🔍 Testing authentication with known users...")
    
    # List of users from database
    users = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    # Common passwords to try
    passwords = ["admin", "password", "123456", "arihant123", "test123", ""]
    
    successful_auth = None
    
    for username in users:
        print(f"\n🔍 Testing user: {username}")
        for password in passwords:
            uid = test_authentication(username, password)
            if uid:
                successful_auth = (username, password, uid)
                print(f"✅ Found working credentials!")
                print(f"   Username: {username}")
                print(f"   Password: {'[empty]' if not password else '[hidden]'}")
                break
        if successful_auth:
            break
    
    if successful_auth:
        print(f"\n🎉 Success! Use these credentials for integration:")
        print(f"Username: {successful_auth[0]}")
        print(f"Password: {successful_auth[1] if successful_auth[1] else '[empty]'}")
        
        # Update the integration test script
        print(f"\n📝 Updating integration test script...")
        try:
            with open('/mnt/whatsapp/test_integration.py', 'r') as f:
                content = f.read()
            
            # Replace the username and password
            content = content.replace(
                'ODOO_USERNAME = "<EMAIL>"',
                f'ODOO_USERNAME = "{successful_auth[0]}"'
            )
            content = content.replace(
                'ODOO_PASSWORD = "admin"',
                f'ODOO_PASSWORD = "{successful_auth[1]}"'
            )
            
            with open('/mnt/whatsapp/test_integration.py', 'w') as f:
                f.write(content)
            
            print("✅ Integration test script updated!")
            
        except Exception as e:
            print(f"⚠️  Could not update integration test script: {e}")
        
        return 0
    else:
        print("\n❌ No working credentials found")
        print("You may need to reset passwords manually in Odoo")
        return 1

if __name__ == "__main__":
    sys.exit(main())
