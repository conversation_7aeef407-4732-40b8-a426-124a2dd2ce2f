#!/usr/bin/env python3
"""
Test script for WhatsApp webhook functionality
"""

import requests
import json
import time

# Test webhook URL
WEBHOOK_URL = "https://arihantai.com/whatsapp/webhook/8487921219"

def test_webhook(test_name, payload):
    """Test webhook with given payload"""
    print(f"\n🧪 Testing: {test_name}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            WEBHOOK_URL,
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS")
        else:
            print("❌ FAILED")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    time.sleep(1)  # Small delay between tests

def main():
    """Run all webhook tests"""
    print("🚀 Starting WhatsApp Webhook Tests")
    
    # Test 1: Simple text message
    test_webhook("Text Message", {
        "type": "message",
        "instance_id": "8487921219",
        "message": {
            "id": "test_text_001",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "Test text message",
            "type": "text",
            "timestamp": int(time.time()),
            "fromMe": False
        }
    })
    
    # Test 2: Outgoing message (bot message)
    test_webhook("Bot Message (Outgoing)", {
        "type": "message",
        "instance_id": "8487921219",
        "message": {
            "id": "test_bot_001",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "This is a bot response",
            "type": "text",
            "timestamp": int(time.time()),
            "fromMe": True
        }
    })
    
    # Test 3: Image message
    test_webhook("Image Message", {
        "type": "message",
        "instance_id": "8487921219",
        "message": {
            "id": "test_image_001",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "Check this image!",
            "type": "image",
            "timestamp": int(time.time()),
            "fromMe": False,
            "hasMedia": True,
            "media": {
                "filename": "test_image.jpg",
                "mimetype": "image/jpeg"
            }
        }
    })
    
    # Test 4: Video message
    test_webhook("Video Message", {
        "type": "message",
        "instance_id": "8487921219",
        "message": {
            "id": "test_video_001",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "Video message",
            "type": "video",
            "timestamp": int(time.time()),
            "fromMe": False,
            "hasMedia": True,
            "media": {
                "filename": "test_video.mp4",
                "mimetype": "video/mp4",
                "duration": 30
            }
        }
    })
    
    # Test 5: Audio message
    test_webhook("Audio Message", {
        "type": "message",
        "instance_id": "8487921219",
        "message": {
            "id": "test_audio_001",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "",
            "type": "audio",
            "timestamp": int(time.time()),
            "fromMe": False,
            "hasMedia": True,
            "media": {
                "filename": "test_audio.mp3",
                "mimetype": "audio/mpeg",
                "duration": 15
            }
        }
    })
    
    # Test 6: Document message
    test_webhook("Document Message", {
        "type": "message",
        "instance_id": "8487921219",
        "message": {
            "id": "test_doc_001",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "Important document",
            "type": "document",
            "timestamp": int(time.time()),
            "fromMe": False,
            "hasMedia": True,
            "media": {
                "filename": "document.pdf",
                "mimetype": "application/pdf"
            }
        }
    })
    
    print("\n🎯 All webhook tests completed!")
    print("Check Odoo WhatsApp → Messages to see if all message types were created correctly.")

if __name__ == "__main__":
    main()
