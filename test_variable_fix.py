#!/usr/bin/env python3
"""
Test script to verify the has_media variable fix
"""

import requests
import json

def test_webhook_simple():
    """Test webhook with simple message to verify no variable errors"""
    
    webhook_url = "https://arihantai.com/whatsapp/webhook/8487921219"
    
    # Simple text message test
    payload = {
        "type": "message",
        "instance_id": "8487921219",
        "message": {
            "id": "test_variable_fix_001",
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "body": "Testing variable fix - this should work without errors",
            "type": "text",
            "timestamp": 1750330500,
            "fromMe": False
        }
    }
    
    print("🧪 Testing webhook after variable fix...")
    print(f"URL: {webhook_url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            webhook_url,
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=10
        )
        
        print(f"\n📊 Results:")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Webhook processed without variable errors!")
            return True
        else:
            print("❌ FAILED: Unexpected status code")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_webhook_simple()
    if success:
        print("\n🎉 Variable fix appears to be working!")
        print("You can now test image sending in Odoo without the 'has_media referenced before assignment' error.")
    else:
        print("\n⚠️  There may still be issues. Check Odoo logs for details.")
